﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaInstallationDay
    {
        public MediaInstallationDay()
        {
            BurstInstallationDays = new HashSet<BurstInstallationDay>();
        }

        public int InstallationDayId { get; set; }
        public string MediaInstallationDayName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public bool Dormant { get; set; }

        public virtual ICollection<BurstInstallationDay> BurstInstallationDays { get; set; }
    }
}
