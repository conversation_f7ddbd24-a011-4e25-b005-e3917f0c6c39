﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Client
    {
        public Client()
        {
            ClientAccountManagers = new HashSet<ClientAccountManager>();
            ClientBrands = new HashSet<ClientBrand>();
            ContractAgencies = new HashSet<Contract>();
            ContractClients = new HashSet<Contract>();
        }

        public int ClientId { get; set; }
        public int ClassificationId { get; set; }
        public int TermsId { get; set; }
        public string ClientName { get; set; }
        public string ClientAbbreviation { get; set; }
        public string AccountNumber { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public int CityId { get; set; }
        public string PostalCode { get; set; }
        public bool Dormant { get; set; }
        public string Vatnumber { get; set; }
        public string Notes { get; set; }
        public bool Agency { get; set; }
        public bool ApprovedByFinance { get; set; }
        public bool Retailer { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual City City { get; set; }
        public virtual Classification Classification { get; set; }
        public virtual Term Terms { get; set; }
        public virtual ICollection<ClientAccountManager> ClientAccountManagers { get; set; }
        public virtual ICollection<ClientBrand> ClientBrands { get; set; }
        public virtual ICollection<Contract> ContractAgencies { get; set; }
        public virtual ICollection<Contract> ContractClients { get; set; }
    }
}
