﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class DistributionCheckDetail
    {
        public Guid CheckId { get; set; }
        public Guid ProductId { get; set; }
        public Guid PackSizeId { get; set; }
        public int StoreId { get; set; }
        public bool InStock { get; set; }
        public byte CallNumber { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual DistributionCheck Check { get; set; }
        public virtual PackSize PackSize { get; set; }
        public virtual Product Product { get; set; }
        public virtual Store Store { get; set; }
    }
}
