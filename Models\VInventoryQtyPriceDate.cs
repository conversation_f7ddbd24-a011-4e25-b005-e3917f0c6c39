﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VInventoryQtyPriceDate
    {
        public int ItemQtyId { get; set; }
        public int ItemQtyPriceId { get; set; }
        public DateTime From { get; set; }
        public DateTime? To { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SellPrice { get; set; }
    }
}
