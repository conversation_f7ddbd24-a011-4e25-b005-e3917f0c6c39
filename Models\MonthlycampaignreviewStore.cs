﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MonthlycampaignreviewStore
    {
        public string Period { get; set; }
        public string AssignedAm { get; set; }
        public string CurrentAm { get; set; }
        public string ContractAm { get; set; }
        public string Client { get; set; }
        public string Brand { get; set; }
        public string MediaService { get; set; }
        public string Contract { get; set; }
        public string Chain { get; set; }
        public string ChainType { get; set; }
        public string Category { get; set; }
        public string CategoryType { get; set; }
        public decimal? CurrentForecastRevenue { get; set; }
        public decimal? PriorForecastRevenue { get; set; }
        public decimal? PriorActualRevenue { get; set; }
        public bool? AgencyComm { get; set; }
        public int? Countedcorps { get; set; }
        public int? CountedFranchises { get; set; }
        public int? TotalStores { get; set; }
        public decimal? CurrentForecastRevenueCorps { get; set; }
        public decimal? CurrentForecastRevenueFranchises { get; set; }
        public decimal? PriorForecastRevenueCorps { get; set; }
        public decimal? PriorForecastRevenueFranchises { get; set; }
        public decimal? PriorActualRevenueCorps { get; set; }
        public decimal? PriorActualRevenueFranchises { get; set; }
    }
}
