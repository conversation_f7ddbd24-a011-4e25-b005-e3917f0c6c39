﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VClientsImPermittedToSee
    {
        public int ClientId { get; set; }
        public int ClassificationId { get; set; }
        public int TermsId { get; set; }
        public string ClientName { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public int CityId { get; set; }
        public string PostalCode { get; set; }
        public bool Dormant { get; set; }
        public string Notes { get; set; }
        public bool Agency { get; set; }
        public string ClassificationName { get; set; }
        public string TermsName { get; set; }
        public string CityName { get; set; }
        public string BrandList { get; set; }
        public bool ApprovedByFinance { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string AccountManagerName { get; set; }
        public bool LinkedContracts { get; set; }
        public string ClientAbbreviation { get; set; }
        public bool Retailer { get; set; }
    }
}
