﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class PackSize
    {
        public PackSize()
        {
            BemsTasks = new HashSet<BemsTask>();
            ContractProducts = new HashSet<ContractProduct>();
            DistributionCheckDetails = new HashSet<DistributionCheckDetail>();
            PurchaseDetails = new HashSet<PurchaseDetail>();
            SaleDetails = new HashSet<SaleDetail>();
            UnitsPerShrinks = new HashSet<UnitsPerShrink>();
        }

        public Guid PackSizeId { get; set; }
        public string PackSizeName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<BemsTask> BemsTasks { get; set; }
        public virtual ICollection<ContractProduct> ContractProducts { get; set; }
        public virtual ICollection<DistributionCheckDetail> DistributionCheckDetails { get; set; }
        public virtual ICollection<PurchaseDetail> PurchaseDetails { get; set; }
        public virtual ICollection<SaleDetail> SaleDetails { get; set; }
        public virtual ICollection<UnitsPerShrink> UnitsPerShrinks { get; set; }
    }
}
