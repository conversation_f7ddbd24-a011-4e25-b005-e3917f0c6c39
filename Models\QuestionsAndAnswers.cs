﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class QuestionsAndAnswers
    {

        [Key]
        [Column("QuestionAnswerId")]
        public int QuestionAnswerId { get; set; }

        public int QuestionId { get; set; }

        [Foreign<PERSON><PERSON>(nameof(QuestionId))]
        public virtual Questions Questions { get; set; }

        public int AnswerId { get; set; }

        [ForeignKey(nameof(AnswerId))]
        public virtual Answers Answers { get; set; }

        // public virtual List<InventoryStockTakeDetails> InventoryStockTakeDetails { get; set; }
    }
}
