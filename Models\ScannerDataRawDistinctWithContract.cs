﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ScannerDataRawDistinctWithContract
    {
        public string Barcode { get; set; }
        public string Variant { get; set; }
        public string Region { get; set; }
        public string Storeid { get; set; }
        public string Brand { get; set; }
        public string Storename { get; set; }
        public string Category { get; set; }
        public string Department { get; set; }
        public string Subcategory { get; set; }
        public string Branddescription { get; set; }
        public string Units { get; set; }
        public string Sales { get; set; }
        public DateTime? Enddate { get; set; }
        public double? UnitsSold { get; set; }
        public string ContractNumber { get; set; }
        public long? RowNumber { get; set; }
        public DateTime? PriorYearStartDate { get; set; }
        public DateTime? PriorYearEndDate { get; set; }
        public DateTime? PriorPeriodStartDate { get; set; }
        public DateTime? PriorPeriodEndDate { get; set; }
        public DateTime? PostPeriodStartDate { get; set; }
        public DateTime? PostPeriodEndDate { get; set; }
        public int NovaStoreId { get; set; }
    }
}
