﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Policy
    {
        public int PolicyId { get; set; }
        public string PolicyName { get; set; }
        public int ChainId { get; set; }
        public int RegionId { get; set; }
        public int StoreId { get; set; }
        public int CategoryId { get; set; }
        public int MediaGroupId { get; set; }
        public DateTime EffectiveDate { get; set; }
        public bool? Homesite { get; set; }
        public int Limit { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual MediaGroup MediaGroup { get; set; }
    }
}
