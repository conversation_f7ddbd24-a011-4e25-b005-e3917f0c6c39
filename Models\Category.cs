﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Category
    {
        public Category()
        {
            MasterItems = new HashSet<MasterItem>();
        }

        public int Id { get; set; }
        public string CategoryName { get; set; }
        public bool? IsActive { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string DeletedBy { get; set; }
        public DateTime? DeletionDate { get; set; }

        public virtual ICollection<MasterItem> MasterItems { get; set; }
    }
}
