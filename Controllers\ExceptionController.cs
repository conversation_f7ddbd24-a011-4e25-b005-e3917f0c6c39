﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System.IO;


namespace PhoenixAPI.Controllers
{

    public class ExceptionController : ControllerBase
    {

        private static readonly string ErrorLogPath = @"\\192.168.0.16\API Resources\Error Logs";

        public ExceptionController()
        {
        }


       
        public async Task HandleException(Exception exception)
        {
            //lets create the folder first.
            string errorDirectory = ErrorLogPath + "\\" + System.DateTime.Now.ToShortDateString().Replace("/", "-") + "\\";
            System.IO.Directory.CreateDirectory(errorDirectory);

            //now create the file, we want to split them up by hour
            string fileName = errorDirectory + "\\error-" + System.DateTime.Now.Hour.ToString() + ".txt";
           
            if(System.IO.File.Exists(fileName))
            {
                using (StreamWriter sw = System.IO.File.AppendText(fileName))
                {
                    sw.WriteLine(System.DateTime.Now.ToString());
                    sw.WriteLine(exception.Message);
                    sw.WriteLine(exception.InnerException);
                    
                }

            }
            else
            {
                using (StreamWriter sw = System.IO.File.CreateText(fileName))
                {
                    sw.WriteLine(System.DateTime.Now.ToString());
                    sw.WriteLine(exception.Message);
                    sw.WriteLine(exception.InnerException);
                }
            }
        }

        
    }


  
}
