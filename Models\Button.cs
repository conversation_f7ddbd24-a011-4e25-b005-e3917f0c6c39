﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Button
    {
        public Button()
        {
            Buttonreceivers = new HashSet<Buttonreceiver>();
        }

        public Guid Buttonid { get; set; }
        public Guid Contractid { get; set; }
        public byte Buttonnumber { get; set; }
        public string Buttonlabel { get; set; }
        public string Buttondescription { get; set; }

        public virtual Contract Contract { get; set; }
        public virtual Buttondatum Buttondatum { get; set; }
        public virtual ICollection<Buttonreceiver> Buttonreceivers { get; set; }
    }
}
