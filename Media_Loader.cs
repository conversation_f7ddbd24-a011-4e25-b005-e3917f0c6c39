﻿using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Helpers.IRCodesHelper;
using PhoenixAPI.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI
{
    public static class Media_Loader
    {

        public static void GenerateSchedule()
        {
           
            DateTime today = DateTime.Today;
            int daysUntilMonday = ((int)DayOfWeek.Monday - (int)today.DayOfWeek + 7) % 7;
            DateTime nextMonday = today.AddDays(daysUntilMonday);
            DateTime ShceduleDate = nextMonday; //new DateTime(2023, 04, 23);//
            //DateTime ShceduleDate = new DateTime(2024, 03, 04);//

            //  var context = new NovaDBContext();

            //DID THIS BECAUSE WITH REPORTS AND AND 

            try
            {
                //var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                string email = "<EMAIL>";// <EMAIL>";
                // var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    List<IRCodes> _list = new List<IRCodes>();
                    var _irc = IRC.GetInstance();
                    _list = _irc.getList();
                    if (_list.Count == 0)
                    {
                        _list = db.IRCodes.ToList();
                        _irc.setList(_list);
                    }

                    db.Database.ExecuteSqlRaw("Update [Ops].[InstallationScheduleCurrent] set IsCurrent = 0 WHERE IsCurrent = 1 ");
                    InstallationScheduleDates installationScheduleDates = new InstallationScheduleDates();
                    installationScheduleDates.isLatest = true;
                    installationScheduleDates.isCurrent = true;
                    installationScheduleDates.CreatedBy = email;
                    installationScheduleDates.ScheduleDate = ShceduleDate;

                    //lets see if the date exists
                    var getScheduleDates = db.InstallationScheduleDates.Where(x => x.ScheduleDate != ShceduleDate).ToList();
                    if (getScheduleDates.Count > 0)
                    {
                        //we need to update them all here
                        foreach (InstallationScheduleDates scheduleDates in getScheduleDates)
                        {
                            scheduleDates.isCurrent = false;
                            scheduleDates.isLatest = false;
                            db.Update(scheduleDates);
                        }
                    }

                    db.InstallationScheduleDates.Add(installationScheduleDates);
                    db.SaveChanges();


                    //now we need to get the rest of them, and then insert into this table
                    var results = db.VMyMobilityExportWithDays.ToList().OrderBy(x => x.Store);

                    var targetList = results.Select(x => new InstallationScheduleCurrent()
                    {
                        InstallationScheduleCurrentID = new System.Guid()
                        ,
                        CampaignFinished = false
                        ,
                        CampaignIRCodeSelected = false
                        ,
                        CampaignPicturePath = ""
                        ,
                        CampaignPictureTaken = false
                        ,
                        CampaignSpecialInstructionsRead = false
                        ,
                        CategoryName = x.CategoryName
                        ,
                        Chain = x.Chain
                        ,
                        Client = x.Client
                        ,
                        CommencementDate = x.CommencementDate
                        ,
                        CreatedBy = x.CreatedBy
                        ,
                        Cycle = x.Cycle
                        ,
                        DayOfCommencementDate = x.DayOfCommencementDate
                        ,
                        DayOfLastModified = x.DayOfLastModified
                        ,
                        DayOfTerminationDate = x.DayOfTerminationDate
                        ,
                        ForDate = x.ForDate
                        ,
                        GeneratedScheduleDate = System.DateTime.Now
                        ,
                        Group = x.Group
                        ,
                        InstallationDay = x.InstallationDay
                        ,
                        IRCodeID = 56
                        ,
                        JobNumber = x.JobNumber
                        ,
                        LastModified = x.LastModified
                        ,
                        LastModifiedBy = x.LastModifiedBy
                        ,
                        MediaType = x.MediaType
                        ,
                        MonthOfCommencementDate = x.MonthOfCommencementDate
                        ,
                        MonthOfLastModified = x.MonthOfLastModified
                        ,
                        MonthOfTerminationDate = x.MonthOfTerminationDate
                        ,
                        NumberOfWeeks = x.NumberOfWeeks
                        ,
                        Product = x.Product
                        ,
                        QtyToInstall = x.QtyToInstall
                        ,
                        Quantity = x.Quantity
                        ,
                        QuarterOfCommencementDate = x.QuarterOfCommencementDate
                        ,
                        QuarterOfLastModified = x.QuarterOfLastModified
                        ,
                        QuarterOfTerminationDate = x.QuarterOfTerminationDate
                        ,
                        Region = x.Region
                        ,
                        Section = x.Section
                        ,
                        SelectedIRCode = null
                        ,
                        SpecialInstructions = x.SpecialInstructions
                        ,
                        Status = x.Status
                        ,
                        StoId = x.StoId

                        ,
                        Store = x.Store
                        ,
                        StoreAndChain = x.StoreAndChain
                        ,
                        Teamlist = x.Teamlist
                        ,
                        TerminationDate = x.TerminationDate
                        ,
                        WeekOfCommencementDate = x.WeekOfCommencementDate
                        ,
                        WeekOfLastModified = x.WeekOfLastModified
                        ,
                        WeekOfTerminationDate = x.WeekOfTerminationDate
                        ,
                        YearOfCommencementDate = x.YearOfCommencementDate
                        ,
                        YearOfLastModified = x.YearOfLastModified
                        ,
                        AllowPictureAfterwards = true,

                        YearOfTerminationDate = x.YearOfTerminationDate
                        ,
                        MediaId = x.MediaId
                        ,
                        Installationteamid = x.Installationteamid
                        ,
                        IsCurrent = true
                    }).ToList();

                    //List<InstallationScheduleCurrent> targetList = new List<InstallationScheduleCurrent>(results.Cast<InstallationScheduleCurrent>());
                    db.InstallationScheduleCurrent.AddRange(targetList);
                    db.SaveChanges();

                    //try
                    //{
                    //    //we need to load previous IRCodes here
                    //    var CurrentList = db.InstallationScheduleCurrent.Where(x => x.IsCurrent == true && (x.Status == "Running" || x.Status == "Remove")).ToList();
                    //    var previousDate = db.InstallationScheduleCurrent.Where(x => x.IsCurrent == false).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                    //    var PreviousList = db.InstallationScheduleCurrent.Where(x => x.ForDate == previousDate.ForDate && (x.Status == "Running" || x.Status == "Install")).ToList();

                    //    List<InstallationScheduleCurrent> lstFinal = new List<InstallationScheduleCurrent>();

                    //    Parallel.ForEach(targetList, ins =>
                    //    {
                    //        //here we need to conver the rows
                    //        var previousInstallation = PreviousList.Where(x => x.IsCurrent == false &&
                    //        //x.Status != "Install" &&
                    //        x.JobNumber == ins.JobNumber &&
                    //        x.MediaType == ins.MediaType &&
                    //        x.CategoryName == ins.CategoryName &&
                    //        x.StoId == ins.StoId && x.Product == ins.Product &&
                    //        x.Status != "Install" &&
                    //        x.Client == ins.Client).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                    //        if (previousInstallation != null)
                    //        {
                    //            //we can set it
                    //            //lest see if it gets here.
                    //            ins.PreviousIRCodeId = previousInstallation.IRCodeID;
                    //            ins.PreviousIRCode = _list.FirstOrDefault(p => p.IRCodeID == previousInstallation.IRCodeID);// previousInstallation.SelectedIRCode;
                    //            ins.PreviousIRCodeComment = previousInstallation.IRCodeComment;
                    //            lstFinal.Add(ins);


                    //        }
                    //        else
                    //        {
                    //            ins.PreviousIRCode = null;
                    //        }
                    //    });

                    //    db.UpdateRange(lstFinal);
                    //    db.SaveChanges();


                    //}
                    //catch (Exception ex)sa
                    //{

                    //}
                   // return true;
                }
            }
            catch (Exception ex)
            {
               // return StatusCode(500, ex.InnerException.Message);

            }

        }

    }
}
