﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Term
    {
        public Term()
        {
            Clients = new HashSet<Client>();
        }

        public int TermsId { get; set; }
        public string TermsName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<Client> Clients { get; set; }
    }
}
