﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Vproposal
    {
        public string AccountManager { get; set; }
        public string Country { get; set; }
        public string ProposalStatus { get; set; }
        public string ProposalCreated { get; set; }
        public string ClientName { get; set; }
        public string MediaName { get; set; }
        public string CancellationReason { get; set; }
        public string StartDate { get; set; }
        public string EndDate { get; set; }
        public decimal? Rate { get; set; }
        public DateTime Date { get; set; }
        public string ProposalLikelyness { get; set; }
        public string InternalId { get; set; }
        public string Week { get; set; }
        public int ContactDetailId { get; set; }
        public string Quarter { get; set; }
        public string Fyear { get; set; }
        public string Month { get; set; }
    }
}
