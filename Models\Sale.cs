﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Sale
    {
        public Sale()
        {
            SaleDetails = new HashSet<SaleDetail>();
        }

        public Guid SaleId { get; set; }
        public int VanId { get; set; }
        public int StoreId { get; set; }
        public DateTime SaleDate { get; set; }
        public string OperatorName { get; set; }
        public string InvoiceNumber { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Store Store { get; set; }
        public virtual Van1 Van { get; set; }
        public virtual ICollection<SaleDetail> SaleDetails { get; set; }
    }
}
