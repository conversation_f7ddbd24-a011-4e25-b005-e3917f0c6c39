﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class GroupChain
    {
        public GroupChain()
        {
            ChainGroupStores = new HashSet<ChainGroupStore>();
        }

        public int GroupChainId { get; set; }
        public string GroupChainName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<ChainGroupStore> ChainGroupStores { get; set; }
    }
}
