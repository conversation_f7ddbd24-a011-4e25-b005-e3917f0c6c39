﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ContractProposalHeat
    {
        public ContractProposalHeat()
        {
            Contracts = new HashSet<Contract>();
        }

        public int ContractProposalHeatId { get; set; }
        public string ContractProposalHeatName { get; set; }
        public bool Dormant { get; set; }
        public DateTime CreationDate { get; set; }
        public string CreatedBy { get; set; }

        public virtual ICollection<Contract> Contracts { get; set; }
    }
}
