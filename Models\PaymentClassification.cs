﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class PaymentClassification
    {
        public PaymentClassification()
        {
            ChainSelectForPaymentClassifications = new HashSet<ChainSelectForPaymentClassification>();
            MediaRates = new HashSet<MediaRate>();
            StoreSelectedForPayments = new HashSet<StoreSelectedForPayment>();
        }

        public int Id { get; set; }
        public string PaymentClassificationName { get; set; }
        public int TypeOfPaymentId { get; set; }
        public double? StoreRentalRate { get; set; }
        public bool? Dormant { get; set; }
        public string TypeOfRate { get; set; }
        public string Beneficiary { get; set; }
        public DateTime? FixedPaymentFirstWeek { get; set; }
        public DateTime? FixedPaymentLastWeek { get; set; }

        public virtual TypeOfPayment TypeOfPayment { get; set; }
        public virtual ICollection<ChainSelectForPaymentClassification> ChainSelectForPaymentClassifications { get; set; }
        public virtual ICollection<MediaRate> MediaRates { get; set; }
        public virtual ICollection<StoreSelectedForPayment> StoreSelectedForPayments { get; set; }
    }
}
