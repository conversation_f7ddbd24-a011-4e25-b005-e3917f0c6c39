﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class PurchaseDetail
    {
        public Guid PurchaseDetailId { get; set; }
        public Guid PurchaseId { get; set; }
        public Guid ProductId { get; set; }
        public Guid PackSizeId { get; set; }
        public int Shrinks { get; set; }
        public int UnitsPerShrink { get; set; }
        public decimal Price { get; set; }

        public virtual PackSize PackSize { get; set; }
        public virtual Product Product { get; set; }
        public virtual Purchase Purchase { get; set; }
    }
}
