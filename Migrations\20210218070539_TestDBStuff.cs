﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class TestDBStuff : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.AlterColumn<string>(
            //    name: "MasterItemName",
            //    table: "InvetoryItemsWithMovement",
            //    type: "nvarchar(450)",
            //    nullable: false,
            //    defaultValue: "",
            //    oldClrType: typeof(string),
            //    oldType: "nvarchar(max)",
            //    oldNullable: true);

            //migrationBuilder.AddColumn<string>(
            //    name: "SourceLocation",
            //    table: "InvetoryItemsWithMovement",
            //    type: "nvarchar(450)",
            //    nullable: false,
            //    defaultValue: "");

            //migrationBuilder.AddPrimaryKey(
            //    name: "PK_InvetoryItemsWithMovement",
            //    table: "InvetoryItemsWithMovement",
            //    columns: new[] { "MasterItemName", "SourceLocation" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropPrimaryKey(
            //    name: "PK_InvetoryItemsWithMovement",
            //    table: "InvetoryItemsWithMovement");

            //migrationBuilder.DropColumn(
            //    name: "SourceLocation",
            //    table: "InvetoryItemsWithMovement");

            //migrationBuilder.AlterColumn<string>(
            //    name: "MasterItemName",
            //    table: "InvetoryItemsWithMovement",
            //    type: "nvarchar(max)",
            //    nullable: true,
            //    oldClrType: typeof(string),
            //    oldType: "nvarchar(450)");
        }
    }
}
