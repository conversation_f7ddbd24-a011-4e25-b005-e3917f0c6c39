﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BurstNotificationStoresRemoved
    {
        public Guid BurstId { get; set; }
        public Guid ContractId { get; set; }
        public int StoreId { get; set; }
        public bool? IsNewlyAdded { get; set; }
        public bool? HasBeenRemoved { get; set; }
        public bool? FirstNotificationSent { get; set; }
        public DateTime? FirstNotificationSentDate { get; set; }
        public string FirstNotificationSentDetails { get; set; }
        public bool? SecondNotificationSent { get; set; }
        public DateTime? SecondNotificationSentDate { get; set; }
        public string SecondNotificationSentDetails { get; set; }
        public bool? CancellationNoticeSent { get; set; }
    }
}
