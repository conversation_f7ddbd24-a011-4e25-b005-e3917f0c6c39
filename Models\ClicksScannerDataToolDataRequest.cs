﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ClicksScannerDataToolDataRequest
    {
        public int Id { get; set; }
        public string ContractNumber { get; set; }
        public bool? PriorYearIsSelectedForScannerData { get; set; }
        public bool? PriorPeriodIsSelectedForScannerData { get; set; }
        public bool? PostPeriodIsSelectedForScannerData { get; set; }
        public DateTime? PriorYearSelectedForScannerDataDate { get; set; }
        public DateTime? PriorPeriodSelectedForScannerDataDate { get; set; }
        public DateTime? PostPeriodSelectedForScannerDataDate { get; set; }
        public bool? PriorYearIsDataReadyForPrc { get; set; }
        public bool? PriorPeriodIsDataReadyForPrc { get; set; }
        public bool? PostPeriodIsDataReadyForPrc { get; set; }
        public DateTime? PriorYearReadyDate { get; set; }
        public DateTime? PriorPeriodReadyDate { get; set; }
        public DateTime? PostPeriodReadyDate { get; set; }
        public bool? PriorYearIsDataSubmittedForPrc { get; set; }
        public bool? PriorPeriodIsDataSubmittedForPrc { get; set; }
        public bool? PostPeriodIsDataSubmittedForPrc { get; set; }
        public DateTime? PriorYearSubmissionDate { get; set; }
        public DateTime? PriorPeriodSubmissionDate { get; set; }
        public DateTime? PostPeriodSubmissionDate { get; set; }
        public DateTime? PriorYearStartDate { get; set; }
        public DateTime? PriorYearEndDate { get; set; }
        public DateTime? PriorPeriodStartDate { get; set; }
        public DateTime? PriorPeriodEndDate { get; set; }
        public DateTime? PostPeriodStartDate { get; set; }
        public DateTime? PostPeriodEndDate { get; set; }
        public DateTime? PriorYearStartDateWeekendEnding { get; set; }
        public DateTime? PriorYearEndDateWeekendEnding { get; set; }
        public DateTime? PriorPeriodStartDateWeekendEnding { get; set; }
        public DateTime? PriorPeriodEndDateWeekendEnding { get; set; }
        public DateTime? PostPeriodStartDateWeekendEnding { get; set; }
        public DateTime? PostPeriodEndDateWeekendEnding { get; set; }
        public bool? RemoveMediaStores { get; set; }
    }
}
