﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class checkWhatHappens : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_InstallationScheduleCurrent_StoreId",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                column: "StoreId");

            migrationBuilder.AddForeignKey(
                name: "FK_InstallationScheduleCurrent_Store_StoreId",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                column: "StoreId",
                principalSchema: "Store",
                principalTable: "Store",
                principalColumn: "StoreID",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InstallationScheduleCurrent_Store_StoreId",
                schema: "Ops",
                table: "InstallationScheduleCurrent");

            migrationBuilder.DropIndex(
                name: "IX_InstallationScheduleCurrent_StoreId",
                schema: "Ops",
                table: "InstallationScheduleCurrent");

            migrationBuilder.DropColumn(
                name: "StoreId",
                schema: "Ops",
                table: "InstallationScheduleCurrent");
        }
    }
}
