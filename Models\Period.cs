﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Period
    {
        public Period()
        {
            BillingInstructions = new HashSet<BillingInstruction>();
            ChainTargets = new HashSet<ChainTarget>();
        }

        public int PeriodId { get; set; }
        public string PeriodName { get; set; }

        public virtual ICollection<BillingInstruction> BillingInstructions { get; set; }
        public virtual ICollection<ChainTarget> ChainTargets { get; set; }
    }
}
