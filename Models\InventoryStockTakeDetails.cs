﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryStockTakeDetails
    {

        [Key]
        [Column("InventoryStockTakeDetailId")]
        public int InventoryStockTakeDetailId { get; set; }
        public int InventoryStockTakeId { get; set; }

        public int? WarehouseId { get; set; }
        public int? InstallationTeamId { get; set; }

      
        public int InventoryStockTakeRoleId { get; set; }
        public string userName { get; set; }
      
        public DateTime CreationDate { get; set; }
      
        public string CreatedBy { get; set; }
        public bool isCompleted { get; set; }

        public bool isSignedOff { get; set; }

        public string signedOffBy { get; set; }
        [ForeignKey(nameof(InventoryStockTakeId))]
        public virtual InventoryStockTake InventoryStockTake { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        public virtual Warehouse Warehouse { get; set; }
        [ForeignKey(nameof(InstallationTeamId))]
        public virtual InstallationTeam InstallationTeam { get; set; }
       
        [ForeignKey(nameof(InventoryStockTakeRoleId))]
        public virtual InventoryStockTakeRole InventoryStockTakeRole { get; set; }


        public virtual List<InventoryStockTakeDetailsBarcodes> InventoryStockTakeDetailsBarcodes { get; set; }

    }
}
