﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BurstNotification
    {
        public int BurstNotificationId { get; set; }
        public Guid BurstId { get; set; }
        public Guid ContractId { get; set; }
        public string ContractNumber { get; set; }
        public DateTime FirstWeek { get; set; }
        public DateTime FirstNotificationDateOfBurst { get; set; }
        public DateTime SecondNotificationDateOfBurst { get; set; }
        public bool FirstNotificationSent { get; set; }
        public bool SecondNotificationSent { get; set; }
        public DateTime? DateFirstNotificationSent { get; set; }
        public DateTime? DateSecondNotificationSent { get; set; }
    }
}
