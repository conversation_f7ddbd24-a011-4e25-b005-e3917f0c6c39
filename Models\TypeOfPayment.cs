﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class TypeOfPayment
    {
        public TypeOfPayment()
        {
            PaymentClassifications = new HashSet<PaymentClassification>();
        }

        public int Id { get; set; }
        public string Type { get; set; }
        public bool Dormant { get; set; }

        public virtual ICollection<PaymentClassification> PaymentClassifications { get; set; }
    }
}
