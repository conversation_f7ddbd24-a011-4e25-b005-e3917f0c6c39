﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Systemuser
    {
        public Systemuser()
        {
            PasswordhistoryCreatedbyusers = new HashSet<Passwordhistory>();
            PasswordhistoryUsers = new HashSet<Passwordhistory>();
            RolememberModifiedbyusers = new HashSet<Rolemember>();
            RolememberUsers = new HashSet<Rolemember>();
            Roleowner1Modifiedbyusers = new HashSet<Roleowner1>();
            Roleowner1Users = new HashSet<Roleowner1>();
            Systemuserdeleteds = new HashSet<Systemuserdeleted>();
            SystemuserenabledModifiedbyusers = new HashSet<Systemuserenabled>();
            SystemuserenabledUsers = new HashSet<Systemuserenabled>();
        }

        public Guid Userid { get; set; }
        public string Firstname { get; set; }
        public string Lastname { get; set; }
        public string Email { get; set; }
        public string Username { get; set; }
        public string Passwordsalt { get; set; }

        public virtual ICollection<Passwordhistory> PasswordhistoryCreatedbyusers { get; set; }
        public virtual ICollection<Passwordhistory> PasswordhistoryUsers { get; set; }
        public virtual ICollection<Rolemember> RolememberModifiedbyusers { get; set; }
        public virtual ICollection<Rolemember> RolememberUsers { get; set; }
        public virtual ICollection<Roleowner1> Roleowner1Modifiedbyusers { get; set; }
        public virtual ICollection<Roleowner1> Roleowner1Users { get; set; }
        public virtual ICollection<Systemuserdeleted> Systemuserdeleteds { get; set; }
        public virtual ICollection<Systemuserenabled> SystemuserenabledModifiedbyusers { get; set; }
        public virtual ICollection<Systemuserenabled> SystemuserenabledUsers { get; set; }
    }
}
