﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryItemMovement
    {
        public int Id { get; set; }
        public string Barcode { get; set; }
        public int? MasterItemInvenoryItemId { get; set; }
        public int? FromWarehouseId { get; set; }
        public int? ToWarehouseId { get; set; }
        public int? MasterItemId { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string DeletedBy { get; set; }
        public DateTime? DeletionDate { get; set; }
        public int? FromVanId { get; set; }
        public int? ToVanId { get; set; }

        [ForeignKey(nameof(FromVanId))]
        public virtual InstallationTeam FromInstallationTeam { get; set; }
        public virtual Warehouse FromWarehouse { get; set; }
        public virtual MasterItem MasterItem { get; set; }
        public virtual MasterItemInventoryItem MasterItemInvenoryItem { get; set; }
        [ForeignKey(nameof(ToVanId))]
        public virtual InstallationTeam ToInstallationTeam { get; set; }
        public virtual Warehouse ToWarehouse { get; set; }
    }
}
