﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BillingScheduleGroupingDetail
    {
        public Guid? GroupId { get; set; }
        public int GroupDetailId { get; set; }
        public int? MediaId { get; set; }
        public int? ChainId { get; set; }

        public virtual BillingScheduleGroup Group { get; set; }
    }
}
