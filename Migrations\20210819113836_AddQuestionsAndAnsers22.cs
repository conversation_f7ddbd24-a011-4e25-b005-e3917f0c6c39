﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddQuestionsAndAnsers22 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "QuestionsAndAnswers",
                schema: "Ops",
                columns: table => new
                {
                    QuestionId = table.Column<int>(type: "int", nullable: false),
                    AnswerId = table.Column<int>(type: "int", nullable: false),
                    QuestionAnswerId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QuestionsAndAnswers", x => new { x.AnswerId, x.QuestionId });
                    table.ForeignKey(
                        name: "FK_QuestionsAndAnswers_Answers_AnswerId",
                        column: x => x.AnswerId,
                        principalSchema: "Ops",
                        principalTable: "Answers",
                        principalColumn: "AnswerId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_QuestionsAndAnswers_Questions_QuestionId",
                        column: x => x.QuestionId,
                        principalSchema: "Ops",
                        principalTable: "Questions",
                        principalColumn: "QuestionId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_QuestionsAndAnswers_QuestionId",
                schema: "Ops",
                table: "QuestionsAndAnswers",
                column: "QuestionId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "QuestionsAndAnswers",
                schema: "Ops");
        }
    }
}
