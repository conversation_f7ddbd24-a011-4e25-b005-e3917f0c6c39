﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Store
    {
        public Store()
        {
            BemsTasks = new HashSet<BemsTask>();
            ChainGroupStores = new HashSet<ChainGroupStore>();
            Consoles = new HashSet<Console>();
            DistributionCheckDetails = new HashSet<DistributionCheckDetail>();
            IndependentStoreListMembers = new HashSet<IndependentStoreListMember>();
            InstallationInstructions = new HashSet<InstallationInstruction>();
            InstallationSchedules = new HashSet<InstallationSchedule>();
            MasterItemInventoryItems = new HashSet<MasterItemInventoryItem>();
            Sales = new HashSet<Sale>();
            StoreLists = new HashSet<StoreList>();
            StoreMedia = new HashSet<StoreMedia>();
            StoreMediaCategoryPermissions = new HashSet<StoreMediaCategoryPermission>();
            StoreRentalRates = new HashSet<StoreRentalRate>();
            StoreSelectedForPayments = new HashSet<StoreSelectedForPayment>();
        }

        public int StoreId { get; set; }
        public int RegionID { get; set; }
        public int WarehouseId { get; set; }
        public int HeadOfficeId { get; set; }
        public int InstallationTeamId { get; set; }
        public string StoreNumber { get; set; }
        public string StoreName { get; set; }
        public string PhysicalAddressLine1 { get; set; }
        public string PhysicalAddressLine2 { get; set; }
        public int PhysicalAddressCityId { get; set; }
        public string PhysicalAddressPostalCode { get; set; }
        public string BillingAddressLine1 { get; set; }
        public string BillingAddressLine2 { get; set; }
        public int BillingAddressCityId { get; set; }
        public string BillingAddressPostalCode { get; set; }
        public string StoreVendorNumber { get; set; }
        public string StoreGlcode { get; set; }
        public string StoreManagerEmailAddress { get; set; }
        public string StoreManagerName { get; set; }
        public string SecondaryEmailAddress { get; set; }
        public string Notes { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }
        public DateTime? DormantEffectiveDate { get; set; }
        public DateTime? NoLongerDormantEndDate { get; set; }
        public int? InstallationDayId { get; set; }
        public int? InstallationDaysInstallationDayId { get; set; }

        public virtual City BillingAddressCity { get; set; }
        public virtual HeadOffice HeadOffice { get; set; }
        public virtual InstallationDays InstallationDaysInstallationDay { get; set; }
        public virtual InstallationTeam InstallationTeam { get; set; }
        public virtual City PhysicalAddressCity { get; set; }
        public virtual Region Region { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        public virtual ICollection<BemsTask> BemsTasks { get; set; }
        public virtual ICollection<ChainGroupStore> ChainGroupStores { get; set; }
        public virtual ICollection<Console> Consoles { get; set; }
        public virtual ICollection<DistributionCheckDetail> DistributionCheckDetails { get; set; }
        public virtual ICollection<IndependentStoreListMember> IndependentStoreListMembers { get; set; }
        public virtual ICollection<InstallationInstruction> InstallationInstructions { get; set; }
        public virtual ICollection<InstallationSchedule> InstallationSchedules { get; set; }

        public virtual ICollection<InstallationScheduleCurrent> InstallationSchedulesCurrent { get; set; }
        public virtual ICollection<MasterItemInventoryItem> MasterItemInventoryItems { get; set; }
        public virtual ICollection<Sale> Sales { get; set; }
        public virtual ICollection<StoreList> StoreLists { get; set; }
        public virtual ICollection<StoreMedia> StoreMedia { get; set; }
        public virtual ICollection<StoreMediaCategoryPermission> StoreMediaCategoryPermissions { get; set; }
        public virtual ICollection<StoreRentalRate> StoreRentalRates { get; set; }
        public virtual ICollection<StoreSelectedForPayment> StoreSelectedForPayments { get; set; }
    }
}
