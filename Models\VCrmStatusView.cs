﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VCrmStatusView
    {
        public string Country { get; set; }
        public string AccountManager { get; set; }
        public string ClientName { get; set; }
        public string ContactName { get; set; }
        public string StatusDesc { get; set; }
        public string MediaName { get; set; }
        public string Notes { get; set; }
        public string ProposalStatus { get; set; }
        public int? BillableWeeks { get; set; }
        public decimal? ProposalValue { get; set; }
        public DateTime? ContactDate { get; set; }
        public DateTime? CreatedDt { get; set; }
        public string StatusMonth { get; set; }
        public string StatusDate { get; set; }
        public string CreationMonth { get; set; }
        public string CreationDate { get; set; }
        public int StatusCount { get; set; }
        public int InternalId { get; set; }
    }
}
