﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaGroup
    {
        public MediaGroup()
        {
            MediaChannelGroupMembers = new HashSet<MediaChannelGroupMember>();
            MediaGroupMembers = new HashSet<MediaGroupMember>();
            Policies = new HashSet<Policy>();
        }

        public int MediaGroupId { get; set; }
        public string MediaGroupName { get; set; }
        public bool? Dormant { get; set; }

        public virtual ICollection<MediaChannelGroupMember> MediaChannelGroupMembers { get; set; }
        public virtual ICollection<MediaGroupMember> MediaGroupMembers { get; set; }
        public virtual ICollection<Policy> Policies { get; set; }
    }
}
