﻿using PhoenixAPI.Models;
using PhoenixAPI.Models.IncomingMedia;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Services.Interfaces
{
    public interface IMediaMonitorService
    {
        Task<object> CaptureMediaMonitor(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised);
        Task<object> DistributeMedia(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised);
        Task<object> GetContractNumber(string contractId);
        Task<object> GetBursts();
        string GetDocumentById(string document);
        Task<object> GetIncomingMediaRevised();
        Task<object> GetIncomingMediaRevisedByID(Guid contractID, string chain);
        Task<object> GetIncomingMediaRevisedByWarehouse(string warehouse);
        Task<object> GetIncomingMediaRevisedForNationalWarehouse();
        Task<object> GetIncomingMedia();
        Task<object> GetMediaMonitor();
        Task<object> UpdateImageOrCampaignNote(MediaMonitor mediaMonitor);
        Task<object> UploadImageOrCaptureCampaignNote(MediaMonitor mediaMonitor);
        Task<object> GetMediaRelationship();
        Task<object> AcknowledgeStatus(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised);
        Task<object> GetMediaMonitorRegions();
    }
}
