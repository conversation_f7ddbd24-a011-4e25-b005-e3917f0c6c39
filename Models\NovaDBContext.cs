﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using PhoenixAPI.Models.IncomingMedia;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class NovaDBContext : DbContext
    {
        public NovaDBContext()
        {
        }

        public NovaDBContext(DbContextOptions<NovaDBContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AccountManager> AccountManagers { get; set; }
        public virtual DbSet<AccountManagerBudget> AccountManagerBudgets { get; set; }
        public virtual DbSet<AccountManagerPermission> AccountManagerPermissions { get; set; }
        public virtual DbSet<AccountManagerPermissionUser> AccountManagerPermissionUsers { get; set; }
        public virtual DbSet<AllYearMonth> AllYearMonths { get; set; }
        public virtual DbSet<AuditLog> AuditLogs { get; set; }
        public virtual DbSet<BemsTask> BemsTasks { get; set; }
        public virtual DbSet<BillingInstruction> BillingInstructions { get; set; }
        public virtual DbSet<BillingScheduleGroup> BillingScheduleGroups { get; set; }
        public virtual DbSet<BillingScheduleGroupType> BillingScheduleGroupTypes { get; set; }
        public virtual DbSet<BillingScheduleGroupingDetail> BillingScheduleGroupingDetails { get; set; }
        public virtual DbSet<Brand> Brands { get; set; }
        public virtual DbSet<BrandAccountManager> BrandAccountManagers { get; set; }
        public virtual DbSet<BrandCategory> BrandCategories { get; set; }
        public virtual DbSet<BrandCategoryMember> BrandCategoryMembers { get; set; }
        public virtual DbSet<BrandFamily> BrandFamilies { get; set; }
        public virtual DbSet<BrandFamilyMember> BrandFamilyMembers { get; set; }
        public virtual DbSet<Burst> Bursts { get; set; }
        public virtual DbSet<BurstCategory> BurstCategories { get; set; }
        public virtual DbSet<BurstInstallationDay> BurstInstallationDays { get; set; }
        public virtual DbSet<BurstLoadingFee> BurstLoadingFees { get; set; }
        public virtual DbSet<BurstNotification> BurstNotifications { get; set; }
        public virtual DbSet<BurstNotificationStore> BurstNotificationStores { get; set; }
        public virtual DbSet<BurstNotificationStoresRemoved> BurstNotificationStoresRemoveds { get; set; }
        public virtual DbSet<BurstPcaStatus> BurstPcaStatuses { get; set; }
        public virtual DbSet<Button> Buttons { get; set; }
        public virtual DbSet<Buttondatum> Buttondata { get; set; }
        public virtual DbSet<Buttonreceiver> Buttonreceivers { get; set; }
        public virtual DbSet<Category> Categories { get; set; }
        public virtual DbSet<Category1> Categories1 { get; set; }
        public virtual DbSet<Chain> Chain { get; set; }
        public virtual DbSet<ChainGroup> ChainGroups { get; set; }
        public virtual DbSet<ChainGroupChain> ChainGroupChains { get; set; }
        public virtual DbSet<ChainGroupStore> ChainGroupStores { get; set; }
        public virtual DbSet<ChainPermission> ChainPermissions { get; set; }
        public virtual DbSet<ChainSelectForPaymentClassification> ChainSelectForPaymentClassifications { get; set; }
        public virtual DbSet<ChainTarget> ChainTargets { get; set; }
        public virtual DbSet<ChainType> ChainTypes { get; set; }
        public virtual DbSet<ChainsToIncludeForStorePayment> ChainsToIncludeForStorePayments { get; set; }
        public virtual DbSet<City> Cities { get; set; }
        public virtual DbSet<Classification> Classifications { get; set; }
        public virtual DbSet<ClicksBarcode> ClicksBarcodes { get; set; }
        public virtual DbSet<ClicksScannerDataToolAllRawDatum> ClicksScannerDataToolAllRawData { get; set; }
        public virtual DbSet<ClicksScannerDataToolBarcodeRepository> ClicksScannerDataToolBarcodeRepositories { get; set; }
        public virtual DbSet<ClicksScannerDataToolBarcodeRequest> ClicksScannerDataToolBarcodeRequests { get; set; }
        public virtual DbSet<ClicksScannerDataToolDataRequest> ClicksScannerDataToolDataRequests { get; set; }
        public virtual DbSet<ClicksScannerDataToolDegenerateRawDatum> ClicksScannerDataToolDegenerateRawData { get; set; }
        public virtual DbSet<ClicksScannerDataToolLoadedDatum> ClicksScannerDataToolLoadedData { get; set; }
        public virtual DbSet<ClicksScannerDataToolRollForwardRequest> ClicksScannerDataToolRollForwardRequests { get; set; }
        public virtual DbSet<ClicksScannerDataToolStoreLink> ClicksScannerDataToolStoreLinks { get; set; }
        public virtual DbSet<ClicksScannerDataToolStoreList> ClicksScannerDataToolStoreLists { get; set; }
        public virtual DbSet<Client> Clients { get; set; }
        public virtual DbSet<ClientAccountManager> ClientAccountManagers { get; set; }
        public virtual DbSet<ClientBrand> ClientBrands { get; set; }
        public virtual DbSet<Console> Consoles { get; set; }
        public virtual DbSet<Contract> Contract { get; set; }
        public virtual DbSet<ContractApproved> ContractApproveds { get; set; }
        public virtual DbSet<ContractClassification> ContractClassifications { get; set; }
        public virtual DbSet<ContractCostEstimate> ContractCostEstimates { get; set; }
        public virtual DbSet<ContractDate> ContractDates { get; set; }
        public virtual DbSet<ContractInventoryQty> ContractInventoryQties { get; set; }
        public virtual DbSet<ContractInvoice> ContractInvoices { get; set; }
        public virtual DbSet<ContractMediaCost> ContractMediaCosts { get; set; }
        public virtual DbSet<ContractMiscellaneousCharge> ContractMiscellaneousCharges { get; set; }
        public virtual DbSet<ContractProduct> ContractProducts { get; set; }
        public virtual DbSet<ContractProposalHeat> ContractProposalHeats { get; set; }
        public virtual DbSet<CrmClient> CrmClients { get; set; }
        public virtual DbSet<CrmContact> CrmContacts { get; set; }
        public virtual DbSet<CrmContactDetail> CrmContactDetails { get; set; }
        public virtual DbSet<CrmContactStatus> CrmContactStatuses { get; set; }
        public virtual DbSet<CrmLoginTracker> CrmLoginTrackers { get; set; }
        public virtual DbSet<DailyFiguresRevenue> DailyFiguresRevenues { get; set; }
        public virtual DbSet<DateCalendar> DateCalendar { get; set; }
        public virtual DbSet<DateCalendarLuka> DateCalendarLukas { get; set; }
        public virtual DbSet<DateTable> DateTables { get; set; }
        public virtual DbSet<DeletedMediaHistory> DeletedMediaHistories { get; set; }
        public virtual DbSet<DistributionCheck> DistributionChecks { get; set; }
        public virtual DbSet<DistributionCheckDetail> DistributionCheckDetails { get; set; }
        public virtual DbSet<DistributionRegion> DistributionRegions { get; set; }
        public virtual DbSet<EmailsToSend> EmailsToSends { get; set; }
        public virtual DbSet<FinancialYearQuarter> FinancialYearQuarters { get; set; }
        public virtual DbSet<FindDuplicate> FindDuplicates { get; set; }
        public virtual DbSet<Fiscal> Fiscals { get; set; }
        public virtual DbSet<FranchiseRevenuePnp> FranchiseRevenuePnps { get; set; }
        public virtual DbSet<FranchiseRevenuePnpdistinct> FranchiseRevenuePnpdistincts { get; set; }
        public virtual DbSet<GroupChain> GroupChains { get; set; }
        public virtual DbSet<HeadOffice> HeadOffices { get; set; }
        public virtual DbSet<ImportedInstallationActions> ImportedInstallationActions { get; set; }
        public virtual DbSet<IndependentStoreList> IndependentStoreLists { get; set; }
        public virtual DbSet<IndependentStoreListMember> IndependentStoreListMembers { get; set; }
        public virtual DbSet<InstallationActionsReport> InstallationActionsReports { get; set; }
        public virtual DbSet<InstallationActionsReportWithStore> InstallationActionsReportWithStores { get; set; }
        public virtual DbSet<InstallationDays> InstallationDays { get; set; }
        public virtual DbSet<InstallationInstruction> InstallationInstructions { get; set; }
        public virtual DbSet<InstallationSchedule> InstallationSchedules { get; set; }
        public virtual DbSet<InstallationSchedule20190315> InstallationSchedule20190315s { get; set; }
        public virtual DbSet<InstallationScheduleCurrent> InstallationScheduleCurrent { get; set; }

        public virtual DbSet<InstallationScheduleArchived> InstallationScheduleArchived { get; set; }

        public virtual DbSet<InstallationScheduleMM> InstallationScheduleMM { get; set; }
        public virtual DbSet<InstallationTeam> InstallationTeam { get; set; }
        public virtual DbSet<InstallationTeamType> InstallationTeamTypes { get; set; }
        public virtual DbSet<Inventory> Inventories { get; set; }
        public virtual DbSet<InventoryItemMovement> InventoryItemMovements { get; set; }
        public DbSet<InvetoryItemsWithMovement> InvetoryItemsWithMovement { get; set; }

        public DbSet<InventoryItemTransactions> InventoryItemTransactions { get; set; }

        public DbSet<InventoryItemTransactionType> InventoryItemTransactionType { get; set; }
        public virtual DbSet<InventoryQty> InventoryQties { get; set; }
        public virtual DbSet<InventoryQtyPart> InventoryQtyParts { get; set; }
        public virtual DbSet<InventoryQtyPrice> InventoryQtyPrices { get; set; }
        public virtual DbSet<IRCodes> IRCodes { get; set; }
        public virtual DbSet<LoadingFee> LoadingFees { get; set; }
        public virtual DbSet<LocationType> LocationTypes { get; set; }
        public virtual DbSet<Loginhistory> Loginhistories { get; set; }
        public virtual DbSet<MasterItem> MasterItems { get; set; }
        public virtual DbSet<MasterItemGroups> MasterItemGroups { get; set; }
        public virtual DbSet<MasterItemGroupMember> MasterItemGroupMembers { get; set; }
        public virtual DbSet<MasterItemInventoryItem> MasterItemInventoryItems { get; set; }
        public virtual DbSet<MasterItemType> MasterItemTypes { get; set; }
        public virtual DbSet<MediaCapexOpex> MediaCapexOpex { get; set; }
        public virtual DbSet<MediaCategory> MediaCategories { get; set; }
        public virtual DbSet<MediaChannel> MediaChannels { get; set; }
        public virtual DbSet<MediaChannelGroupMember> MediaChannelGroupMembers { get; set; }
        public virtual DbSet<MediaCost> MediaCosts { get; set; }
        public virtual DbSet<MediaFamily> MediaFamilies { get; set; }
        public virtual DbSet<MediaFamilyMember> MediaFamilyMembers { get; set; }
        public virtual DbSet<MediaGroup> MediaGroups { get; set; }
        public virtual DbSet<MediaGroupMember> MediaGroupMembers { get; set; }
        public virtual DbSet<MediaInstallationDay> MediaInstallationDays { get; set; }
        public virtual DbSet<MediaInventory> MediaInventories { get; set; }
        public virtual DbSet<MediaLifeCycle> MediaLifeCycles { get; set; }
        public virtual DbSet<MediaMonitor> MediaMonitors { get; set; }
        public virtual DbSet<MediaRate> MediaRates { get; set; }
        public virtual DbSet<MediaRule> MediaRules { get; set; }
        public virtual DbSet<MediaRuleRelationship> MediaRuleRelationships { get; set; }
        public virtual DbSet<Media> Media { get; set; }
        public virtual DbSet<Meeting> Meetings { get; set; }
        public virtual DbSet<Meetingtopic> Meetingtopics { get; set; }
        public virtual DbSet<Minute> Minutes { get; set; }
        public virtual DbSet<Minutetype> Minutetypes { get; set; }
        public virtual DbSet<MiscellaneousCharge> MiscellaneousCharges { get; set; }
        public virtual DbSet<MonthlycampaignreviewStore> MonthlycampaignreviewStores { get; set; }
        public virtual DbSet<OpsRegion> OpsRegions { get; set; }
        public virtual DbSet<OpsMediaMonitorRegion> OpsMediaMonitorRegions { get; set; }
        public virtual DbSet<NewContractListToBeUpdatedWithDatum> NewContractListToBeUpdatedWithData { get; set; }
        public virtual DbSet<OldProductName> OldProductNames { get; set; }
        public virtual DbSet<PackSize> PackSizes { get; set; }
        public virtual DbSet<PackSizeUnitOfMeasure> PackSizeUnitOfMeasures { get; set; }
        public virtual DbSet<Passwordhistory> Passwordhistories { get; set; }
        public virtual DbSet<PaymentClassification> PaymentClassifications { get; set; }
        public virtual DbSet<PcaStatus> PcaStatuses { get; set; }
        public virtual DbSet<Period> Periods { get; set; }
        public virtual DbSet<PnpBarcode> PnpBarcodes { get; set; }
        public virtual DbSet<Policy> Policies { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<ProductDistribution> ProductDistributions { get; set; }
        public virtual DbSet<ProvisionalBooking> ProvisionalBookings { get; set; }
        public virtual DbSet<Purchase> Purchases { get; set; }
        public virtual DbSet<PurchaseDetail> PurchaseDetails { get; set; }
        public virtual DbSet<PurchaseOrderNumber> PurchaseOrderNumbers { get; set; }
        public virtual DbSet<QuantityUnitOfMeasure> QuantityUnitOfMeasures { get; set; }
        public virtual DbSet<Receiver> Receivers { get; set; }
        public virtual DbSet<Region> Regions { get; set; }
        public virtual DbSet<RequestBarcode> RequestBarcodes { get; set; }
        public virtual DbSet<ResearchCategory> ResearchCategories { get; set; }
        public virtual DbSet<Revforde> Revfordes { get; set; }
        public virtual DbSet<Role> Roles { get; set; }
        public virtual DbSet<RoleOwner> RoleOwners { get; set; }
        public virtual DbSet<Rolemapping> Rolemappings { get; set; }
        public virtual DbSet<Rolemapping1> Rolemappings1 { get; set; }
        public virtual DbSet<Rolemember> Rolemembers { get; set; }
        public virtual DbSet<Roleowner1> Roleowners1 { get; set; }
        public virtual DbSet<Sale> Sales { get; set; }
        public virtual DbSet<SaleDetail> SaleDetails { get; set; }
        public virtual DbSet<ScannerBarCode> ScannerBarCodes { get; set; }
        public virtual DbSet<ScannerBarCodesResult> ScannerBarCodesResults { get; set; }
        public virtual DbSet<ScannerDataRawDistinct> ScannerDataRawDistincts { get; set; }
        public virtual DbSet<ScannerDataRawDistinctWithContract> ScannerDataRawDistinctWithContracts { get; set; }
        public virtual DbSet<ScannerDataRequestSet> ScannerDataRequestSets { get; set; }
        public virtual DbSet<ScannerDataResultDetail> ScannerDataResultDetails { get; set; }
        public virtual DbSet<ScannerDataResultDetailHi> ScannerDataResultDetailHis { get; set; }
        public virtual DbSet<ScannerDataToolBarcode> ScannerDataToolBarcodes { get; set; }
        public virtual DbSet<ScannerStore> ScannerStores { get; set; }
        public virtual DbSet<ScannerVOutstandingPcrReport> ScannerVOutstandingPcrReports { get; set; }
        public virtual DbSet<SchedulesForInstallationOnly> SchedulesForInstallationOnlies { get; set; }
        public virtual DbSet<Setting> Settings { get; set; }
        public virtual DbSet<Setting1> Settings1 { get; set; }
        public virtual DbSet<SettingGroup> SettingGroups { get; set; }
        public virtual DbSet<Settingtype> Settingtypes { get; set; }
        public virtual DbSet<Settingtypelink> Settingtypelinks { get; set; }
        public virtual DbSet<Shelf> Shelves { get; set; }
        public virtual DbSet<SparBarcode> SparBarcodes { get; set; }
        public virtual DbSet<SparScannerDataToolBarcodeRepository> SparScannerDataToolBarcodeRepositories { get; set; }
        public virtual DbSet<SparScannerDataToolBarcodeRequest> SparScannerDataToolBarcodeRequests { get; set; }
        public virtual DbSet<SparScannerDataToolDataRequest> SparScannerDataToolDataRequests { get; set; }
        public virtual DbSet<SparScannerDataToolStoreLink> SparScannerDataToolStoreLinks { get; set; }
        public virtual DbSet<SparScannerDataToolStoreList> SparScannerDataToolStoreLists { get; set; }
        public virtual DbSet<SparScannerDataToolXmlErrorLogFile> SparScannerDataToolXmlErrorLogFiles { get; set; }
        public virtual DbSet<StockTake> StockTakes { get; set; }
        public virtual DbSet<Store> Store { get; set; }
        public virtual DbSet<StoreList> StoreLists { get; set; }
        public virtual DbSet<StoreListBck20200608> StoreListBck20200608s { get; set; }
        public virtual DbSet<StoreMediaCategoryPermission> StoreMediaCategoryPermissions { get; set; }
        public virtual DbSet<StoreMedia> StoreMedia { get; set; }
        public virtual DbSet<StoreNotificationsSetup> StoreNotificationsSetups { get; set; }
        public virtual DbSet<StorePool> StorePools { get; set; }
        public virtual DbSet<StoreRentalRate> StoreRentalRates { get; set; }
        public virtual DbSet<StoreRentalRateStaging> StoreRentalRateStagings { get; set; }
        public virtual DbSet<StoreSelectedForPayment> StoreSelectedForPayments { get; set; }
        public virtual DbSet<StoreSelectedForPaymentBck> StoreSelectedForPaymentBcks { get; set; }
        public virtual DbSet<StorelistBckjune> StorelistBckjunes { get; set; }
        public virtual DbSet<StorelistBckjunev2> StorelistBckjunev2s { get; set; }
        public virtual DbSet<StoresByChainCount> StoresByChainCounts { get; set; }
        public virtual DbSet<Supplier> Suppliers { get; set; }
        public virtual DbSet<SupplierInventory> SupplierInventories { get; set; }
        public virtual DbSet<Systemuser> Systemusers { get; set; }
        public virtual DbSet<Systemuserdeleted> Systemuserdeleteds { get; set; }
        public virtual DbSet<Systemuserenabled> Systemuserenableds { get; set; }
        public virtual DbSet<TeamManager> TeamManagers { get; set; }
        public virtual DbSet<TempBrandNameWord> TempBrandNameWords { get; set; }
        public virtual DbSet<TempSparStoreStagingTable> TempSparStoreStagingTables { get; set; }
        public virtual DbSet<TempStoreGrouping> TempStoreGroupings { get; set; }
        public virtual DbSet<Term> Terms { get; set; }
        public virtual DbSet<ThisIsMine> ThisIsMines { get; set; }
        public virtual DbSet<Transactor> Transactors { get; set; }
        public virtual DbSet<TypeOfPayment> TypeOfPayments { get; set; }
        public virtual DbSet<UnitsPerShrink> UnitsPerShrinks { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<User1> Users1 { get; set; }
        public virtual DbSet<V2CurrentStoreRentalRate> V2CurrentStoreRentalRates { get; set; }
        public virtual DbSet<V2StoreRentalDue> V2StoreRentalDues { get; set; }
        public virtual DbSet<VAccountManagersByPermissionEditMyClient> VAccountManagersByPermissionEditMyClients { get; set; }
        public virtual DbSet<VAccountManagersByPermissionEditMyContract> VAccountManagersByPermissionEditMyContracts { get; set; }
        public virtual DbSet<VAccountManagersByPermissionEditMyProvisionalBooking> VAccountManagersByPermissionEditMyProvisionalBookings { get; set; }
        public virtual DbSet<VAccountManagersByPermissionViewMyContract> VAccountManagersByPermissionViewMyContracts { get; set; }
        public virtual DbSet<VBaccount> VBaccounts { get; set; }
        public virtual DbSet<VBaseRevenue> VBaseRevenues { get; set; }
        public virtual DbSet<VBaseRevenueContracthardcopy> VBaseRevenueContracthardcopies { get; set; }
        public virtual DbSet<VBaseRevenueUnsigned> VBaseRevenueUnsigneds { get; set; }
        public virtual DbSet<VBillingInstructionSplit> VBillingInstructionSplits { get; set; }
        public virtual DbSet<VBottomEndDistributionStore> VBottomEndDistributionStores { get; set; }
        public virtual DbSet<VBrandAccountManagersDate> VBrandAccountManagersDates { get; set; }
        public virtual DbSet<VBrandFamiliesByPermissionEditMyClient> VBrandFamiliesByPermissionEditMyClients { get; set; }
        public virtual DbSet<VBrandsByPermissionEditMyClient> VBrandsByPermissionEditMyClients { get; set; }
        public virtual DbSet<VBrandsByPermissionEditMyProvisionalBooking> VBrandsByPermissionEditMyProvisionalBookings { get; set; }
        public virtual DbSet<VBurstCategory> VBurstCategories { get; set; }
        public virtual DbSet<VBurstDetailsByPermissionViewMyContract> VBurstDetailsByPermissionViewMyContracts { get; set; }
        public virtual DbSet<VBurstPeriodCoveredByProvisionalBooking> VBurstPeriodCoveredByProvisionalBookings { get; set; }
        public virtual DbSet<VBurstQueuedBehindCompetingBrand> VBurstQueuedBehindCompetingBrands { get; set; }
        public virtual DbSet<VBurstWeekQtyCoveredByProvisionalBooking> VBurstWeekQtyCoveredByProvisionalBookings { get; set; }
        public virtual DbSet<VClientAccountManagerDate> VClientAccountManagerDates { get; set; }
        public virtual DbSet<VClientAccountManagerPeriodsWithActivity> VClientAccountManagerPeriodsWithActivities { get; set; }
        public virtual DbSet<VClientHistoryReadOnlyContract> VClientHistoryReadOnlyContracts { get; set; }
        public virtual DbSet<VClientsImPermittedToSee> VClientsImPermittedToSees { get; set; }
        public virtual DbSet<VCompetingBrand> VCompetingBrands { get; set; }
        public virtual DbSet<VCompetingMedium> VCompetingMedia { get; set; }
        public virtual DbSet<VContractActivityDate> VContractActivityDates { get; set; }
        public virtual DbSet<VContractDate> VContractDates { get; set; }
        public virtual DbSet<VContractDatesWithBurst> VContractDatesWithBursts { get; set; }
        public virtual DbSet<VContractProduction> VContractProductions { get; set; }
        public virtual DbSet<VContractScannerDataStatus> VContractScannerDataStatuses { get; set; }
        public virtual DbSet<VContractScannerDataStatusDetail> VContractScannerDataStatusDetails { get; set; }
        public virtual DbSet<VContractsByPermissionEditMyContract> VContractsByPermissionEditMyContracts { get; set; }
        public virtual DbSet<VContractsByPermissionViewMyContract> VContractsByPermissionViewMyContracts { get; set; }
        public virtual DbSet<VCrmStatusView> VCrmStatusViews { get; set; }
        public virtual DbSet<VCurrentBrandAccountManager> VCurrentBrandAccountManagers { get; set; }
        public virtual DbSet<VCurrentClientAccountManager> VCurrentClientAccountManagers { get; set; }
        public virtual DbSet<VCurrentMediaService> VCurrentMediaServices { get; set; }
        public virtual DbSet<VCurrentStoreRentalRate> VCurrentStoreRentalRates { get; set; }
        public virtual DbSet<VDistFullCycleCheckByVanByStore> VDistFullCycleCheckByVanByStores { get; set; }
        public virtual DbSet<VEffectiveBurst> VEffectiveBursts { get; set; }
        public virtual DbSet<VEffectiveProvisionalBooking> VEffectiveProvisionalBookings { get; set; }
        public virtual DbSet<VExecutoryContract> VExecutoryContracts { get; set; }
        public virtual DbSet<VInstallationRevenueByStore> VInstallationRevenueByStores { get; set; }
        public virtual DbSet<VInstallationsByPolicyByBurst> VInstallationsByPolicyByBursts { get; set; }
        public virtual DbSet<VInventoryQtyPriceDate> VInventoryQtyPriceDates { get; set; }
        public virtual DbSet<VMediaService> VMediaServices { get; set; }
        public virtual DbSet<VMonthlyRevenueBreakdown> VMonthlyRevenueBreakdowns { get; set; }
        public virtual DbSet<VMyMobilityExport> VMyMobilityExport { get; set; }
        public virtual DbSet<VMyMobilityExportWithDays> VMyMobilityExportWithDays { get; set; }
        public virtual DbSet<VNovaStoreList> VNovaStoreLists { get; set; }
        public virtual DbSet<VNovaStoreListAll> VNovaStoreListAlls { get; set; }
        public virtual DbSet<VNovaStoreListLiquor> VNovaStoreListLiquors { get; set; }
        public virtual DbSet<VPnPchainSplit> VPnPchainSplits { get; set; }
        public virtual DbSet<VPolicyBurst> VPolicyBursts { get; set; }
        public virtual DbSet<VProductionByContract> VProductionByContracts { get; set; }
        public virtual DbSet<VProductionRevenue> VProductionRevenues { get; set; }
        public virtual DbSet<VRentalBreakdown> VRentalBreakdowns { get; set; }
        public virtual DbSet<VRentalBreakdownUnsigned> VRentalBreakdownUnsigneds { get; set; }
        public virtual DbSet<VRentalBreakdownWithPriorYear> VRentalBreakdownWithPriorYears { get; set; }
        public virtual DbSet<VRentalBreakdownWithPriorYearUnsigned> VRentalBreakdownWithPriorYearUnsigneds { get; set; }
        public virtual DbSet<VRevenueCalculation> VRevenueCalculations { get; set; }
        public virtual DbSet<VRevenuePercentageByCategory> VRevenuePercentageByCategories { get; set; }
        public virtual DbSet<VScannerContractList> VScannerContractLists { get; set; }
        public virtual DbSet<VSelectsummarySet> VSelectsummarySets { get; set; }
        public virtual DbSet<VSparContractList> VSparContractLists { get; set; }
        public virtual DbSet<VStorePaymentRatesExport> VStorePaymentRatesExports { get; set; }
        public virtual DbSet<VStoreRentalDue> VStoreRentalDues { get; set; }
        public virtual DbSet<VStoreUpdateList> VStoreUpdateLists { get; set; }
        public virtual DbSet<VStorecountByChainByMedium> VStorecountByChainByMedia { get; set; }
        public virtual DbSet<VViewableContractInfo> VViewableContractInfos { get; set; }
        public virtual DbSet<VViewableProvisionalBookingInfo> VViewableProvisionalBookingInfos { get; set; }
        public virtual DbSet<VVisibleContractList> VVisibleContractLists { get; set; }
        public virtual DbSet<VWeeklyProductionRevenue> VWeeklyProductionRevenues { get; set; }
        public virtual DbSet<Van> Vans { get; set; }
        public virtual DbSet<Van1> Vans1 { get; set; }
        public virtual DbSet<VanChain> VanChains { get; set; }
        public virtual DbSet<VanRegion> VanRegions { get; set; }
        public virtual DbSet<VanUser> VanUsers { get; set; }
        public virtual DbSet<VpicknpayContractList> VpicknpayContractLists { get; set; }
        public virtual DbSet<VpicknpayContractListDetail> VpicknpayContractListDetails { get; set; }
        public virtual DbSet<VpicknpayStoreList> VpicknpayStoreLists { get; set; }
        public virtual DbSet<VpicknpaysparContractListDetail> VpicknpaysparContractListDetails { get; set; }
        public virtual DbSet<Vproposal> Vproposals { get; set; }
        public virtual DbSet<VstoreAllocation> VstoreAllocations { get; set; }
        public virtual DbSet<VstoreAllocationAll> VstoreAllocationAlls { get; set; }
        public virtual DbSet<VstoreAllocationLiquor> VstoreAllocationLiquors { get; set; }
        public virtual DbSet<Vunallocatedrequest> Vunallocatedrequests { get; set; }
        public virtual DbSet<VwFixedPaymentCalenderIteration> VwFixedPaymentCalenderIterations { get; set; }
        public virtual DbSet<VwInventoryqtycostwhensigned> VwInventoryqtycostwhensigneds { get; set; }
        public virtual DbSet<VwListofContract> VwListofContracts { get; set; }
        public virtual DbSet<VwListofProductlocatorcontract> VwListofProductlocatorcontracts { get; set; }
        public virtual DbSet<VwListofProductlocatorstore> VwListofProductlocatorstores { get; set; }
        public virtual DbSet<VwListofSystemuser> VwListofSystemusers { get; set; }
        public virtual DbSet<VwPnpstoretype> VwPnpstoretypes { get; set; }
        public virtual DbSet<VwRolemember> VwRolemembers { get; set; }
        public virtual DbSet<VwRoleowner> VwRoleowners { get; set; }
        public virtual DbSet<VwStoreinstallationsperburstperweek> VwStoreinstallationsperburstperweeks { get; set; }
        public virtual DbSet<VwSystemuserpassword> VwSystemuserpasswords { get; set; }
        public virtual DbSet<VwSystemuserstatus> VwSystemuserstatuses { get; set; }
        public virtual DbSet<Warehouse> Warehouse { get; set; }
        public virtual DbSet<Wholesaler> Wholesalers { get; set; }

        public virtual DbSet<InstallationCounts> InstallationCounts { get; set; }

        public virtual DbSet<ItemMovementToAndFrom> ItemMovementToAndFrom { get; set; }

        public virtual DbSet<WarehouseManager> WarehouseManager { get; set; }

        public virtual DbSet<InstallationScheduleDates> InstallationScheduleDates { get; set; }

        public virtual DbSet<InventoryStockTake> InventoryStockTake { get; set; }

        public virtual DbSet<InventoryStockTakeRole> InventoryStockTakeRole { get; set; }

        public virtual DbSet<InventoryStockTakeDetails> InventoryStockTakeDetails { get; set; }

        public virtual DbSet<InventoryStockTakeDetailsBarcodes> InventoryStockTakeDetailsBarcodes { get; set; }

        public virtual DbSet<Questions> Questions { get; set; }

        public virtual DbSet<InstallationScheduleQuestionsAndAnswers> InstallationScheduleQuestionsAndAnswers { get; set; }

        public virtual DbSet<Answers> Answers { get; set; }

        public virtual DbSet<QuestionsAndAnswers> QuestionsAndAnswers { get; set; }

        public virtual DbSet<IncomingMediaPerRegion> IncomingMediaPerRegions { get; set; }

        public virtual DbSet<IncomingMediaPerRegionRevised> IncomingMediaPerRegionRevised { get; set; }
        public virtual DbSet<PhoenixAPI.Models.Demonstrations.Demonstration> Demonstrations { get; set; }
        public virtual DbSet<PhoenixAPI.Models.IncomingMedia.IncomingMedia> IncomingMedia { get; set; }
        public virtual DbSet<PhoenixAPI.Models.Demonstrations.StoreResult> StoreResults { get; set; }
        public virtual DbSet<PhoenixAPI.Models.Demonstrations.StoreListResult> StoreListResults { get; set; }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
               // optionsBuilder.UseSqlServer("Data Source=192.168.0.56;Initial Catalog=NovaDB;Persist Security Info=True;User ID=sa;Password=************");
               // optionsBuilder.UseSqlServer("Data Source=10.20.32.3;Initial Catalog=NovaDB;Persist Security Info=True;User ID=sa;Password=************");
                optionsBuilder.UseSqlServer(@"Data Source=192.168.0.13\LIVE;Initial Catalog=NovaDB;Persist Security Info=True;User ID=SSIS;Password=*****************$&*");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<InvetoryItemsWithMovement>().HasKey(c => new { c.MasterItemName, c.SourceLocation });

            modelBuilder.HasAnnotation("Relational:Collation", "Latin1_General_CI_AS");

            modelBuilder.Entity<AccountManager>(entity =>
            {
                entity.ToTable("AccountManager", "Sales");

                entity.HasIndex(e => e.Code, "UniqueCode")
                    .IsUnique();

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')")
                    .HasComment("If this contact is an Instore account director, he must have a code.");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.FirstName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.LastName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PrincipalId).HasColumnName("principal_id");
            });

            modelBuilder.Entity<AccountManagerBudget>(entity =>
            {
                entity.HasKey(e => new { e.AccountManagerId, e.FiscalId });

                entity.ToTable("AccountManagerBudgets", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.FiscalId).HasColumnName("FiscalID");

                entity.Property(e => e.Budget).HasColumnType("money");

                entity.HasOne(d => d.AccountManager)
                    .WithMany(p => p.AccountManagerBudgets)
                    .HasForeignKey(d => d.AccountManagerId)
                    .HasConstraintName("FK_AccountManagerBudgets_AccountManager");

                entity.HasOne(d => d.Fiscal)
                    .WithMany(p => p.AccountManagerBudgets)
                    .HasForeignKey(d => d.FiscalId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AccountManagerBudgets_Fiscal");
            });

            modelBuilder.Entity<AccountManagerPermission>(entity =>
            {
                entity.ToTable("AccountManagerPermission", "Sales");

                entity.Property(e => e.AccountManagerPermissionId).HasColumnName("AccountManagerPermissionID");

                entity.Property(e => e.AccountManagerPermissionName)
                    .IsRequired()
                    .HasMaxLength(200);
            });

            modelBuilder.Entity<AccountManagerPermissionUser>(entity =>
            {
                entity.HasKey(e => new { e.AccountManagerPermissionId, e.AccountManagerId, e.PrincipalId });

                entity.ToTable("AccountManagerPermissionUser", "Sales");

                entity.Property(e => e.AccountManagerPermissionId).HasColumnName("AccountManagerPermissionID");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.PrincipalId).HasColumnName("principal_id");

                entity.HasOne(d => d.AccountManager)
                    .WithMany(p => p.AccountManagerPermissionUsers)
                    .HasForeignKey(d => d.AccountManagerId)
                    .HasConstraintName("FK_AccountManagerPermissionUser_AccountManager");

                entity.HasOne(d => d.AccountManagerPermission)
                    .WithMany(p => p.AccountManagerPermissionUsers)
                    .HasForeignKey(d => d.AccountManagerPermissionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AccountManagerPermissionUser_AccountManagerPermission");
            });

            modelBuilder.Entity<AllYearMonth>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("AllYearMonths", "reporting");

                entity.Property(e => e.ContractNumber).HasMaxLength(8);

                entity.Property(e => e.YearMonth)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ZeroValue).HasColumnType("decimal(38, 4)");
            });

            modelBuilder.Entity<AuditLog>(entity =>
            {
                entity.HasKey(e => e.EntryId);

                entity.ToTable("AuditLog");

                entity.Property(e => e.EntryId)
                    .HasColumnName("EntryID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Action)
                    .IsRequired()
                    .HasMaxLength(4000);

                entity.Property(e => e.ActionDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ObjectName)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.ObjectType)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.User)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");
            });

            modelBuilder.Entity<BemsTask>(entity =>
            {
                entity.ToTable("BemsTask", "Distribution");

                entity.HasComment("BEMS - Bottom End Marketing Services.  This table contains the individual tasks that a van operator needs to perform.  One task is defined as going to one store on a specific date to check stock of a specific product in a specific pack size measure in a specific unit.\r\n\r\nDateScheduled - date on which the van operator was scheduled to perform this task\r\nDateCompleted - date on which the van operator actually completed this task\r\nDateModified - date someone changes the task internally\r\nDateRequested - date someone requests a copy of the task externally via the web service\r\nDateSubmitted - date someone updates the Date, InStock and UserID columns externally via the web service");

                entity.HasIndex(e => new { e.DateScheduled, e.VanId, e.StoreId, e.ProductId, e.PackSizeId, e.PackSizeUnitOfMeasureId }, "UniqueTask")
                    .IsUnique();

                entity.Property(e => e.BemsTaskId)
                    .HasColumnName("BemsTaskID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.DateCompleted).HasColumnType("datetime");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.DateScheduled).HasColumnType("datetime");

                entity.Property(e => e.DateSubmitted).HasColumnType("datetime");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.PackSizeUnitOfMeasureId).HasColumnName("PackSizeUnitOfMeasureID");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.UserId).HasColumnName("UserID");

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.HasOne(d => d.PackSize)
                    .WithMany(p => p.BemsTasks)
                    .HasForeignKey(d => d.PackSizeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionTask_PackSize");

                entity.HasOne(d => d.PackSizeUnitOfMeasure)
                    .WithMany(p => p.BemsTasks)
                    .HasForeignKey(d => d.PackSizeUnitOfMeasureId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionTask_PackSizeUnitOfMeasure");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.BemsTasks)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionTask_Store");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.BemsTasks)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_BemsTask_User");

                entity.HasOne(d => d.Van)
                    .WithMany(p => p.BemsTasks)
                    .HasForeignKey(d => d.VanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionTask_Van");
            });

            modelBuilder.Entity<BillingInstruction>(entity =>
            {
                entity.ToTable("BillingInstruction", "Sales");

                entity.HasIndex(e => new { e.ContractId, e.PeriodId }, "UniqueContractPeriodCombo")
                    .IsUnique();

                entity.Property(e => e.BillingInstructionId)
                    .HasColumnName("BillingInstructionID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Amount).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.PeriodId).HasColumnName("PeriodID");

                entity.Property(e => e.Ponumber)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("PONumber")
                    .HasDefaultValueSql("('')");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.BillingInstructionsNavigation)
                    .HasForeignKey(d => d.ContractId)
                    .HasConstraintName("FK_BillingInstruction_Contract");

                entity.HasOne(d => d.Period)
                    .WithMany(p => p.BillingInstructions)
                    .HasForeignKey(d => d.PeriodId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BillingInstruction_Period");
            });

            modelBuilder.Entity<BillingScheduleGroup>(entity =>
            {
                entity.HasKey(e => e.GroupId);

                entity.ToTable("BillingScheduleGroup", "Finance");

                entity.Property(e => e.GroupId)
                    .ValueGeneratedNever()
                    .HasColumnName("GroupID");

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("(suser_name())");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.GroupName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GroupTypeId).HasColumnName("GroupTypeID");

                entity.HasOne(d => d.GroupType)
                    .WithMany(p => p.BillingScheduleGroups)
                    .HasForeignKey(d => d.GroupTypeId)
                    .HasConstraintName("FK_BillingScheduleGroup_BillingScheduleGroupType");
            });

            modelBuilder.Entity<BillingScheduleGroupType>(entity =>
            {
                entity.HasKey(e => e.GroupTypeId);

                entity.ToTable("BillingScheduleGroupType", "Finance");

                entity.Property(e => e.GroupTypeId).HasColumnName("GroupTypeID");

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.GroupTypeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<BillingScheduleGroupingDetail>(entity =>
            {
                entity.HasKey(e => e.GroupDetailId);

                entity.ToTable("BillingScheduleGroupingDetail", "Finance");

                entity.Property(e => e.GroupDetailId).HasColumnName("GroupDetailID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.GroupId).HasColumnName("GroupID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.HasOne(d => d.Group)
                    .WithMany(p => p.BillingScheduleGroupingDetails)
                    .HasForeignKey(d => d.GroupId)
                    .HasConstraintName("FK_BillingScheduleGroupingDetail_BillingScheduleGroup");
            });

            modelBuilder.Entity<Brand>(entity =>
            {
                entity.ToTable("Brand", "Client");

                entity.HasIndex(e => e.BrandName, "UniqueBrandName")
                    .IsUnique();

                entity.Property(e => e.BrandId)
                    .HasColumnName("BrandID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");
            });

            modelBuilder.Entity<BrandAccountManager>(entity =>
            {
                entity.HasKey(e => new { e.BrandId, e.EffectiveDate });

                entity.ToTable("BrandAccountManager", "Client");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.AccountManager)
                    .WithMany(p => p.BrandAccountManagers)
                    .HasForeignKey(d => d.AccountManagerId)
                    .HasConstraintName("FK_BrandAccountManager_AccountManager");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.BrandAccountManagers)
                    .HasForeignKey(d => d.BrandId)
                    .HasConstraintName("FK_BrandAccountManager_Brand");
            });

            modelBuilder.Entity<BrandCategory>(entity =>
            {
                entity.ToTable("BrandCategory", "Client");

                entity.Property(e => e.BrandCategoryName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<BrandCategoryMember>(entity =>
            {
                entity.HasKey(e => new { e.BrandId, e.BrandCategoryId });

                entity.ToTable("BrandCategoryMember", "Client");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BrandCategoryId).HasColumnName("BrandCategoryID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.BrandCategory)
                    .WithMany(p => p.BrandCategoryMembers)
                    .HasForeignKey(d => d.BrandCategoryId)
                    .HasConstraintName("FK_BrandCategoryMember_BrandCategory");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.BrandCategoryMembers)
                    .HasForeignKey(d => d.BrandId)
                    .HasConstraintName("FK_BrandCategoryMember_Brand");
            });

            modelBuilder.Entity<BrandFamily>(entity =>
            {
                entity.ToTable("BrandFamily", "Client");

                entity.HasIndex(e => e.BrandFamilyName, "UniqueBrandFamilyName")
                    .IsUnique();

                entity.Property(e => e.BrandFamilyId).HasColumnName("BrandFamilyID");

                entity.Property(e => e.BrandFamilyName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<BrandFamilyMember>(entity =>
            {
                entity.HasKey(e => new { e.BrandId, e.BrandFamilyId });

                entity.ToTable("BrandFamilyMember", "Client");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BrandFamilyId).HasColumnName("BrandFamilyID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.BrandFamily)
                    .WithMany(p => p.BrandFamilyMembers)
                    .HasForeignKey(d => d.BrandFamilyId)
                    .HasConstraintName("FK_BrandFamilyMember_BrandFamily");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.BrandFamilyMembers)
                    .HasForeignKey(d => d.BrandId)
                    .HasConstraintName("FK_BrandFamilyMember_Brand");
            });

            modelBuilder.Entity<Burst>(entity =>
            {
                entity.ToTable("Burst", "Sales");

                entity.HasIndex(e => e.ContractID, "INDX_Sales_Burst_ContractID")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.BurstId)
                    .HasColumnName("BurstID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.AdsPerInstallation).HasDefaultValueSql("((1))");

                entity.Property(e => e.ApplyInstructionsAcrossAllBursts).HasDefaultValueSql("((0))");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ContractID).HasColumnName("ContractID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Discount).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.FirstWeek)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.InstallationInstructions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.RentalRate).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.StorePoolId)
                    .HasColumnName("StorePoolID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.TempIgnoreDefaultInstructions)
                    .HasColumnName("tempIgnoreDefaultInstructions")
                    .HasDefaultValueSql("((0))");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.Bursts)
                    .HasForeignKey(d => d.BrandId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Burst_Brand");

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.Bursts)
                    .HasForeignKey(d => d.ChainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Burst_Chain");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.Bursts)
                    .HasForeignKey(d => d.ContractID);

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.Burst)
                    .HasForeignKey(d => d.MediaId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Burst_Media");

                entity.HasOne(d => d.StorePool)
                    .WithMany(p => p.Bursts)
                    .HasForeignKey(d => d.StorePoolId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Burst_StorePool");
            });

            modelBuilder.Entity<BurstCategory>(entity =>
            {
                entity.HasKey(e => new { e.BurstId, e.CategoryId });

                entity.ToTable("BurstCategory", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Burst)
                    .WithMany(p => p.BurstCategories)
                    .HasForeignKey(d => d.BurstId)
                    .HasConstraintName("FK_BurstCategory_Burst");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.BurstCategories)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BurstCategory_Category");
            });

            modelBuilder.Entity<BurstInstallationDay>(entity =>
            {
                entity.HasKey(e => new { e.BurstId, e.InstallationDayId });

                entity.ToTable("BurstInstallationDay", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.InstallationDayId).HasColumnName("InstallationDayID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Burst)
                    .WithMany(p => p.BurstInstallationDays)
                    .HasForeignKey(d => d.BurstId)
                    .HasConstraintName("FK_BurstInstallationDay_Burst");

                entity.HasOne(d => d.InstallationDay)
                    .WithMany(p => p.BurstInstallationDays)
                    .HasForeignKey(d => d.InstallationDayId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MediaInstallationDay_BurstInstallationDay");
            });

            modelBuilder.Entity<BurstLoadingFee>(entity =>
            {
                entity.HasKey(e => new { e.BurstId, e.LoadingFeeId });

                entity.ToTable("BurstLoadingFee", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.LoadingFeeId).HasColumnName("LoadingFeeID");

                entity.Property(e => e.Percentage).HasColumnType("decimal(5, 2)");

                entity.HasOne(d => d.Burst)
                    .WithMany(p => p.BurstLoadingFees)
                    .HasForeignKey(d => d.BurstId)
                    .HasConstraintName("FK_BurstLoadingFee_Burst");

                entity.HasOne(d => d.LoadingFee)
                    .WithMany(p => p.BurstLoadingFees)
                    .HasForeignKey(d => d.LoadingFeeId)
                    .HasConstraintName("FK_BurstLoadingFee_LoadingFee");
            });

            modelBuilder.Entity<BurstNotification>(entity =>
            {
                entity.ToTable("BurstNotifications", "Ops");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.DateFirstNotificationSent).HasColumnType("datetime");

                entity.Property(e => e.DateSecondNotificationSent).HasColumnType("datetime");

                entity.Property(e => e.FirstNotificationDateOfBurst).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.SecondNotificationDateOfBurst).HasColumnType("datetime");
            });

            modelBuilder.Entity<BurstNotificationStore>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("BurstNotificationStores", "Ops");

                entity.Property(e => e.CancellationNoticeSent).HasColumnName("cancellationNoticeSent");

                entity.Property(e => e.FirstNotificationSentDate).HasColumnType("datetime");

                entity.Property(e => e.HasBeenRemoved).HasColumnName("hasBeenRemoved");

                entity.Property(e => e.IsNewlyAdded).HasColumnName("isNewlyAdded");

                entity.Property(e => e.SecondNotificationSentDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<BurstNotificationStoresRemoved>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("BurstNotificationStoresRemoved", "Ops");

                entity.Property(e => e.CancellationNoticeSent).HasColumnName("cancellationNoticeSent");

                entity.Property(e => e.FirstNotificationSentDate).HasColumnType("datetime");

                entity.Property(e => e.HasBeenRemoved).HasColumnName("hasBeenRemoved");

                entity.Property(e => e.IsNewlyAdded).HasColumnName("isNewlyAdded");

                entity.Property(e => e.SecondNotificationSentDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<BurstPcaStatus>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("BurstPcaStatus", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Burst)
                    .WithMany()
                    .HasForeignKey(d => d.BurstId)
                    .HasConstraintName("FK_BurstPcaStatus_Burst");

                entity.HasOne(d => d.PcaStatus)
                    .WithMany()
                    .HasForeignKey(d => d.PcaStatusId)
                    .HasConstraintName("FK_BurstPcaStatus_PcaStatus");
            });

            modelBuilder.Entity<Button>(entity =>
            {
                entity.ToTable("button", "productlocator");

                entity.Property(e => e.Buttonid)
                    .HasColumnName("buttonid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Buttondescription)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("buttondescription")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Buttonlabel)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("buttonlabel")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Buttonnumber).HasColumnName("buttonnumber");

                entity.Property(e => e.Contractid).HasColumnName("contractid");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.Buttons)
                    .HasForeignKey(d => d.Contractid)
                    .HasConstraintName("FK_button_Contract");
            });

            modelBuilder.Entity<Buttondatum>(entity =>
            {
                entity.HasKey(e => e.Buttonid);

                entity.ToTable("buttondata", "productlocator");

                entity.Property(e => e.Buttonid)
                    .HasColumnName("buttonid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Buttonpresses).HasColumnName("buttonpresses");

                entity.Property(e => e.Dateprocessed)
                    .HasColumnType("datetime")
                    .HasColumnName("dateprocessed")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Button)
                    .WithOne(p => p.Buttondatum)
                    .HasForeignKey<Buttondatum>(d => d.Buttonid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_buttondata_button");
            });

            modelBuilder.Entity<Buttonreceiver>(entity =>
            {
                entity.HasKey(e => new { e.Buttonid, e.Receiverid });

                entity.ToTable("buttonreceiver", "productlocator");

                entity.Property(e => e.Buttonid).HasColumnName("buttonid");

                entity.Property(e => e.Receiverid).HasColumnName("receiverid");

                entity.HasOne(d => d.Button)
                    .WithMany(p => p.Buttonreceivers)
                    .HasForeignKey(d => d.Buttonid)
                    .HasConstraintName("FK_buttonreceiver_button");

                entity.HasOne(d => d.Receiver)
                    .WithMany(p => p.Buttonreceivers)
                    .HasForeignKey(d => d.Receiverid)
                    .HasConstraintName("FK_buttonreceiver_receiver");
            });

            modelBuilder.Entity<Category>(entity =>
            {
                entity.ToTable("Category", "Inventory");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.CategoryName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("createdBy");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasColumnName("creationDate")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DeletedBy)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("deletedBy");

                entity.Property(e => e.DeletionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("deletionDate");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasColumnName("isActive")
                    .HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<Category1>(entity =>
            {
                entity.HasKey(e => e.CategoryId)
                    .HasName("PK_Subcategory");

                entity.ToTable("Category", "Store");

                entity.HasIndex(e => e.CategoryName, "UniqueCategoryName")
                    .IsUnique();

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");
            });

            modelBuilder.Entity<Chain>(entity =>
            {
                entity.ToTable("Chain", "Store");

                entity.HasIndex(e => e.ChainName, "UniqueChainName")
                    .IsUnique();

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ChainTypeId)
                    .HasColumnName("ChainTypeID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ParentChainId)
                    .HasColumnName("ParentChainID")
                    .HasDefaultValueSql("((-1))");

                entity.Property(e => e.ScannerGroup)
                    .HasMaxLength(150)
                    .HasColumnName("Scanner_Group");

                entity.HasOne(d => d.ChainType)
                    .WithMany(p => p.Chains)
                    .HasForeignKey(d => d.ChainTypeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Chain_ChainType");

                entity.HasOne(d => d.ParentChain)
                    .WithMany(p => p.InverseParentChain)
                    .HasForeignKey(d => d.ParentChainId)
                    .HasConstraintName("FK_Chain_Chain");
            });

            modelBuilder.Entity<ChainGroup>(entity =>
            {
                entity.ToTable("ChainGroup", "Store");

                entity.Property(e => e.ChainGroupName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");
            });

            modelBuilder.Entity<ChainGroupChain>(entity =>
            {
                entity.ToTable("ChainGroupChain", "Store");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.ChainName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.ChainGroup)
                    .WithMany(p => p.ChainGroupChains)
                    .HasForeignKey(d => d.ChainGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChainGroupChain_ChainGroup");

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.ChainGroupChains)
                    .HasForeignKey(d => d.ChainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChainGroupChain_Chain");
            });

            modelBuilder.Entity<ChainGroupStore>(entity =>
            {
                entity.HasKey(e => new { e.StoreId, e.GroupChainId });

                entity.ToTable("ChainGroupStore", "Store");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.GroupChainId).HasColumnName("GroupChainID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.HasOne(d => d.GroupChain)
                    .WithMany(p => p.ChainGroupStores)
                    .HasForeignKey(d => d.GroupChainId)
                    .HasConstraintName("FK_ChainGroupStore_GroupChain");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.ChainGroupStores)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChainGroupStore_Chain");
            });

            modelBuilder.Entity<ChainPermission>(entity =>
            {
                entity.ToTable("ChainPermission", "Store");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.UserId).HasColumnName("UserID");
            });

            modelBuilder.Entity<ChainSelectForPaymentClassification>(entity =>
            {
                entity.ToTable("ChainSelectForPaymentClassification", "TradeRights");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.PaymentClassificationId).HasColumnName("PaymentClassificationID");

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.ChainSelectForPaymentClassifications)
                    .HasForeignKey(d => d.ChainId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("fk_ChainSelectForPaymentClassification_ChainID");

                entity.HasOne(d => d.PaymentClassification)
                    .WithMany(p => p.ChainSelectForPaymentClassifications)
                    .HasForeignKey(d => d.PaymentClassificationId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("fk_ChainSelectForPaymentClassification_PaymentClassificationID");
            });

            modelBuilder.Entity<ChainTarget>(entity =>
            {
                entity.HasKey(e => new { e.ChainId, e.PeriodId });

                entity.ToTable("ChainTargets", "Store");

                entity.Property(e => e.Amount).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.ChainTargetId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.ChainTargets)
                    .HasForeignKey(d => d.ChainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChainTargets_Chain");

                entity.HasOne(d => d.Period)
                    .WithMany(p => p.ChainTargets)
                    .HasForeignKey(d => d.PeriodId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChainTargets_Period");
            });

            modelBuilder.Entity<ChainType>(entity =>
            {
                entity.ToTable("ChainType", "Store");

                entity.HasIndex(e => e.ChainTypeName, "UniqueChainTypeName")
                    .IsUnique();

                entity.Property(e => e.ChainTypeId).HasColumnName("ChainTypeID");

                entity.Property(e => e.ChainTypeName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<ChainsToIncludeForStorePayment>(entity =>
            {
                entity.HasKey(e => e.ChainId)
                    .HasName("PK_ChainsToExcludeFromStorePayments");

                entity.ToTable("ChainsToIncludeForStorePayments", "TradeRights");

                entity.Property(e => e.ChainId)
                    .ValueGeneratedNever()
                    .HasColumnName("ChainID");

                entity.HasOne(d => d.Chain)
                    .WithOne(p => p.ChainsToIncludeForStorePayment)
                    .HasForeignKey<ChainsToIncludeForStorePayment>(d => d.ChainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChainsToIncludeForStorePayments_Chain");
            });

            modelBuilder.Entity<City>(entity =>
            {
                entity.ToTable("City");

                entity.HasIndex(e => e.CityName, "UniqueCityName")
                    .IsUnique();

                entity.Property(e => e.CityId).HasColumnName("CityID");

                entity.Property(e => e.CityName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<Classification>(entity =>
            {
                entity.ToTable("Classification", "Client");

                entity.HasIndex(e => e.ClassificationName, "UniqueClassificationName")
                    .IsUnique();

                entity.Property(e => e.ClassificationId).HasColumnName("ClassificationID");

                entity.Property(e => e.ClassificationName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<ClicksBarcode>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("Clicks_barcode", "Scanner");

                entity.Property(e => e.ArticleNo)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Barcode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BrandDescription)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BrandType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Category)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Country)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasComputedColumnSql("(CONVERT([varchar](10),getdate(),(120)))", false);

                entity.Property(e => e.Department)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.PackSize)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PacksInCase)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PriceRegion)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Province)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Sales)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Segment)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SubCategory)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Units)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WeekEndDate)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ClicksScannerDataToolAllRawDatum>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_AllRawData", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ArticleNo)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.BrandDescription).IsUnicode(false);

                entity.Property(e => e.BrandType)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Category)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Country)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Department)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .IsRequired()
                    .IsUnicode(false);

                entity.Property(e => e.FileName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.PriceRegion)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Province)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Sales).HasColumnType("money");

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.SubCategory)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.WeekendDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<ClicksScannerDataToolBarcodeRepository>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_BarcodeRepository", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.SizeDesc)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.Variant)
                    .HasMaxLength(200)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ClicksScannerDataToolBarcodeRequest>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_BarcodeRequest", "Scanner");

                entity.HasIndex(e => new { e.Id, e.Barcode }, "idx_BarcodeRequest")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Group)
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.IncludeInData).HasDefaultValueSql("((1))");

                entity.Property(e => e.IsCompetitor).HasColumnName("isCompetitor");

                entity.Property(e => e.Variant)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ClicksScannerDataToolDataRequest>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_DataRequest", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.PostPeriodEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_EndDate");

                entity.Property(e => e.PostPeriodEndDateWeekendEnding)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_EndDateWeekendEnding");

                entity.Property(e => e.PostPeriodIsDataReadyForPrc).HasColumnName("PostPeriod_isDataReadyForPRC");

                entity.Property(e => e.PostPeriodIsDataSubmittedForPrc).HasColumnName("PostPeriod_isDataSubmittedForPRC");

                entity.Property(e => e.PostPeriodIsSelectedForScannerData).HasColumnName("PostPeriod_isSelectedForScannerData");

                entity.Property(e => e.PostPeriodReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_ReadyDate");

                entity.Property(e => e.PostPeriodSelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_SelectedForScannerData_Date");

                entity.Property(e => e.PostPeriodStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_StartDate");

                entity.Property(e => e.PostPeriodStartDateWeekendEnding)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_StartDateWeekendEnding");

                entity.Property(e => e.PostPeriodSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_SubmissionDate");

                entity.Property(e => e.PriorPeriodEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_EndDate");

                entity.Property(e => e.PriorPeriodEndDateWeekendEnding)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_EndDateWeekendEnding");

                entity.Property(e => e.PriorPeriodIsDataReadyForPrc).HasColumnName("PriorPeriod_isDataReadyForPRC");

                entity.Property(e => e.PriorPeriodIsDataSubmittedForPrc).HasColumnName("PriorPeriod_isDataSubmittedForPRC");

                entity.Property(e => e.PriorPeriodIsSelectedForScannerData).HasColumnName("PriorPeriod_isSelectedForScannerData");

                entity.Property(e => e.PriorPeriodReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_ReadyDate");

                entity.Property(e => e.PriorPeriodSelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_SelectedForScannerData_Date");

                entity.Property(e => e.PriorPeriodStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_StartDate");

                entity.Property(e => e.PriorPeriodStartDateWeekendEnding)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_StartDateWeekendEnding");

                entity.Property(e => e.PriorPeriodSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_SubmissionDate");

                entity.Property(e => e.PriorYearEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_EndDate");

                entity.Property(e => e.PriorYearEndDateWeekendEnding)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_EndDateWeekendEnding");

                entity.Property(e => e.PriorYearIsDataReadyForPrc).HasColumnName("PriorYear_isDataReadyForPRC");

                entity.Property(e => e.PriorYearIsDataSubmittedForPrc).HasColumnName("PriorYear_isDataSubmittedForPRC");

                entity.Property(e => e.PriorYearIsSelectedForScannerData).HasColumnName("PriorYear_isSelectedForScannerData");

                entity.Property(e => e.PriorYearReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_ReadyDate");

                entity.Property(e => e.PriorYearSelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_SelectedForScannerData_Date");

                entity.Property(e => e.PriorYearStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_StartDate");

                entity.Property(e => e.PriorYearStartDateWeekendEnding)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_StartDateWeekendEnding");

                entity.Property(e => e.PriorYearSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_SubmissionDate");

                entity.Property(e => e.RemoveMediaStores).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<ClicksScannerDataToolDegenerateRawDatum>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_DegenerateRawData", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ArticleNo)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.BrandDescription).IsUnicode(false);

                entity.Property(e => e.BrandType)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Category)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Country)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Department)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .IsRequired()
                    .IsUnicode(false);

                entity.Property(e => e.FileName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.PriceRegion)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Province)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Sales).HasColumnType("money");

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.SubCategory)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.WeekendDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<ClicksScannerDataToolLoadedDatum>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_LoadedData", "Scanner");

                entity.HasIndex(e => new { e.Id, e.ContractNumber }, "idx_LoadedData")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ArticleNo)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.BrandDescription).IsUnicode(false);

                entity.Property(e => e.BrandType)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Category)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ContractNumber)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.Country)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Department)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .IsRequired()
                    .IsUnicode(false);

                entity.Property(e => e.FileName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Group)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.IsDateValid)
                    .HasColumnName("isDateValid")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.Period)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.PriceRegion)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Province)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Sales).HasColumnType("money");

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.SubCategory)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.WeekendDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<ClicksScannerDataToolRollForwardRequest>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_RollForwardRequest", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.IsDataReadyForPcr).HasColumnName("isDataReadyForPCR");

                entity.Property(e => e.IsSubmittedForScannerData).HasColumnName("isSubmittedForScannerData");

                entity.Property(e => e.NonPromoEndDate).HasColumnType("datetime");

                entity.Property(e => e.NonPromoStartDate).HasColumnType("datetime");

                entity.Property(e => e.PromoEndDate).HasColumnType("datetime");

                entity.Property(e => e.PromoStartDate).HasColumnType("datetime");

                entity.Property(e => e.ReadyForPcrDate)
                    .HasColumnType("datetime")
                    .HasColumnName("ReadyForPCR_date");

                entity.Property(e => e.SubmissionDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<ClicksScannerDataToolStoreLink>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_StoreLink", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.CreateDate).HasColumnType("datetime");

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.StoreCodeClicks)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("StoreCode_Clicks");

                entity.Property(e => e.StoreCodeInstore)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("StoreCode_Instore");

                entity.Property(e => e.StoreIdClicks).HasColumnName("StoreId_Clicks");

                entity.Property(e => e.StoreIdInstore).HasColumnName("StoreId_Instore");
            });

            modelBuilder.Entity<ClicksScannerDataToolStoreList>(entity =>
            {
                entity.ToTable("CLICKS_ScannerDataTool_StoreList", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Country)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Province)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<Client>(entity =>
            {
                entity.ToTable("Client", "Client");

                entity.HasIndex(e => e.ClientName, "UniqueClientName")
                    .IsUnique();

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.AccountNumber)
                    .IsRequired()
                    .HasMaxLength(8)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.AddressLine1)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.AddressLine2)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CityId)
                    .HasColumnName("CityID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.ClassificationId).HasColumnName("ClassificationID");

                entity.Property(e => e.ClientAbbreviation)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Fax)
                    .IsRequired()
                    .HasMaxLength(10);

                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PostalCode)
                    .IsRequired()
                    .HasMaxLength(4)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Telephone)
                    .IsRequired()
                    .HasMaxLength(10);

                entity.Property(e => e.TermsId)
                    .HasColumnName("TermsID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Vatnumber)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("VATNumber")
                    .HasDefaultValueSql("('')")
                    .IsFixedLength(true);

                entity.HasOne(d => d.City)
                    .WithMany(p => p.Clients)
                    .HasForeignKey(d => d.CityId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Client_City");

                entity.HasOne(d => d.Classification)
                    .WithMany(p => p.Clients)
                    .HasForeignKey(d => d.ClassificationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Client_Class");

                entity.HasOne(d => d.Terms)
                    .WithMany(p => p.Clients)
                    .HasForeignKey(d => d.TermsId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Client_Terms");
            });

            modelBuilder.Entity<ClientAccountManager>(entity =>
            {
                entity.HasKey(e => new { e.ClientId, e.EffectiveDate });

                entity.ToTable("ClientAccountManager", "Client");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.AccountManager)
                    .WithMany(p => p.ClientAccountManagers)
                    .HasForeignKey(d => d.AccountManagerId)
                    .HasConstraintName("FK_ClientAccountManager_AccountManager");

                entity.HasOne(d => d.Client)
                    .WithMany(p => p.ClientAccountManagers)
                    .HasForeignKey(d => d.ClientId)
                    .HasConstraintName("FK_ClientAccountManager_Client");
            });

            modelBuilder.Entity<ClientBrand>(entity =>
            {
                entity.HasKey(e => new { e.ClientId, e.BrandId });

                entity.ToTable("ClientBrand", "Client");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.ClientBrands)
                    .HasForeignKey(d => d.BrandId)
                    .HasConstraintName("FK_ClientBrand_Brand");

                entity.HasOne(d => d.Client)
                    .WithMany(p => p.ClientBrands)
                    .HasForeignKey(d => d.ClientId)
                    .HasConstraintName("FK_ClientBrand_Client");
            });

            modelBuilder.Entity<Console>(entity =>
            {
                entity.ToTable("console", "productlocator");

                entity.Property(e => e.Consoleid)
                    .HasColumnName("consoleid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Contractid).HasColumnName("contractid");

                entity.Property(e => e.Dateprogrammed)
                    .HasColumnType("datetime")
                    .HasColumnName("dateprogrammed")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Programmedbyuserid).HasColumnName("programmedbyuserid");

                entity.Property(e => e.Programmingconfirmed).HasColumnName("programmingconfirmed");

                entity.Property(e => e.Storeid).HasColumnName("storeid");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.Consoles)
                    .HasForeignKey(d => d.Contractid)
                    .HasConstraintName("FK_console_Contract");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.Consoles)
                    .HasForeignKey(d => d.Storeid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_console_Store");
            });

            modelBuilder.Entity<Contract>(entity =>
            {
                entity.ToTable("Contract", "Sales");

                entity.HasIndex(e => e.ContractNumber, "UniqueContractNumber")
                    .IsUnique()
                    .HasFillFactor((byte)75);

                entity.Property(e => e.ContractId)
                    .HasColumnName("ContractID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.AgencyCommPercentage).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.AgencyId).HasColumnName("AgencyID");

                entity.Property(e => e.AgencyName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.AllowCancelAndReplace).HasDefaultValueSql("((0))");

                entity.Property(e => e.BillingInstructions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CancelDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.CancelledBy)
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.ClientBillingAddress)
                    .IsRequired()
                    .HasMaxLength(1000);

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.ClonedContractNumber)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.ContractDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(((1)-(1))-(1900))");

                entity.Property(e => e.ContractNotes)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.ContractType)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DemoOwner)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.DemoProvider)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.IsCloned)
                    .HasColumnName("isCloned")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.IsReplacement)
                    .HasColumnName("isReplacement")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.ProjectName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.ReasonsForCloningContract)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.SignDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.SpecialConditions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.HasOne(d => d.AccountManager)
                    .WithMany(p => p.Contracts)
                    .HasForeignKey(d => d.AccountManagerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Contract_AccountManager");

                entity.HasOne(d => d.Agency)
                    .WithMany(p => p.ContractAgencies)
                    .HasForeignKey(d => d.AgencyId)
                    .HasConstraintName("FK_Contract_Agency");

                entity.HasOne(d => d.Client)
                    .WithMany(p => p.ContractClients)
                    .HasForeignKey(d => d.ClientId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Contract_Client");

                entity.HasOne(d => d.ContractClassification)
                    .WithMany(p => p.Contracts)
                    .HasForeignKey(d => d.ContractClassificationId)
                    .HasConstraintName("FK_Contract_ContractClassification");

                entity.HasOne(d => d.ContractProposalHeat)
                    .WithMany(p => p.Contracts)
                    .HasForeignKey(d => d.ContractProposalHeatId)
                    .HasConstraintName("FK_Contract_ContractProposalHeat");
            });

            modelBuilder.Entity<ContractApproved>(entity =>
            {
                entity.HasKey(e => e.ContractId);

                entity.ToTable("ContractApproved", "Sales");

                entity.Property(e => e.ContractId)
                    .ValueGeneratedNever()
                    .HasColumnName("ContractID");

                entity.Property(e => e.Approved)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<ContractClassification>(entity =>
            {
                entity.ToTable("ContractClassification", "Sales");

                entity.Property(e => e.Classification)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<ContractCostEstimate>(entity =>
            {
                entity.HasKey(e => e.CostEstimateId);

                entity.ToTable("ContractCostEstimates", "Sales");

                entity.Property(e => e.CostEstimateId).HasColumnName("CostEstimateID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CostEstimateAmount)
                    .HasColumnType("decimal(18, 2)")
                    .HasDefaultValueSql("((0.00))");

                entity.Property(e => e.CostEstimateNumber)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ContractCostEstimates)
                    .HasForeignKey(d => d.ContractId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractCostEstimates_Contract");
            });

            modelBuilder.Entity<ContractDate>(entity =>
            {
                entity.HasKey(e => e.ContractId);

                entity.ToTable("ContractDate", "Sales");

                entity.Property(e => e.ContractId)
                    .ValueGeneratedNever()
                    .HasColumnName("ContractID");

                entity.Property(e => e.Artwork).HasColumnType("datetime");

                entity.Property(e => e.InstallationInstructions).HasColumnType("datetime");

                entity.Property(e => e.Ponumber)
                    .HasColumnType("datetime")
                    .HasColumnName("PONumber");

                entity.Property(e => e.StoreList).HasColumnType("datetime");

                entity.HasOne(d => d.Contract)
                    .WithOne(p => p.ContractDateNavigation)
                    .HasForeignKey<ContractDate>(d => d.ContractId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractDate_Contract");
            });

            modelBuilder.Entity<ContractInventoryQty>(entity =>
            {
                entity.HasKey(e => e.ContractItemQtyId);

                entity.ToTable("ContractInventoryQty", "Sales");

                entity.Property(e => e.ContractItemQtyId)
                    .HasColumnName("ContractItemQtyID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ItemQtyId).HasColumnName("ItemQtyID");

                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.SellPrice).HasColumnType("money");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.ContractInventoryQties)
                    .HasForeignKey(d => d.BrandId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractInventoryQty_Brand");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ContractInventoryQties)
                    .HasForeignKey(d => d.ContractId)
                    .HasConstraintName("FK_ContractInventoryQty_Contract");

                entity.HasOne(d => d.ItemQty)
                    .WithMany(p => p.ContractInventoryQties)
                    .HasForeignKey(d => d.ItemQtyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractInventoryQty_InventoryQty");
            });

            modelBuilder.Entity<ContractInvoice>(entity =>
            {
                entity.HasKey(e => e.ContractInvoiceNumber);

                entity.ToTable("ContractInvoices", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.InvoiceAmount)
                    .HasColumnType("decimal(18, 2)")
                    .HasDefaultValueSql("((0.00))");

                entity.Property(e => e.InvoiceNumber)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ContractInvoices)
                    .HasForeignKey(d => d.ContractId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractInvoices_Contract");
            });

            modelBuilder.Entity<ContractMediaCost>(entity =>
            {
                entity.ToTable("ContractMediaCost", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.MediaCost).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.HasOne(d => d.Burst)
                    .WithMany(p => p.ContractMediaCosts)
                    .HasForeignKey(d => d.BurstId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractMediaCost_Burst");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ContractMediaCosts)
                    .HasForeignKey(d => d.ContractId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractMediaCost_Contract");

              
            });

            modelBuilder.Entity<ContractMiscellaneousCharge>(entity =>
            {
                entity.ToTable("ContractMiscellaneousCharge", "Sales");

                entity.Property(e => e.ContractMiscellaneousChargeId)
                    .HasColumnName("ContractMiscellaneousChargeID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.MiscellaneousChargeAmount).HasColumnType("money");

                entity.Property(e => e.MiscellaneousChargeId).HasColumnName("MiscellaneousChargeID");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ContractMiscellaneousCharges)
                    .HasForeignKey(d => d.ContractId)
                    .HasConstraintName("FK_ContractMiscellaneousCharge_Contract");

                entity.HasOne(d => d.MiscellaneousCharge)
                    .WithMany(p => p.ContractMiscellaneousCharges)
                    .HasForeignKey(d => d.MiscellaneousChargeId)
                    .HasConstraintName("FK_ContractMiscellaneousCharge_MiscellaneousCharge");
            });

            modelBuilder.Entity<ContractProduct>(entity =>
            {
                entity.HasKey(e => new { e.ContractId, e.ProductId, e.PackSizeId });

                entity.ToTable("ContractProduct", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ContractProducts)
                    .HasForeignKey(d => d.ContractId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractProduct_Contract");

                entity.HasOne(d => d.PackSize)
                    .WithMany(p => p.ContractProducts)
                    .HasForeignKey(d => d.PackSizeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractProduct_PackSize");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ContractProducts)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContractProduct_Product");
            });

            modelBuilder.Entity<ContractProposalHeat>(entity =>
            {
                entity.ToTable("ContractProposalHeat", "Sales");

                entity.Property(e => e.ContractProposalHeatName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<CrmClient>(entity =>
            {
                entity.ToTable("CRM_Client", "CRM");

                entity.Property(e => e.CrmClientId).HasColumnName("CRM_ClientID");

                entity.Property(e => e.AccountType).HasMaxLength(20);

                entity.Property(e => e.AddressLine1).HasMaxLength(100);

                entity.Property(e => e.AddressLine2).HasMaxLength(100);

                entity.Property(e => e.CityId).HasColumnName("CityID");

                entity.Property(e => e.ClassificationId).HasColumnName("ClassificationID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ClientName).HasMaxLength(150);

                entity.Property(e => e.CreatedBy).HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.Fax).HasMaxLength(10);

                entity.Property(e => e.ModBy)
                    .HasMaxLength(50)
                    .HasColumnName("Mod_by");

                entity.Property(e => e.ModDt)
                    .HasColumnType("datetime")
                    .HasColumnName("Mod_dt");

                entity.Property(e => e.Notes).HasMaxLength(2000);

                entity.Property(e => e.PostalCode).HasMaxLength(4);

                entity.Property(e => e.Telephone).HasMaxLength(10);

                entity.Property(e => e.Vatnumber)
                    .HasMaxLength(10)
                    .HasColumnName("VATNumber")
                    .IsFixedLength(true);
            });

            modelBuilder.Entity<CrmContact>(entity =>
            {
                entity.HasKey(e => e.ContactId);

                entity.ToTable("CRM_Contact", "CRM");

                entity.Property(e => e.ContactId).HasColumnName("ContactID");

                entity.Property(e => e.ContactDesignation)
                    .HasMaxLength(50)
                    .HasColumnName("Contact_Designation");

                entity.Property(e => e.ContactEmail)
                    .HasMaxLength(50)
                    .HasColumnName("Contact_Email");

                entity.Property(e => e.ContactFax)
                    .HasMaxLength(50)
                    .HasColumnName("Contact_Fax");

                entity.Property(e => e.ContactName)
                    .HasMaxLength(50)
                    .HasColumnName("Contact_Name");

                entity.Property(e => e.ContactPrimary).HasColumnName("Contact_Primary");

                entity.Property(e => e.ContactTel)
                    .HasMaxLength(50)
                    .HasColumnName("Contact_Tel");

                entity.Property(e => e.CrmClientId).HasColumnName("CRM_ClientID");
            });

            modelBuilder.Entity<CrmContactDetail>(entity =>
            {
                entity.HasKey(e => e.ContactDetailId);

                entity.ToTable("CRM_ContactDetails", "CRM");

                entity.Property(e => e.ContactDetailId).HasColumnName("ContactDetailID");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.Brand).HasMaxLength(50);

                entity.Property(e => e.CancellationStatus)
                    .HasMaxLength(200)
                    .HasColumnName("Cancellation_Status");

                entity.Property(e => e.ContactDate).HasColumnType("datetime");

                entity.Property(e => e.ContactId).HasColumnName("ContactID");

                entity.Property(e => e.ContactStatusId).HasColumnName("ContactStatusID");

                entity.Property(e => e.Country).HasMaxLength(30);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(20)
                    .HasColumnName("Created_by");

                entity.Property(e => e.CreatedDt)
                    .HasColumnType("datetime")
                    .HasColumnName("Created_dt");

                entity.Property(e => e.CrmClientId).HasColumnName("CRM_ClientID");

                entity.Property(e => e.EndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("end_date");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.ModBy)
                    .HasMaxLength(20)
                    .HasColumnName("Mod_by");

                entity.Property(e => e.ModDt)
                    .HasColumnType("datetime")
                    .HasColumnName("mod_dt");

                entity.Property(e => e.ProposalLikelyness)
                    .HasMaxLength(10)
                    .HasColumnName("Proposal_Likelyness");

                entity.Property(e => e.ProposalStatus)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("Proposal_Status");

                entity.Property(e => e.StartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("start_date");

                entity.Property(e => e.WeeklyRate)
                    .HasColumnType("money")
                    .HasColumnName("weekly_rate");
            });

            modelBuilder.Entity<CrmContactStatus>(entity =>
            {
                entity.HasKey(e => e.ContactStatusId);

                entity.ToTable("CRM_Contact_Status", "CRM");

                entity.Property(e => e.ContactStatusId)
                    .ValueGeneratedNever()
                    .HasColumnName("ContactStatusID");

                entity.Property(e => e.StatusDesc).HasMaxLength(50);
            });

            modelBuilder.Entity<CrmLoginTracker>(entity =>
            {
                entity.HasKey(e => e.LoginId);

                entity.ToTable("CRM_Login_Tracker", "CRM");

                entity.Property(e => e.LoginId).HasColumnName("LoginID");

                entity.Property(e => e.LoginDate)
                    .HasColumnType("datetime")
                    .HasColumnName("Login_date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.LoginName)
                    .HasMaxLength(50)
                    .HasColumnName("Login_name");
            });

            modelBuilder.Entity<DailyFiguresRevenue>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("DailyFiguresRevenue", "reporting");

                entity.Property(e => e.ContractNumber).HasMaxLength(8);

                entity.Property(e => e._201601)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-01");

                entity.Property(e => e._201602)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-02");

                entity.Property(e => e._201603)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-03");

                entity.Property(e => e._201604)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-04");

                entity.Property(e => e._201605)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-05");

                entity.Property(e => e._201606)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-06");

                entity.Property(e => e._201607)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-07");

                entity.Property(e => e._201608)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-08");

                entity.Property(e => e._201609)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-09");

                entity.Property(e => e._201610)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-10");

                entity.Property(e => e._201611)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-11");

                entity.Property(e => e._201612)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-12");

                entity.Property(e => e._201701)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-01");

                entity.Property(e => e._201702)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-02");

                entity.Property(e => e._201703)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-03");

                entity.Property(e => e._201704)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-04");

                entity.Property(e => e._201705)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-05");

                entity.Property(e => e._201706)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-06");

                entity.Property(e => e._201707)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-07");

                entity.Property(e => e._201708)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-08");

                entity.Property(e => e._201709)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-09");

                entity.Property(e => e._201710)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-10");

                entity.Property(e => e._201711)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-11");

                entity.Property(e => e._201712)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-12");

                entity.Property(e => e._201801)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-01");

                entity.Property(e => e._201802)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-02");

                entity.Property(e => e._201803)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-03");

                entity.Property(e => e._201804)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-04");

                entity.Property(e => e._201805)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-05");

                entity.Property(e => e._201806)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-06");

                entity.Property(e => e._201807)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-07");

                entity.Property(e => e._201808)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-08");

                entity.Property(e => e._201809)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-09");

                entity.Property(e => e._201810)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-10");

                entity.Property(e => e._201811)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-11");

                entity.Property(e => e._201812)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-12");

                entity.Property(e => e._201901)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-01");

                entity.Property(e => e._201902)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-02");

                entity.Property(e => e._201903)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-03");

                entity.Property(e => e._201904)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-04");

                entity.Property(e => e._201905)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-05");

                entity.Property(e => e._201906)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-06");

                entity.Property(e => e._201907)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-07");

                entity.Property(e => e._201908)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-08");

                entity.Property(e => e._201909)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-09");

                entity.Property(e => e._201910)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-10");

                entity.Property(e => e._201911)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-11");

                entity.Property(e => e._201912)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-12");

                entity.Property(e => e._202001)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-01");

                entity.Property(e => e._202002)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-02");

                entity.Property(e => e._202003)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-03");

                entity.Property(e => e._202004)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-04");

                entity.Property(e => e._202005)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-05");

                entity.Property(e => e._202006)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-06");

                entity.Property(e => e._202007)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-07");

                entity.Property(e => e._202008)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-08");

                entity.Property(e => e._202009)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-09");

                entity.Property(e => e._202010)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-10");

                entity.Property(e => e._202011)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-11");

                entity.Property(e => e._202012)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2020-12");

                entity.Property(e => e._202101)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-01");

                entity.Property(e => e._202102)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-02");

                entity.Property(e => e._202103)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-03");

                entity.Property(e => e._202104)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-04");

                entity.Property(e => e._202105)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-05");

                entity.Property(e => e._202106)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-06");

                entity.Property(e => e._202107)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-07");

                entity.Property(e => e._202108)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-08");

                entity.Property(e => e._202109)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-09");

                entity.Property(e => e._202110)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-10");

                entity.Property(e => e._202111)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-11");

                entity.Property(e => e._202112)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2021-12");
            });

            modelBuilder.Entity<DateCalendar>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("DateCalendar");

                entity.Property(e => e.CalendarDate)
                    .HasColumnType("datetime")
                    .HasColumnName("calendar_date");

                entity.Property(e => e.CalendarDay).HasColumnName("calendar_day");

                entity.Property(e => e.CalendarMonth).HasColumnName("calendar_month");

                entity.Property(e => e.CalendarQuarter).HasColumnName("calendar_quarter");

                entity.Property(e => e.CalendarYear).HasColumnName("calendar_year");

                entity.Property(e => e.DayName)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("day_name");

                entity.Property(e => e.DayOfWeek).HasColumnName("day_of_week");

                entity.Property(e => e.DayOfYear).HasColumnName("day_of_year");

                entity.Property(e => e.DaysInMonth).HasColumnName("days_in_month");

                entity.Property(e => e.DaysInQuarter).HasColumnName("days_in_quarter");

                entity.Property(e => e.FirstDayInMonth)
                    .HasColumnType("datetime")
                    .HasColumnName("first_day_in_month");

                entity.Property(e => e.FirstDayInQuarter)
                    .HasColumnType("datetime")
                    .HasColumnName("first_day_in_quarter");

                entity.Property(e => e.FirstDayInWeek)
                    .HasColumnType("datetime")
                    .HasColumnName("first_day_in_week");

                entity.Property(e => e.IsLastDayInMonth).HasColumnName("is_last_day_in_month");

                entity.Property(e => e.IsLastDayInQuarter).HasColumnName("is_last_day_in_quarter");

                entity.Property(e => e.IsLeapYear).HasColumnName("is_leap_year");

                entity.Property(e => e.IsWeekInSameMonth).HasColumnName("is_week_in_same_month");

                entity.Property(e => e.IsWeekday).HasColumnName("is_weekday");

                entity.Property(e => e.LastDayInMonth)
                    .HasColumnType("datetime")
                    .HasColumnName("last_day_in_month");

                entity.Property(e => e.LastDayInQuarter)
                    .HasColumnType("datetime")
                    .HasColumnName("last_day_in_quarter");

                entity.Property(e => e.LastDayInWeek)
                    .HasColumnType("datetime")
                    .HasColumnName("last_day_in_week");

                entity.Property(e => e.MonthDayNameInstance).HasColumnName("month_day_name_instance");

                entity.Property(e => e.MonthDaysRemaining).HasColumnName("month_days_remaining");

                entity.Property(e => e.MonthName)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("month_name");

                entity.Property(e => e.MonthWeekdaysCompleted).HasColumnName("month_weekdays_completed");

                entity.Property(e => e.MonthWeekdaysRemaining).HasColumnName("month_weekdays_remaining");

                entity.Property(e => e.QuarterDayNameInstance).HasColumnName("quarter_day_name_instance");

                entity.Property(e => e.QuarterDaysCompleted).HasColumnName("quarter_days_completed");

                entity.Property(e => e.QuarterDaysRemaining).HasColumnName("quarter_days_remaining");

                entity.Property(e => e.QuarterWeekdaysCompleted).HasColumnName("quarter_weekdays_completed");

                entity.Property(e => e.QuarterWeekdaysRemaining).HasColumnName("quarter_weekdays_remaining");

                entity.Property(e => e.WeekOfMonth).HasColumnName("week_of_month");

                entity.Property(e => e.WeekOfQuarter).HasColumnName("week_of_quarter");

                entity.Property(e => e.WeekOfYear).HasColumnName("week_of_year");

                entity.Property(e => e.WeekdaysInMonth).HasColumnName("weekdays_in_month");

                entity.Property(e => e.WeekdaysInQuarter).HasColumnName("weekdays_in_quarter");

                entity.Property(e => e.YearDayNameInstance).HasColumnName("year_day_name_instance");

                entity.Property(e => e.YearDaysRemaining).HasColumnName("year_days_remaining");

                entity.Property(e => e.YearMonth)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("year_month");

                entity.Property(e => e.YearQuarter)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("year_quarter");

                entity.Property(e => e.YearWeek)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("year_week");
            });

            modelBuilder.Entity<DateCalendarLuka>(entity =>
            {
                entity.HasKey(e => e.CalendarDate)
                    .HasName("PK_date_calendar_calendar_date");

                entity.ToTable("date_calendar_lukas");

                entity.HasIndex(e => e.CalendarMonth, "IX_date_calendar_calendar_month")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.CalendarQuarter, "IX_date_calendar_calendar_quarter")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.CalendarYear, "IX_date_calendar_calendar_year")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.DayOfWeek, "IX_date_calendar_day_of_week")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.FirstDayInWeek, "IX_date_calendar_first_day_in_week")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.IsWeekday, "IX_date_calendar_is_weekday")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.LastDayInWeek, "IX_date_calendar_last_day_in_week")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.YearMonth, "IX_date_calendar_year_month")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.YearQuarter, "IX_date_calendar_year_quarter")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.CalendarDate)
                    .HasColumnType("datetime")
                    .HasColumnName("calendar_date");

                entity.Property(e => e.CalendarDay).HasColumnName("calendar_day");

                entity.Property(e => e.CalendarMonth).HasColumnName("calendar_month");

                entity.Property(e => e.CalendarQuarter).HasColumnName("calendar_quarter");

                entity.Property(e => e.CalendarYear).HasColumnName("calendar_year");

                entity.Property(e => e.DayName)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("day_name");

                entity.Property(e => e.DayOfWeek).HasColumnName("day_of_week");

                entity.Property(e => e.DayOfYear).HasColumnName("day_of_year");

                entity.Property(e => e.DaysInMonth).HasColumnName("days_in_month");

                entity.Property(e => e.DaysInQuarter).HasColumnName("days_in_quarter");

                entity.Property(e => e.FirstDayInMonth)
                    .HasColumnType("datetime")
                    .HasColumnName("first_day_in_month");

                entity.Property(e => e.FirstDayInQuarter)
                    .HasColumnType("datetime")
                    .HasColumnName("first_day_in_quarter");

                entity.Property(e => e.FirstDayInWeek)
                    .HasColumnType("datetime")
                    .HasColumnName("first_day_in_week");

                entity.Property(e => e.IsLastDayInMonth).HasColumnName("is_last_day_in_month");

                entity.Property(e => e.IsLastDayInQuarter).HasColumnName("is_last_day_in_quarter");

                entity.Property(e => e.IsLeapYear).HasColumnName("is_leap_year");

                entity.Property(e => e.IsWeekInSameMonth).HasColumnName("is_week_in_same_month");

                entity.Property(e => e.IsWeekday).HasColumnName("is_weekday");

                entity.Property(e => e.LastDayInMonth)
                    .HasColumnType("datetime")
                    .HasColumnName("last_day_in_month");

                entity.Property(e => e.LastDayInQuarter)
                    .HasColumnType("datetime")
                    .HasColumnName("last_day_in_quarter");

                entity.Property(e => e.LastDayInWeek)
                    .HasColumnType("datetime")
                    .HasColumnName("last_day_in_week");

                entity.Property(e => e.MonthDayNameInstance).HasColumnName("month_day_name_instance");

                entity.Property(e => e.MonthDaysRemaining).HasColumnName("month_days_remaining");

                entity.Property(e => e.MonthName)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("month_name");

                entity.Property(e => e.MonthWeekdaysCompleted).HasColumnName("month_weekdays_completed");

                entity.Property(e => e.MonthWeekdaysRemaining).HasColumnName("month_weekdays_remaining");

                entity.Property(e => e.QuarterDayNameInstance).HasColumnName("quarter_day_name_instance");

                entity.Property(e => e.QuarterDaysCompleted).HasColumnName("quarter_days_completed");

                entity.Property(e => e.QuarterDaysRemaining).HasColumnName("quarter_days_remaining");

                entity.Property(e => e.QuarterWeekdaysCompleted).HasColumnName("quarter_weekdays_completed");

                entity.Property(e => e.QuarterWeekdaysRemaining).HasColumnName("quarter_weekdays_remaining");

                entity.Property(e => e.WeekOfMonth).HasColumnName("week_of_month");

                entity.Property(e => e.WeekOfQuarter).HasColumnName("week_of_quarter");

                entity.Property(e => e.WeekOfYear).HasColumnName("week_of_year");

                entity.Property(e => e.WeekdaysInMonth).HasColumnName("weekdays_in_month");

                entity.Property(e => e.WeekdaysInQuarter).HasColumnName("weekdays_in_quarter");

                entity.Property(e => e.YearDayNameInstance).HasColumnName("year_day_name_instance");

                entity.Property(e => e.YearDaysRemaining).HasColumnName("year_days_remaining");

                entity.Property(e => e.YearMonth)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("year_month");

                entity.Property(e => e.YearQuarter)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("year_quarter");

                entity.Property(e => e.YearWeek)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("year_week");
            });

            modelBuilder.Entity<DateTable>(entity =>
            {
                entity.HasKey(e => e.Date);

                entity.ToTable("DateTable", "CRM");

                entity.Property(e => e.Date).HasColumnType("datetime");

                entity.Property(e => e.Fyear)
                    .HasMaxLength(10)
                    .HasColumnName("FYear");

                entity.Property(e => e.Quater).HasMaxLength(10);
            });

            modelBuilder.Entity<DeletedMediaHistory>(entity =>
            {
                entity.ToTable("DeletedMediaHistory", "Media");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<DistributionCheck>(entity =>
            {
                entity.HasKey(e => e.CheckId);

                entity.ToTable("DistributionCheck", "Ops");

                entity.HasIndex(e => new { e.CheckDate, e.CheckId, e.VanId }, "_dta_index_DistributionCheck_7_1810157544__K3_K1_K2")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.CheckId)
                    .HasColumnName("CheckID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CheckDate).HasColumnType("date");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.OperatorName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.HasOne(d => d.Van)
                    .WithMany(p => p.DistributionChecks)
                    .HasForeignKey(d => d.VanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionCheck_Van");
            });

            modelBuilder.Entity<DistributionCheckDetail>(entity =>
            {
                entity.HasKey(e => new { e.CheckId, e.ProductId, e.PackSizeId, e.StoreId });

                entity.ToTable("DistributionCheckDetail", "Ops");

                entity.HasIndex(e => new { e.PackSizeId, e.ProductId, e.CheckId, e.StoreId }, "_dta_index_DistributionCheckDetail_7_1858157715__K3_K2_K1_K4_5")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.CheckId).HasColumnName("CheckID");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Check)
                    .WithMany(p => p.DistributionCheckDetails)
                    .HasForeignKey(d => d.CheckId)
                    .HasConstraintName("FK_DistributionCheckDetail_DistributionCheck");

                entity.HasOne(d => d.PackSize)
                    .WithMany(p => p.DistributionCheckDetails)
                    .HasForeignKey(d => d.PackSizeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionCheckDetail_PackSize");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.DistributionCheckDetails)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionCheckDetail_Product");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.DistributionCheckDetails)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DistributionCheckDetail_Store");
            });

            modelBuilder.Entity<DistributionRegion>(entity =>
            {
                entity.ToTable("DistributionRegion", "Ops");

                entity.HasIndex(e => e.DistributionRegionName, "UniqueDistributionRegionName")
                    .IsUnique();

                entity.Property(e => e.DistributionRegionId).HasColumnName("DistributionRegionID");

                entity.Property(e => e.DistributionRegionName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<EmailsToSend>(entity =>
            {
                entity.ToTable("EmailsToSend", "Ops");

                entity.Property(e => e.Id).HasColumnName("id");
            });

            modelBuilder.Entity<FinancialYearQuarter>(entity =>
            {
                entity.ToTable("FinancialYearQuarter", "Finance");

                entity.Property(e => e.FinancialYearQuarterId).HasColumnName("FinancialYearQuarterID");

                entity.Property(e => e.MonthName)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.QuarterName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.QuarterNameAbrr)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<FindDuplicate>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("FindDuplicates", "reporting");

                entity.Property(e => e.Approved).HasColumnName("approved");

                entity.Property(e => e.AssignedAm)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("AssignedAM");

                entity.Property(e => e.Brand)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Category)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ChainType)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.ContractAm)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("ContractAM");

                entity.Property(e => e.CurrentAm)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("CurrentAM");

                entity.Property(e => e.CurrentForecastRevenue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.MediaService)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Period).HasMaxLength(7);

                entity.Property(e => e.PriorActualRevenue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorForecastRevenue).HasColumnType("decimal(38, 6)");
            });

            modelBuilder.Entity<Fiscal>(entity =>
            {
                entity.ToTable("Fiscal", "Sales");

                entity.HasIndex(e => e.FiscalName, "UniqueFiscalName")
                    .IsUnique();

                entity.HasIndex(e => e.FiscalStartDate, "UniqueFiscalStartDate")
                    .IsUnique();

                entity.Property(e => e.FiscalId).HasColumnName("FiscalID");

                entity.Property(e => e.FiscalName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.FiscalStartDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<FranchiseRevenuePnp>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("FranchiseRevenuePNP", "reporting");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.HeadOfficeRentalDue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.Period)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.TotalPerStore).HasColumnType("decimal(19, 4)");

                entity.Property(e => e.TotalRentalDue).HasColumnType("decimal(38, 8)");

                entity.Property(e => e.TypeOfStore)
                    .IsRequired()
                    .HasMaxLength(9)
                    .IsUnicode(false);

                entity.Property(e => e.Week)
                    .HasColumnType("datetime")
                    .HasColumnName("week");

                entity.Property(e => e.WeeklyCrossoverRevenuePerBillableWeekPerStore).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.WeeklyRevenuePerBillableWeekPerStore).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<FranchiseRevenuePnpdistinct>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("franchiseRevenuePNPDistinct", "reporting");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.HeadOfficeRentalDue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.Period)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.TotalPerStore).HasColumnType("decimal(19, 4)");

                entity.Property(e => e.TotalRentalDue).HasColumnType("decimal(38, 8)");

                entity.Property(e => e.TypeOfStore)
                    .IsRequired()
                    .HasMaxLength(9)
                    .IsUnicode(false);

                entity.Property(e => e.Week)
                    .HasColumnType("datetime")
                    .HasColumnName("week");

                entity.Property(e => e.WeeklyCrossoverRevenuePerBillableWeekPerStore).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.WeeklyRevenuePerBillableWeekPerStore).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<GroupChain>(entity =>
            {
                entity.ToTable("GroupChain", "Store");

                entity.HasIndex(e => e.GroupChainName, "UniqueGroupChainName")
                    .IsUnique();

                entity.Property(e => e.GroupChainId).HasColumnName("GroupChainID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.GroupChainName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<HeadOffice>(entity =>
            {
                entity.ToTable("HeadOffice", "Store");

                entity.Property(e => e.HeadOfficeId).HasColumnName("HeadOfficeID");

                entity.Property(e => e.AccountNumber)
                    .HasMaxLength(10)
                    .HasComment("Account no / vendor no / creditor code to link Nova to the accounting package.");

                entity.Property(e => e.Approval)
                    .IsRequired()
                    .HasDefaultValueSql("((1))")
                    .HasComment("Head office requires approval letter for contracts running.");

                entity.Property(e => e.FixedPayment).HasColumnType("money");

                entity.Property(e => e.HeadOfficeName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.PaymentPercent)
                    .HasColumnType("decimal(5, 2)")
                    .HasDefaultValueSql("((100))");
            });

            modelBuilder.Entity<ImportedInstallationActions>(entity =>
            {
                entity.ToTable("ImportedInstallationActions", "Ops");


            });

            modelBuilder.Entity<IndependentStoreList>(entity =>
            {
                entity.ToTable("IndependentStoreList", "Sales");

                entity.Property(e => e.IndependentStoreListId)
                    .HasColumnName("IndependentStoreListID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.IndependentStoreListName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.PrincipalId)
                    .HasColumnName("principal_id")
                    .HasDefaultValueSql("(suser_id())");
            });

            modelBuilder.Entity<IndependentStoreListMember>(entity =>
            {
                entity.HasKey(e => new { e.IndependentStoreListId, e.StoreId });

                entity.ToTable("IndependentStoreListMember", "Sales");

                entity.Property(e => e.IndependentStoreListId).HasColumnName("IndependentStoreListID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.HasOne(d => d.IndependentStoreList)
                    .WithMany(p => p.IndependentStoreListMembers)
                    .HasForeignKey(d => d.IndependentStoreListId)
                    .HasConstraintName("FK_IndependentStoreListMember_IndependentStoreList");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.IndependentStoreListMembers)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_IndependentStoreListMember_Store");
            });

            modelBuilder.Entity<InstallationActionsReport>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("InstallationActionsReport", "reporting");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.Installcategory).HasColumnName("installcategory");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Mediatype)
                    .HasMaxLength(1024)
                    .IsUnicode(false)
                    .HasColumnName("mediatype");

                entity.Property(e => e.MymobilityCategory)
                    .HasMaxLength(1024)
                    .IsUnicode(false)
                    .HasColumnName("mymobilityCategory");

                entity.Property(e => e.TotalStores).HasColumnName("Total Stores");
            });

            modelBuilder.Entity<InstallationActionsReportWithStore>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("InstallationActionsReportWithStores", "reporting");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.Installcategory).HasColumnName("installcategory");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Mediatype)
                    .HasMaxLength(1024)
                    .IsUnicode(false)
                    .HasColumnName("mediatype");

                entity.Property(e => e.MymobilityCategory)
                    .HasMaxLength(1024)
                    .IsUnicode(false)
                    .HasColumnName("mymobilityCategory");

                entity.Property(e => e.Region)
                    .HasMaxLength(2048)
                    .IsUnicode(false)
                    .HasColumnName("region");

                entity.Property(e => e.Store)
                    .HasMaxLength(2048)
                    .IsUnicode(false)
                    .HasColumnName("store");

                entity.Property(e => e.TotalStores).HasColumnName("Total Stores");
            });

            modelBuilder.Entity<InstallationDays>(entity =>
            {
               
                entity.ToTable("InstallationDays", "Ops");

                entity.Property(e => e.InstallationDayId).HasColumnName("installationDayId");

                entity.Property(e => e.InstallationDay1)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("installationDay");
            });

            modelBuilder.Entity<InstallationInstruction>(entity =>
            {
                entity.ToTable("InstallationInstruction", "Ops");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Instruction)
                    .IsRequired()
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.InstructionExecutionDate).HasColumnType("datetime");

                entity.Property(e => e.StoreID).HasColumnName("StoreID");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.InstallationInstructions)
                    .HasForeignKey(d => d.Contractid)
                    .HasConstraintName("fk_InstallationInstruction_Contractid");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.InstallationInstructions)
                    .HasForeignKey(d => d.StoreID)
                    .HasConstraintName("fk_InstallationInstruction_StoreID");
            });

            modelBuilder.Entity<InstallationSchedule>(entity =>
            {
                entity.HasKey(e => new { e.Week, e.BurstId, e.StoreId, e.CategoryId });

                entity.ToTable("InstallationSchedule", "Ops");

                entity.HasIndex(e => new { e.CategoryId, e.Action, e.Week, e.Homesite, e.StoreId }, "_dta_index_InstallationSchedule_5_1479012350__K4_K7_K1_K5_K3_2")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Homesite, "_dta_index_InstallationSchedule_5_1479012350__K5")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Week).HasColumnType("datetime");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.Action)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.InstallationInstructions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");



                entity.HasOne(d => d.Burst)
                    .WithMany(p => p.InstallationSchedules)
                    .HasForeignKey(d => d.BurstId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InstallationSchedule_Burst");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.InstallationSchedules)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InstallationSchedule_Category");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.InstallationSchedules)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InstallationSchedule_Store");
            });

            modelBuilder.Entity<InstallationSchedule20190315>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("InstallationSchedule20190315", "Ops");

                entity.Property(e => e.Action)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.InstallationInstructions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<InstallationScheduleCurrent>(entity =>
            {


                entity.ToTable("InstallationScheduleCurrent", "Ops");

                entity.Property(e => e.DayOfCommencementDate).IsUnicode(false);

                entity.Property(e => e.DayOfTerminationDate).IsUnicode(false);

                entity.Property(e => e.ForDate).IsFixedLength();

                entity.Property(e => e.Group).IsUnicode(false);

                entity.Property(e => e.SpecialInstructions).IsUnicode(false);

                entity.Property(e => e.AllowPictureAfterwards)
                   .HasDefaultValueSql("((0))");

                entity.Property(e => e.imageURL)
                   
                    .HasDefaultValueSql("('')");

            });

            modelBuilder.Entity<InstallationScheduleArchived>(entity =>
            {


                entity.ToTable("InstallationScheduleArchived", "Ops");

                entity.Property(e => e.DayOfCommencementDate).IsUnicode(false);

                entity.Property(e => e.DayOfTerminationDate).IsUnicode(false);

                entity.Property(e => e.ForDate).IsFixedLength();

                entity.Property(e => e.Group).IsUnicode(false);

                entity.Property(e => e.SpecialInstructions).IsUnicode(false);

                entity.Property(e => e.AllowPictureAfterwards)
                   .HasDefaultValueSql("((0))");

                entity.Property(e => e.imageURL)

                    .HasDefaultValueSql("('')");

            });

            modelBuilder.Entity<InstallationScheduleMM>(entity =>
            {


                entity.ToTable("InstallationScheduleMM", "Ops");

                entity.Property(e => e.DayOfCommencementDate).IsUnicode(false);

                entity.Property(e => e.DayOfTerminationDate).IsUnicode(false);

                entity.Property(e => e.ForDate).IsFixedLength();

                entity.Property(e => e.Group).IsUnicode(false);

                entity.Property(e => e.SpecialInstructions).IsUnicode(false);

                entity.Property(e => e.AllowPictureAfterwards)
                   .HasDefaultValueSql("((0))");

                entity.Property(e => e.imageURL)

                    .HasDefaultValueSql("('')");

            });

            modelBuilder.Entity<InstallationTeam>(entity =>
            {
                entity.ToTable("InstallationTeam", "Ops");

                entity.HasIndex(e => e.InstallationTeamName, "UniqueInstallationTeamName")
                    .IsUnique();

                entity.Property(e => e.InstallationTeamId).HasColumnName("InstallationTeamID");

                entity.Property(e => e.InstallationTeamName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.InstallationTeamTypeId)
                    .HasColumnName("InstallationTeamTypeID")
                    .HasDefaultValueSql("((1))");

                entity.HasOne(d => d.InstallationTeamType)
                    .WithMany(p => p.InstallationTeams)
                    .HasForeignKey(d => d.InstallationTeamTypeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InstallationTeam_InstallationTeamType");
            });

            modelBuilder.Entity<InstallationTeamType>(entity =>
            {
                entity.ToTable("InstallationTeamType", "Ops");

                entity.Property(e => e.InstallationTeamTypeId).HasColumnName("InstallationTeamTypeID");

                entity.Property(e => e.TypeName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<Inventory>(entity =>
            {
                entity.HasKey(e => e.ItemId);

                entity.ToTable("Inventory", "Ops");

                entity.HasIndex(e => e.ItemName, "UniqueItemName")
                    .IsUnique();

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.AllowInContracts)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ItemName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<InventoryItemMovement>(entity =>
            {
                entity.ToTable("InventoryItemMovement", "Ops");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("createdBy")
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasColumnName("creationDate")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DeletedBy)
                    .HasMaxLength(255)
                    .HasColumnName("deletedBy");

                entity.Property(e => e.DeletionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("deletionDate");

                entity.HasOne(d => d.FromInstallationTeam)
                    .WithMany(p => p.InventoryItemMovementFromVans)
                    .HasForeignKey(d => d.FromVanId);

                entity.HasOne(d => d.FromWarehouse)
                    .WithMany(p => p.InventoryItemMovementFromWarehouses)
                    .HasForeignKey(d => d.FromWarehouseId);

                entity.HasOne(d => d.MasterItem)
                    .WithMany(p => p.InventoryItemMovements)
                    .HasForeignKey(d => d.MasterItemId);

                entity.HasOne(d => d.MasterItemInvenoryItem)
                    .WithMany(p => p.InventoryItemMovements)
                    .HasForeignKey(d => d.MasterItemInvenoryItemId);

                entity.HasOne(d => d.ToInstallationTeam)
                    .WithMany(p => p.InventoryItemMovementToVans)
                    .HasForeignKey(d => d.ToVanId);

                entity.HasOne(d => d.ToWarehouse)
                    .WithMany(p => p.InventoryItemMovementToWarehouses)
                    .HasForeignKey(d => d.ToWarehouseId);
            });

            modelBuilder.Entity<InventoryItemTransactions>(entity =>
            {
                entity.ToTable("InventoryItemTransactions", "Ops");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("createdBy")
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasColumnName("creationDate")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DeletedBy)
                    .HasMaxLength(255)
                    .HasColumnName("deletedBy");

                entity.Property(e => e.DeletionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("deletionDate");

                entity.HasOne(d => d.FromInstallationTeam)
                    .WithMany(p => p.InventoryItemTransactionsFromVans)
                    .HasForeignKey(d => d.FromVanId);

                entity.HasOne(d => d.ToInstallationTeam)
                   .WithMany(p => p.InventoryItemTransactionsToVans)
                   .HasForeignKey(d => d.ToVanId);

                entity.HasOne(d => d.FromWarehouse)
                    .WithMany(p => p.InventoryItemTransactionsFromWarehouses)
                    .HasForeignKey(d => d.FromWarehouseId);

                entity.HasOne(d => d.ToWarehouse)
                    .WithMany(p => p.InventoryItemTransactionsToWarehouses)
                    .HasForeignKey(d => d.ToWarehouseId);

              
                entity.HasOne(d => d.MasterItemInvenoryItem)
                    .WithMany(p => p.InventoryItemTransactions)
                    .HasForeignKey(d => d.MasterItemInvenoryItemId);

               


            });

            modelBuilder.Entity<InventoryQty>(entity =>
            {
                entity.HasKey(e => e.ItemQtyId);

                entity.ToTable("InventoryQty", "Ops");

                entity.HasIndex(e => new { e.ItemId, e.ItemQtyId }, "UniqueItemQty")
                    .IsUnique();

                entity.Property(e => e.ItemQtyId).HasColumnName("ItemQtyID");

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.InventoryQties)
                    .HasForeignKey(d => d.ItemId)
                    .HasConstraintName("FK_InventoryQty_Inventory");
            });

            modelBuilder.Entity<InventoryQtyPart>(entity =>
            {
                entity.ToTable("InventoryQtyPart", "Ops");

                entity.Property(e => e.InventoryQtyPartId).HasColumnName("InventoryQtyPartID");

                entity.Property(e => e.ItemQtyId).HasColumnName("ItemQtyID");

                entity.Property(e => e.ItemQtyPartId).HasColumnName("ItemQtyPartID");

                entity.HasOne(d => d.ItemQty)
                    .WithMany(p => p.InventoryQtyPartItemQties)
                    .HasForeignKey(d => d.ItemQtyId)
                    .HasConstraintName("FK_InventoryQtyPart_InventoryQty");

                entity.HasOne(d => d.ItemQtyPart)
                    .WithMany(p => p.InventoryQtyPartItemQtyParts)
                    .HasForeignKey(d => d.ItemQtyPartId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InventoryQtyPart_InventoryQty1");
            });

            modelBuilder.Entity<InventoryQtyPrice>(entity =>
            {
                entity.HasKey(e => e.ItemQtyPriceId);

                entity.ToTable("InventoryQtyPrice", "Ops");

                entity.HasIndex(e => new { e.ItemQtyId, e.EffectiveDate }, "UniqueItemQtyIDEffectiveDate")
                    .IsUnique();

                entity.Property(e => e.ItemQtyPriceId).HasColumnName("ItemQtyPriceID");

                entity.Property(e => e.CostPrice).HasColumnType("money");

                entity.Property(e => e.EffectiveDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ItemQtyId).HasColumnName("ItemQtyID");

                entity.Property(e => e.SellPrice).HasColumnType("money");

                entity.HasOne(d => d.ItemQty)
                    .WithMany(p => p.InventoryQtyPrices)
                    .HasForeignKey(d => d.ItemQtyId)
                    .HasConstraintName("FK_InventoryQtyPrice_InventoryQty");
            });

            modelBuilder.Entity<IRCodes>(entity =>
            {
                entity.ToTable("IRCodes", "Ops");

                entity.Property(e => e.IRCodeID).HasColumnName("IRCodeID");

                entity.Property(e => e.countsForStrikeRate)
                    .IsRequired()
                    .HasColumnName("countsForStrikeRate")
                    .HasDefaultValueSql("(CONVERT([bit],(0),(0)))");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DefaultComment).HasMaxLength(255);

              
                entity.Property(e => e.IRCodeName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("IRCodeName")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.IrCodeSortOrder).HasDefaultValue(999);
            });

            modelBuilder.Entity<LoadingFee>(entity =>
            {
                entity.ToTable("LoadingFee", "Sales");

                entity.HasIndex(e => e.LoadingFeeName, "UniqueLoadingFeeName")
                    .IsUnique();

                entity.Property(e => e.LoadingFeeId).HasColumnName("LoadingFeeID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DefaultPercentage).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.LoadingFeeName)
                    .IsRequired()
                    .HasMaxLength(200);
            });

            modelBuilder.Entity<LocationType>(entity =>
            {
                entity.ToTable("LocationType", "Ops");

                entity.Property(e => e.LocationTypeId).HasColumnName("LocationTypeID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.LocationTypeDescription).IsRequired();

                entity.Property(e => e.LocationTypeName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<Loginhistory>(entity =>
            {
                entity.HasKey(e => new { e.Userid, e.Dateoflogin });

                entity.ToTable("loginhistory", "security");

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Dateoflogin)
                    .HasColumnType("datetime")
                    .HasColumnName("dateoflogin")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<MasterItem>(entity =>
            {
                entity.ToTable("MasterItem", "Ops");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("createdBy")
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasColumnName("creationDate")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DeletedBy)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("deletedBy");

                entity.Property(e => e.DeletionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("deletionDate");

                entity.Property(e => e.Description).HasDefaultValueSql("('')");

                entity.Property(e => e.Dormant)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.MasterItemName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasDefaultValueSql("(N'')");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.MasterItems)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("FK_MasterItem_Category");

                entity.HasOne(d => d.MasterItemType)
                    .WithMany(p => p.MasterItems)
                    .HasForeignKey(d => d.MasterItemTypeId);
            });

            modelBuilder.Entity<MasterItemGroups>(entity =>
            {
                entity.ToTable("MasterItemGroups", "Ops");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MasterItemGroupName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<MasterItemGroupMember>(entity =>
            {
                entity.HasKey(e => e.MasterItemGroupMembersId);

                entity.ToTable("MasterItemGroupMembers", "Ops");

                entity.HasOne(d => d.MasterItemGroup)
                    .WithMany(p => p.MasterItemGroupMembers)
                    .HasForeignKey(d => d.MasterItemGroupId)
                    .HasConstraintName("FK_MasterItemGroupMembers_MasterItemGroups");

                entity.HasOne(d => d.MasterItem)
                    .WithMany(p => p.MasterItemGroupMembers)
                    .HasForeignKey(d => d.MasterItemId)
                    .HasConstraintName("FK_MasterItemGroupMembers_MasterItem");
            });

            modelBuilder.Entity<MasterItemInventoryItem>(entity =>
            {
                entity.ToTable("MasterItemInventoryItem", "Ops");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Barcode)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("createdBy")
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasColumnName("creationDate")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DeletedBy)
                    .HasMaxLength(255)
                    .HasColumnName("deletedBy");

                entity.Property(e => e.DeletionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("deletionDate");

                entity.HasOne(d => d.InstallationTeam)
                    .WithMany(p => p.MasterItemInventoryItems)
                    .HasForeignKey(d => d.InstallationTeamId);

                entity.HasOne(d => d.MasterItem)
                    .WithMany(p => p.MasterItemInventoryItem)
                    .HasForeignKey(d => d.MasterItemId);

                entity.HasOne(d => d.Shelf)
                    .WithMany(p => p.MasterItemInventoryItems)
                    .HasForeignKey(d => d.ShelfId);

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.MasterItemInventoryItems)
                    .HasForeignKey(d => d.StoreId);

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.MasterItemInventoryItems)
                    .HasForeignKey(d => d.WarehouseId);
            });

            modelBuilder.Entity<MasterItemType>(entity =>
            {
                entity.ToTable("MasterItemType", "Ops");

                entity.Property(e => e.MasterItemTypeId).HasColumnName("MasterItemTypeID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MasterItemTypeDescription).IsRequired();

                entity.Property(e => e.MasterItemTypeName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<MediaCapexOpex>(entity =>
            {
                entity.ToTable("MediaCapexOpex", "Media");

                entity.Property(e => e.MediaCapexOpexId).HasColumnName("MediaCapexOpexID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.isOptional)
                     .IsRequired()
                     .HasDefaultValueSql("((0))");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.MasterItemGroup)
                    .WithMany(p => p.MediaCapexOpices)
                    .HasForeignKey(d => d.MasterItemGroupId);

                entity.HasOne(d => d.MasterItem)
                    .WithMany(p => p.MediaCapexOpices)
                    .HasForeignKey(d => d.MasterItemId);

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaCapexOpex)
                    .HasForeignKey(d => d.MediaId);
            });

            modelBuilder.Entity<MediaCategory>(entity =>
            {
                entity.HasKey(e => new { e.MediaId, e.CategoryId });

                entity.ToTable("MediaCategory", "Media");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.MediaCategories)
                    .HasForeignKey(d => d.CategoryId)
                    .HasConstraintName("FK_MediaCategory_Category");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaCategory)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_MediaCategory_Media");
            });

            modelBuilder.Entity<MediaChannel>(entity =>
            {
                entity.ToTable("MediaChannel", "Media");

                entity.Property(e => e.MediaChannelId).HasColumnName("MediaChannelID");

                entity.Property(e => e.MediaChannelName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<MediaChannelGroupMember>(entity =>
            {
                entity.ToTable("MediaChannelGroupMember", "Media");

                entity.Property(e => e.MediaChannelGroupMemberId).HasColumnName("MediaChannelGroupMemberID");

                entity.Property(e => e.MediaChannelId).HasColumnName("MediaChannelID");

                entity.Property(e => e.MediaGroupId).HasColumnName("MediaGroupID");

                entity.HasOne(d => d.MediaChannel)
                    .WithMany(p => p.MediaChannelGroupMembers)
                    .HasForeignKey(d => d.MediaChannelId)
                    .HasConstraintName("fk_MediaChannelGroupMember_MediaChannelID");

                entity.HasOne(d => d.MediaGroup)
                    .WithMany(p => p.MediaChannelGroupMembers)
                    .HasForeignKey(d => d.MediaGroupId)
                    .HasConstraintName("fk_MediaChannelGroupMember_MediaGroupID");
            });

            modelBuilder.Entity<MediaCost>(entity =>
            {
                entity.ToTable("MediaCost", "Media");

                entity.Property(e => e.CostPercentage)
                    .HasColumnType("decimal(18, 2)")
                    .HasDefaultValueSql("((0.00))");

                entity.Property(e => e.CostPrice).HasColumnType("money");

                entity.Property(e => e.EffectiveDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.IsPercentage).HasColumnName("isPercentage");

               
            });

            modelBuilder.Entity<MediaFamily>(entity =>
            {
                entity.ToTable("MediaFamily", "Media");

                entity.HasIndex(e => e.MediaFamilyName, "UniqueMediaFamilyName")
                    .IsUnique();

                entity.Property(e => e.MediaFamilyId).HasColumnName("MediaFamilyID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MediaFamilyName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<MediaFamilyMember>(entity =>
            {
                entity.HasKey(e => new { e.MediaId, e.MediaFamilyId });

                entity.ToTable("MediaFamilyMember", "Media");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaFamilyId).HasColumnName("MediaFamilyID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.MediaFamily)
                    .WithMany(p => p.MediaFamilyMembers)
                    .HasForeignKey(d => d.MediaFamilyId)
                    .HasConstraintName("FK_MediaFamilyMember_MediaFamily");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaFamilyMember)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_MediaFamilyMember_Media");
            });

            modelBuilder.Entity<MediaGroup>(entity =>
            {
                entity.ToTable("MediaGroup", "Media");

                entity.HasIndex(e => e.MediaGroupName, "UniqueMediaGroupName")
                    .IsUnique();

                entity.Property(e => e.MediaGroupId).HasColumnName("MediaGroupID");

                entity.Property(e => e.Dormant).HasDefaultValueSql("((0))");

                entity.Property(e => e.MediaGroupName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<MediaGroupMember>(entity =>
            {
                entity.HasKey(e => new { e.MediaGroupId, e.MediaId });

                entity.ToTable("MediaGroupMember", "Media");

                entity.Property(e => e.MediaGroupId).HasColumnName("MediaGroupID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.HasOne(d => d.MediaGroup)
                    .WithMany(p => p.MediaGroupMembers)
                    .HasForeignKey(d => d.MediaGroupId)
                    .HasConstraintName("FK_MediaGroupMember_MediaGroup");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaGroupMember)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_MediaGroupMember_Media");
            });

            modelBuilder.Entity<MediaInstallationDay>(entity =>
            {
                entity.HasKey(e => e.InstallationDayId);

                entity.ToTable("MediaInstallationDay", "Media");

                entity.Property(e => e.InstallationDayId).HasColumnName("InstallationDayID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MediaInstallationDayName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<MediaInventory>(entity =>
            {
                entity.HasKey(e => new { e.MediaId, e.ItemId });

                entity.ToTable("MediaInventory", "Ops");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.MediaInventories)
                    .HasForeignKey(d => d.ItemId)
                    .HasConstraintName("FK_MediaInventory_Inventory");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaInventory)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_MediaInventory_Media");
            });

            modelBuilder.Entity<MediaLifeCycle>(entity =>
            {
                entity.HasKey(e => new { e.MediaId, e.FirstWeek, e.LastWeek });

                entity.ToTable("MediaLifeCycle", "Media");

                entity.HasIndex(e => e.MediaId, "IX_MediaLifeCycle");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.FirstWeek)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(dateadd(week,(3),getdate()))");

                entity.Property(e => e.LastWeek)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("('20901225')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaLifeCycle)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_MediaLifeCycle_Media");
            });

            modelBuilder.Entity<MediaRate>(entity =>
            {
                entity.ToTable("MediaRate", "TradeRights");

                entity.HasIndex(e => e.EffectiveDate, "idx_MediaRateEffectiveDate")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.EffectiveDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaRate1).HasColumnName("MediaRate");

                entity.Property(e => e.PaymentClassificationId).HasColumnName("PaymentClassificationID");


                entity.HasOne(d => d.PaymentClassification)
                    .WithMany(p => p.MediaRates)
                    .HasForeignKey(d => d.PaymentClassificationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK__MediaRate__Payme__3180F3EE");
            });

            modelBuilder.Entity<MediaRule>(entity =>
            {
                entity.ToTable("MediaRule", "Media");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Description).IsUnicode(false);

                entity.Property(e => e.Dormant).HasDefaultValueSql("((0))");

                entity.Property(e => e.Rule)
                    .IsRequired()
                    .HasMaxLength(250)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<MediaRuleRelationship>(entity =>
            {
                entity.ToTable("MediaRule_Relationship", "Media");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaRuleId).HasColumnName("MediaRuleID");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.MediaRuleRelationship)
                    .HasForeignKey(d => d.MediaId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_MediaRule_Relationship_Media");

                entity.HasOne(d => d.MediaRule)
                    .WithMany(p => p.MediaRuleRelationships)
                    .HasForeignKey(d => d.MediaRuleId)
                    .HasConstraintName("FK_MediaRule_Relationship_MediaRule");
            });

         

            modelBuilder.Entity<Meeting>(entity =>
            {
                entity.ToTable("meeting", "admin");

                entity.Property(e => e.Meetingid)
                    .HasColumnName("meetingid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Meetingname)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("meetingname")
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<Meetingtopic>(entity =>
            {
                entity.ToTable("meetingtopic", "admin");

                entity.Property(e => e.Meetingtopicid)
                    .HasColumnName("meetingtopicid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Meetingid).HasColumnName("meetingid");

                entity.Property(e => e.Meetingtopicname)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("meetingtopicname")
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<Minute>(entity =>
            {
                entity.ToTable("minute", "admin");

                entity.Property(e => e.Minuteid)
                    .HasColumnName("minuteid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Minutetext)
                    .IsRequired()
                    .HasMaxLength(3000)
                    .HasColumnName("minutetext");

                entity.Property(e => e.Minutetypeid).HasColumnName("minutetypeid");
            });

            modelBuilder.Entity<Minutetype>(entity =>
            {
                entity.ToTable("minutetype", "admin");

                entity.Property(e => e.Minutetypeid)
                    .HasColumnName("minutetypeid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Minutetypename)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("minutetypename");
            });

            modelBuilder.Entity<MiscellaneousCharge>(entity =>
            {
                entity.ToTable("MiscellaneousCharge", "Sales");

                entity.HasIndex(e => e.MiscellaneousChargeName, "UniqueMiscellaneousChargeName")
                    .IsUnique();

                entity.Property(e => e.MiscellaneousChargeId).HasColumnName("MiscellaneousChargeID");

                entity.Property(e => e.MiscellaneousChargeName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<MonthlycampaignreviewStore>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("monthlycampaignreviewStore", "reporting");

                entity.Property(e => e.AssignedAm)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("AssignedAM");

                entity.Property(e => e.Brand)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Category)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ChainType)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.ContractAm)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("ContractAM");

                entity.Property(e => e.Countedcorps).HasColumnName("countedcorps");

                entity.Property(e => e.CurrentAm)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("CurrentAM");

                entity.Property(e => e.CurrentForecastRevenue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.CurrentForecastRevenueCorps).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.CurrentForecastRevenueFranchises).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.MediaService)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Period).HasMaxLength(7);

                entity.Property(e => e.PriorActualRevenue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorActualRevenueCorps).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorActualRevenueFranchises).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorForecastRevenue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorForecastRevenueCorps).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorForecastRevenueFranchises).HasColumnType("decimal(38, 6)");
            });

            modelBuilder.Entity<NewContractListToBeUpdatedWithDatum>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("New_ContractListToBeUpdatedWithData", "Scanner");

                entity.Property(e => e.AccountManagerInitialsCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("AccountManagerInitials_Code");

                entity.Property(e => e.AccountManagerName)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.BrandName).HasMaxLength(200);

                entity.Property(e => e.CampaignFirstWeek).HasColumnType("datetime");

                entity.Property(e => e.CampaignLastWeek).HasColumnType("datetime");

                entity.Property(e => e.ChainName).HasMaxLength(200);

                entity.Property(e => e.ClicksIsDataReadyForPrc).HasColumnName("CLICKS_isDataReadyForPRC");

                entity.Property(e => e.ClicksIsDataSubmittedForPrc).HasColumnName("CLICKS_isDataSubmittedForPRC");

                entity.Property(e => e.ClicksNumberOfStoresSelected).HasColumnName("Clicks_NumberOfStoresSelected");

                entity.Property(e => e.ClicksReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("CLICKS_ReadyDate");

                entity.Property(e => e.ClicksSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("CLICKS_SubmissionDate");

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.ContractTerminationDate).HasColumnType("datetime");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.IsSelectedForScannerData).HasColumnName("isSelectedForScannerData");

                entity.Property(e => e.MediaType).HasMaxLength(200);

                entity.Property(e => e.PnpIsDataReadyForPrc).HasColumnName("PNP_isDataReadyForPRC");

                entity.Property(e => e.PnpIsDataSubmittedForPrc).HasColumnName("PNP_isDataSubmittedForPRC");

                entity.Property(e => e.PnpNumberOfStoresSelected).HasColumnName("PNP_NumberOfStoresSelected");

                entity.Property(e => e.PnpReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PNP_ReadyDate");

                entity.Property(e => e.PnpSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PNP_SubmissionDate");

                entity.Property(e => e.SelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("SelectedForScannerData_Date");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SparIsDataReadyForPrc).HasColumnName("SPAR_isDataReadyForPRC");

                entity.Property(e => e.SparIsDataSubmittedForPrc).HasColumnName("SPAR_isDataSubmittedForPRC");

                entity.Property(e => e.SparNumberOfStoresSelected).HasColumnName("Spar_NumberOfStoresSelected");

                entity.Property(e => e.SparReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("SPAR_ReadyDate");

                entity.Property(e => e.SparSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("SPAR_SubmissionDate");
            });

            modelBuilder.Entity<OldProductName>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("OldProductName");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<PackSize>(entity =>
            {
                entity.ToTable("PackSize", "Client");

                entity.HasIndex(e => e.PackSizeName, "UniquePackSizeName")
                    .IsUnique();

                entity.Property(e => e.PackSizeId)
                    .HasColumnName("PackSizeID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.PackSizeName)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<PackSizeUnitOfMeasure>(entity =>
            {
                entity.ToTable("PackSizeUnitOfMeasure", "Distribution");

                entity.Property(e => e.PackSizeUnitOfMeasureId)
                    .HasColumnName("PackSizeUnitOfMeasureID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.PackSizeUnitOfMeasureName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<Passwordhistory>(entity =>
            {
                entity.HasKey(e => new { e.Userid, e.Dateofcreation });

                entity.ToTable("passwordhistory", "security");

                entity.HasIndex(e => e.Userid, "index_userid")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Dateofcreation)
                    .HasColumnType("datetime")
                    .HasColumnName("dateofcreation")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Createdbyuserid).HasColumnName("createdbyuserid");

                entity.Property(e => e.Passwordhash)
                    .IsRequired()
                    .HasMaxLength(88)
                    .HasColumnName("passwordhash")
                    .IsFixedLength(true);

                entity.HasOne(d => d.Createdbyuser)
                    .WithMany(p => p.PasswordhistoryCreatedbyusers)
                    .HasForeignKey(d => d.Createdbyuserid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_passwordhistory_systemuser1");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.PasswordhistoryUsers)
                    .HasForeignKey(d => d.Userid)
                    .HasConstraintName("FK_passwordhistory_systemuser");
            });

            modelBuilder.Entity<PaymentClassification>(entity =>
            {
                entity.ToTable("PaymentClassification", "TradeRights");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Beneficiary)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('Stores')");

                entity.Property(e => e.Dormant).HasDefaultValueSql("((0))");

                entity.Property(e => e.FixedPaymentFirstWeek).HasColumnType("datetime");

                entity.Property(e => e.FixedPaymentLastWeek).HasColumnType("datetime");

                entity.Property(e => e.PaymentClassificationName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.StoreRentalRate).HasDefaultValueSql("((0.00))");

                entity.Property(e => e.TypeOfPaymentId).HasColumnName("TypeOfPaymentID");

                entity.Property(e => e.TypeOfRate)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('Rental Rate')");

                entity.HasOne(d => d.TypeOfPayment)
                    .WithMany(p => p.PaymentClassifications)
                    .HasForeignKey(d => d.TypeOfPaymentId)
                    .HasConstraintName("FK_TypeOfPayment_PaymentClassification");
            });

            modelBuilder.Entity<PcaStatus>(entity =>
            {
                entity.ToTable("PcaStatus", "Media");

                entity.Property(e => e.PcaStatusName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<Period>(entity =>
            {
                entity.ToTable("Period", "Finance");

                entity.Property(e => e.PeriodId)
                    .ValueGeneratedNever()
                    .HasColumnName("PeriodID");

                entity.Property(e => e.PeriodName)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<PnpBarcode>(entity =>
            {
                entity.HasKey(e => e.BarcodeId);

                entity.ToTable("PNP_barcode", "Scanner");

                entity.HasIndex(e => e.Barcode, "IX_PNP_barcode_barcode")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.BarcodeId)
                    .ValueGeneratedNever()
                    .HasColumnName("BarcodeID");

                entity.Property(e => e.Barcode).HasMaxLength(30);

                entity.Property(e => e.Brand).HasMaxLength(50);

                entity.Property(e => e.Description).HasMaxLength(100);

                entity.Property(e => e.Size).HasMaxLength(20);
            });

            modelBuilder.Entity<Policy>(entity =>
            {
                entity.ToTable("Policy", "Sales");

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EffectiveDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MediaGroupId).HasColumnName("MediaGroupID");

                entity.Property(e => e.PolicyName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.RegionId).HasColumnName("RegionID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.HasOne(d => d.MediaGroup)
                    .WithMany(p => p.Policies)
                    .HasForeignKey(d => d.MediaGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Policy_MediaGroup");
            });

            modelBuilder.Entity<Product>(entity =>
            {
                entity.ToTable("Product", "Client");

                entity.Property(e => e.ProductId)
                    .HasColumnName("ProductID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.Products)
                    .HasForeignKey(d => d.BrandId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Product_Brand");
            });

            modelBuilder.Entity<ProductDistribution>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ProductDistribution", "ReportData");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CheckDate).HasColumnType("date");

                entity.Property(e => e.DistributionRegionId).HasColumnName("DistributionRegionID");

                entity.Property(e => e.DistributionRegionName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.PackSizeName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreNumber)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.Property(e => e.VanName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<ProvisionalBooking>(entity =>
            {
                entity.ToTable("ProvisionalBooking", "Sales");

                entity.Property(e => e.ProvisionalBookingId)
                    .HasColumnName("ProvisionalBookingID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.BookTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.ExpiryTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(dateadd(week,(2),getdate()))");

                entity.Property(e => e.FirstWeek)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MediaFamilyId).HasColumnName("MediaFamilyID");

                entity.Property(e => e.ProvisionalBookingName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.ProvisionalBookings)
                    .HasForeignKey(d => d.BrandId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProvisionalBooking_Brand");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.ProvisionalBookings)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProvisionalBooking_Category");

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.ProvisionalBookings)
                    .HasForeignKey(d => d.ChainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProvisionalBooking_Chain");

                entity.HasOne(d => d.MediaFamily)
                    .WithMany(p => p.ProvisionalBookings)
                    .HasForeignKey(d => d.MediaFamilyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProvisionalBooking_MediaFamily");
            });

            modelBuilder.Entity<Purchase>(entity =>
            {
                entity.ToTable("Purchase", "Ops");

                entity.Property(e => e.PurchaseId)
                    .HasColumnName("PurchaseID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.InvoiceNumber)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.OperatorName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PurchaseDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.Property(e => e.WholesalerName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.HasOne(d => d.Van)
                    .WithMany(p => p.Purchases)
                    .HasForeignKey(d => d.VanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Purchase_Van");
            });

            modelBuilder.Entity<PurchaseDetail>(entity =>
            {
                entity.ToTable("PurchaseDetail", "Ops");

                entity.Property(e => e.PurchaseDetailId)
                    .HasColumnName("PurchaseDetailID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.PurchaseId).HasColumnName("PurchaseID");

                entity.HasOne(d => d.PackSize)
                    .WithMany(p => p.PurchaseDetails)
                    .HasForeignKey(d => d.PackSizeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseDetail_PackSize");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PurchaseDetails)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseDetail_Product");

                entity.HasOne(d => d.Purchase)
                    .WithMany(p => p.PurchaseDetails)
                    .HasForeignKey(d => d.PurchaseId)
                    .HasConstraintName("FK_PurchaseDetail_Purchase");
            });

            modelBuilder.Entity<PurchaseOrderNumber>(entity =>
            {
                entity.HasKey(e => new { e.ContractId, e.PurchaseOrderNumber1 });

                entity.ToTable("PurchaseOrderNumber", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.PurchaseOrderNumber1)
                    .HasMaxLength(50)
                    .HasColumnName("PurchaseOrderNumber")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.PurchaseOrderNumberDescription)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.PurchaseOrderNumbers)
                    .HasForeignKey(d => d.ContractId)
                    .HasConstraintName("FK_PurchaseOrderNumber_Contract");
            });

            modelBuilder.Entity<QuantityUnitOfMeasure>(entity =>
            {
                entity.ToTable("QuantityUnitOfMeasure", "Distribution");

                entity.Property(e => e.QuantityUnitOfMeasureId)
                    .HasColumnName("QuantityUnitOfMeasureID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.QuantityUnitOfMeasureName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<Receiver>(entity =>
            {
                entity.ToTable("receiver", "productlocator");

                entity.Property(e => e.Receiverid)
                    .HasColumnName("receiverid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Contractid).HasColumnName("contractid");

                entity.Property(e => e.Product)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("product")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Receivernumber).HasColumnName("receivernumber");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.Receivers)
                    .HasForeignKey(d => d.Contractid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_receiver_Contract");
            });

            modelBuilder.Entity<Region>(entity =>
            {
                entity.ToTable("Region", "Store");

                entity.HasIndex(e => new { e.ChainId, e.RegionName }, "UniqueRegionName")
                    .IsUnique();

                entity.Property(e => e.RegionId).HasColumnName("RegionID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.RegionName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.Regions)
                    .HasForeignKey(d => d.ChainId)
                    .HasConstraintName("FK_Region_Chain");
            });

            modelBuilder.Entity<OpsRegion>(entity =>
            {
                entity.HasKey(e => e.RegionId)
                    .HasName("PK_Ops.Region");

                entity.ToTable("Region", "Ops");

                entity.Property(e => e.RegionId).HasColumnName("RegionID");

                entity.Property(e => e.RegionCode).HasColumnName("RegionCode");

                entity.Property(e => e.RegionName).HasColumnName("RegionName");

                entity.Property(e => e.Dormant).HasColumnName("Dormant");

                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate).HasColumnName("CreationDate").HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<RequestBarcode>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("Request_barcode", "Scanner");

                entity.HasIndex(e => e.Barcode, "IX_Request_barcode_BARCODE")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Number, "IX_Request_barcode_CONTRACT_NO")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Barcodeid, "IX_Request_barcode_ID")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.PcrId, "IX_Request_barcode_PCR_D")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Barcode)
                    .HasMaxLength(30)
                    .HasColumnName("barcode");

                entity.Property(e => e.Barcodeid).HasColumnName("barcodeid");

                entity.Property(e => e.Brand)
                    .HasMaxLength(50)
                    .HasColumnName("brand");

                entity.Property(e => e.ClicksBarCode)
                    .HasMaxLength(30)
                    .HasColumnName("Clicks_BarCode");

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(12)
                    .HasColumnName("created_by");

                entity.Property(e => e.CreatedDt)
                    .HasColumnType("datetime")
                    .HasColumnName("created_dt");

                entity.Property(e => e.ModBy)
                    .HasMaxLength(12)
                    .HasColumnName("mod_by");

                entity.Property(e => e.ModDt)
                    .HasColumnType("datetime")
                    .HasColumnName("mod_dt");

                entity.Property(e => e.Number)
                    .HasMaxLength(8)
                    .HasColumnName("number");

                entity.Property(e => e.PcrId).HasColumnName("PCR_ID");

                entity.Property(e => e.ProdGrouping)
                    .HasMaxLength(50)
                    .HasColumnName("Prod_grouping");

                entity.Property(e => e.Prodvariant)
                    .HasMaxLength(50)
                    .HasColumnName("prodvariant");
            });

            modelBuilder.Entity<ResearchCategory>(entity =>
            {
                entity.ToTable("ResearchCategory", "Sales");

                entity.Property(e => e.ResearchCategoryId)
                    .HasColumnName("ResearchCategoryID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Discount).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.Fee).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.FromDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Months).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.ResearchCategories)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ResearchCategory_Category");

                entity.HasOne(d => d.Contract)
                    .WithMany(p => p.ResearchCategories)
                    .HasForeignKey(d => d.ContractId)
                    .HasConstraintName("FK_ResearchCategory_Contract");
            });

            modelBuilder.Entity<Revforde>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("revfordes");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.Period).HasMaxLength(7);

                entity.Property(e => e.RecognisedRevenue).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("role", "master");

                entity.Property(e => e.Roleid)
                    .HasColumnName("roleid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Roledescription)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .HasColumnName("roledescription")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Rolename)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("rolename")
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<RoleOwner>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("RoleOwner");

                entity.Property(e => e.Owner)
                    .HasMaxLength(50)
                    .HasColumnName("owner");

                entity.Property(e => e.Role)
                    .HasMaxLength(50)
                    .HasColumnName("role");
            });

            modelBuilder.Entity<Rolemapping>(entity =>
            {
                entity.HasKey(e => e.Roleid)
                    .HasName("PK_rolemapping_1");

                entity.ToTable("rolemapping", "integration");

                entity.Property(e => e.Roleid)
                    .ValueGeneratedNever()
                    .HasColumnName("roleid");

                entity.Property(e => e.Legacyrolename)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("legacyrolename");
            });

            modelBuilder.Entity<Rolemapping1>(entity =>
            {
                entity.HasKey(e => new { e.Dbrolename, e.Rolename });

                entity.ToTable("rolemapping", "security");

                entity.HasComment("This table maps the database roles used in Nova2 to the new roles in Nova3.  Once the migration to Nova3 is complete, this table can be deleted.\r\nLink to sys.database_principals to get role information.");

                entity.Property(e => e.Dbrolename)
                    .HasMaxLength(100)
                    .HasColumnName("dbrolename");

                entity.Property(e => e.Rolename)
                    .HasMaxLength(100)
                    .HasColumnName("rolename")
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<Rolemember>(entity =>
            {
                entity.HasKey(e => new { e.Userid, e.Roleid, e.Datemodified })
                    .HasName("PK_systemuserrole");

                entity.ToTable("rolemember", "security");

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Roleid).HasColumnName("roleid");

                entity.Property(e => e.Datemodified)
                    .HasColumnType("datetime")
                    .HasColumnName("datemodified")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Ismember)
                    .IsRequired()
                    .HasColumnName("ismember")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Modifiedbyuserid).HasColumnName("modifiedbyuserid");

                entity.HasOne(d => d.Modifiedbyuser)
                    .WithMany(p => p.RolememberModifiedbyusers)
                    .HasForeignKey(d => d.Modifiedbyuserid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_rolemember_systemuser1");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.Rolemembers)
                    .HasForeignKey(d => d.Roleid)
                    .HasConstraintName("FK_rolemember_role");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.RolememberUsers)
                    .HasForeignKey(d => d.Userid)
                    .HasConstraintName("FK_rolemember_systemuser");
            });

            modelBuilder.Entity<Roleowner1>(entity =>
            {
                entity.HasKey(e => new { e.Userid, e.Roleid, e.Datemodified });

                entity.ToTable("roleowner", "security");

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Roleid).HasColumnName("roleid");

                entity.Property(e => e.Datemodified)
                    .HasColumnType("datetime")
                    .HasColumnName("datemodified")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Isowner)
                    .IsRequired()
                    .HasColumnName("isowner")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Modifiedbyuserid).HasColumnName("modifiedbyuserid");

                entity.HasOne(d => d.Modifiedbyuser)
                    .WithMany(p => p.Roleowner1Modifiedbyusers)
                    .HasForeignKey(d => d.Modifiedbyuserid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_roleowner_systemuser1");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.Roleowner1s)
                    .HasForeignKey(d => d.Roleid)
                    .HasConstraintName("FK_roleowner_role");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.Roleowner1Users)
                    .HasForeignKey(d => d.Userid)
                    .HasConstraintName("FK_roleowner_systemuser");
            });

            modelBuilder.Entity<Sale>(entity =>
            {
                entity.ToTable("Sale", "Ops");

                entity.HasIndex(e => e.InvoiceNumber, "UniqueInvoiceNumber")
                    .IsUnique();

                entity.HasIndex(e => new { e.SaleId, e.SaleDate, e.VanId, e.StoreId }, "_dta_index_Sale_7_1266155606__K1_K4_K2_K3")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.SaleId)
                    .HasColumnName("SaleID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.InvoiceNumber)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.OperatorName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.SaleDate).HasColumnType("date");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.Sales)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Sale_Store");

                entity.HasOne(d => d.Van)
                    .WithMany(p => p.Sales)
                    .HasForeignKey(d => d.VanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Sale_Van");
            });

            modelBuilder.Entity<SaleDetail>(entity =>
            {
                entity.ToTable("SaleDetail", "Ops");

                entity.HasIndex(e => new { e.ProductId, e.PackSizeId, e.Units, e.SaleId }, "_dta_index_SaleDetail_7_1314155777__K3_K4_K6_K2")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.SaleDetailId)
                    .HasColumnName("SaleDetailID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.SaleId).HasColumnName("SaleID");

                entity.HasOne(d => d.PackSize)
                    .WithMany(p => p.SaleDetails)
                    .HasForeignKey(d => d.PackSizeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SaleDetail_PackSize");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SaleDetails)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SaleDetail_Product");

                entity.HasOne(d => d.Sale)
                    .WithMany(p => p.SaleDetails)
                    .HasForeignKey(d => d.SaleId)
                    .HasConstraintName("FK_SaleDetail_Sale");
            });

            modelBuilder.Entity<ScannerBarCode>(entity =>
            {
                entity.ToTable("ScannerBarCodes", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.SizeDesc)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.Variant)
                    .HasMaxLength(200)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ScannerBarCodesResult>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ScannerBarCodesResults", "Scanner");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ContractCode)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Contract code");

                entity.Property(e => e.MonthYr)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Month-Yr");

                entity.Property(e => e.Product)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.RegionClusterStore)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Region Cluster Store");

                entity.Property(e => e.ReqNo).HasColumnName("Req No.");

                entity.Property(e => e.SizeDesc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Size Desc");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<ScannerDataRawDistinct>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ScannerDataRawDistinct", "Media");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("barcode")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Brand)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("brand")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Branddescription)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("branddescription")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Category)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("category")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Department)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("department")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Enddate)
                    .HasColumnType("date")
                    .HasColumnName("enddate");

                entity.Property(e => e.Region)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("region")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Sales)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("sales")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Storeid)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("storeid")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Storename)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("storename")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Subcategory)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("subcategory")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Units)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("units")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Variant)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("variant")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");
            });

            modelBuilder.Entity<ScannerDataRawDistinctWithContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ScannerDataRawDistinctWithContract", "Media");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("barcode")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Brand)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("brand")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Branddescription)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("branddescription")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Category)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("category")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.Department)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("department")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Enddate)
                    .HasColumnType("date")
                    .HasColumnName("enddate");

                entity.Property(e => e.PostPeriodEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_EndDate");

                entity.Property(e => e.PostPeriodStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_StartDate");

                entity.Property(e => e.PriorPeriodEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_EndDate");

                entity.Property(e => e.PriorPeriodStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_StartDate");

                entity.Property(e => e.PriorYearEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_EndDate");

                entity.Property(e => e.PriorYearStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_StartDate");

                entity.Property(e => e.Region)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("region")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Sales)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("sales")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Storeid)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("storeid")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Storename)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("storename")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Subcategory)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("subcategory")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Units)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("units")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");

                entity.Property(e => e.Variant)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("variant")
                    .UseCollation("SQL_Latin1_General_CP1_CI_AS");
            });

            modelBuilder.Entity<ScannerDataRequestSet>(entity =>
            {
                entity.HasKey(e => new { e.Number, e.FromDate1, e.ToDate1, e.FromDate2, e.ToDate2 });

                entity.ToTable("scanner_data_request_set", "Scanner");

                entity.HasIndex(e => e.RequestId, "IX_scanner_data_request_set_ID")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.RequestId, "IX_scanner_data_request_set_REquestID")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => new { e.FromDate1, e.FromDate2, e.ToDate1, e.ToDate2 }, "IX_scanner_data_request_set_dates")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Number, "IX_scanner_data_request_set_number")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Number).HasMaxLength(8);

                entity.Property(e => e.FromDate1).HasColumnType("datetime");

                entity.Property(e => e.ToDate1).HasColumnType("datetime");

                entity.Property(e => e.FromDate2).HasColumnType("datetime");

                entity.Property(e => e.ToDate2).HasColumnType("datetime");

                entity.Property(e => e.CorpStoresData)
                    .HasColumnName("Corp_stores_Data")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.FamilyStoresData)
                    .HasColumnName("Family_stores_Data")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDatePnp)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_PNP");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.RequestId).HasColumnName("request_id");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(1)
                    .HasColumnName("store_type");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDatePnp)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_PNP");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");
            });

            modelBuilder.Entity<ScannerDataResultDetail>(entity =>
            {
                entity.HasKey(e => e.ScannerImportId);

                entity.ToTable("ScannerData_Result_Detail", "Scanner");

                entity.HasIndex(e => e.BarcodeId, "IX_ScannerData_Result_Detail_BARCODEID")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => new { e.FromDate1, e.FromDate2 }, "IX_ScannerData_Result_Detail_FROMDATES")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Number, "IX_ScannerData_Result_Detail_Number")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.PnpstoreId, "IX_ScannerData_Result_Detail_PNPStoreID")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => new { e.ToDate1, e.ToDate2 }, "IX_ScannerData_Result_Detail_TODATES")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.StoreType, "IX_ScannerDate_Result_Detail_Type")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.PcrId, "ScannerData_Result_Detail_pcrid")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.PcrId, "scannerdata_result_detail_nonclust")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Number, "scannerdata_result_detail_nonclust_fromdate1")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.FromDate2, "scannerdata_result_detail_nonclust_fromdate2")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.Number, "scannerdata_result_detail_nonclust_number")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.ToDate1, "scannerdata_result_detail_nonclust_todate1")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.ToDate2, "scannerdata_result_detail_nonclust_todate2")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.ScannerImportId)
                    .ValueGeneratedNever()
                    .HasColumnName("ScannerImportID");

                entity.Property(e => e.BarcodeId).HasColumnName("BarcodeID");

                entity.Property(e => e.CreatedDt)
                    .HasColumnType("smalldatetime")
                    .HasColumnName("created_dt");

                entity.Property(e => e.FromDate1).HasColumnType("datetime");

                entity.Property(e => e.FromDate2).HasColumnType("datetime");

                entity.Property(e => e.Number)
                    .HasMaxLength(8)
                    .HasColumnName("number");

                entity.Property(e => e.PcrId).HasColumnName("PCR_ID");

                entity.Property(e => e.PnpstoreId).HasColumnName("PNPStoreID");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(1)
                    .HasColumnName("Store_Type");

                entity.Property(e => e.ToDate1).HasColumnType("datetime");

                entity.Property(e => e.ToDate2).HasColumnType("datetime");
            });

            modelBuilder.Entity<ScannerDataResultDetailHi>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ScannerData_Result_Detail_His", "Scanner");

                entity.Property(e => e.BarcodeId).HasColumnName("BarcodeID");

                entity.Property(e => e.CreatedDt)
                    .HasColumnType("smalldatetime")
                    .HasColumnName("created_dt");

                entity.Property(e => e.FromDate1).HasColumnType("datetime");

                entity.Property(e => e.FromDate2).HasColumnType("datetime");

                entity.Property(e => e.Number)
                    .HasMaxLength(8)
                    .HasColumnName("number");

                entity.Property(e => e.PcrId).HasColumnName("PCR_ID");

                entity.Property(e => e.PnpstoreId).HasColumnName("PNPStoreID");

                entity.Property(e => e.ScannerImportId).HasColumnName("ScannerImportID");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(1)
                    .HasColumnName("Store_Type");

                entity.Property(e => e.ToDate1).HasColumnType("datetime");

                entity.Property(e => e.ToDate2).HasColumnType("datetime");
            });

            modelBuilder.Entity<ScannerDataToolBarcode>(entity =>
            {
                entity.ToTable("ScannerDataTool_Barcode", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Description).IsUnicode(false);

                entity.Property(e => e.Size)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ScannerStore>(entity =>
            {
                entity.HasKey(e => e.PnpstoreId)
                    .HasName("PK_Scanner_Store_PNP_ID");

                entity.ToTable("Scanner_Store", "Scanner");

                entity.HasIndex(e => e.PnpstoreId, "IX_Scanner_Store")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.PnpstoreId, "IX_Scanner_Store_1")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.PnpstoreCode, "IX_Scanner_Store_PNP_CODE")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => e.StoreId, "IX_Scanner_Store_StoreID")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.PnpstoreId)
                    .ValueGeneratedNever()
                    .HasColumnName("PNPStoreID");

                entity.Property(e => e.PnpstoreCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.Region).HasMaxLength(50);

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreIdLiqour).HasColumnName("StoreID_Liqour");

                entity.Property(e => e.StoreName).HasMaxLength(50);

                entity.Property(e => e.StoreType).HasMaxLength(50);
            });

            modelBuilder.Entity<ScannerVOutstandingPcrReport>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("Scanner.vOutstanding_pcr_reports");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(30)
                    .HasColumnName("barcode");

                entity.Property(e => e.Brand)
                    .HasMaxLength(50)
                    .HasColumnName("brand");

                entity.Property(e => e.FromDate1).HasColumnType("datetime");

                entity.Property(e => e.FromDate2).HasColumnType("datetime");

                entity.Property(e => e.Number)
                    .HasMaxLength(8)
                    .HasColumnName("number");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.ScannerGroup)
                    .HasMaxLength(150)
                    .HasColumnName("Scanner_Group");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");

                entity.Property(e => e.SubmitTimeClicks)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Clicks");

                entity.Property(e => e.SubmitTimeSpar)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Spar");

                entity.Property(e => e.ToDate1).HasColumnType("datetime");

                entity.Property(e => e.ToDate2).HasColumnType("datetime");
            });

            modelBuilder.Entity<SchedulesForInstallationOnly>(entity =>
            {
                entity.ToTable("SchedulesForInstallationOnly");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.CategoryName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Chain)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Client)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.CommencementDate).HasColumnType("datetime");

                entity.Property(e => e.JobNumber)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.MediaType)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Product)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Region)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.TerminationDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<Setting>(entity =>
            {
                entity.ToTable("Setting");

                entity.HasIndex(e => e.SettingName, "UniqueSettingName")
                    .IsUnique();

                entity.Property(e => e.SettingId).HasColumnName("SettingID");

                entity.Property(e => e.SettingGroupId).HasColumnName("SettingGroupID");

                entity.Property(e => e.SettingName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.SettingValue)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.HasOne(d => d.SettingGroup)
                    .WithMany(p => p.Settings)
                    .HasForeignKey(d => d.SettingGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Setting_SettingGroup");
            });

            modelBuilder.Entity<Setting1>(entity =>
            {
                entity.HasKey(e => e.Settingid);

                entity.ToTable("setting", "master");

                entity.Property(e => e.Settingid)
                    .HasColumnName("settingid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Settingdescription)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("settingdescription");

                entity.Property(e => e.Settingname)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("settingname");

                entity.Property(e => e.Settingvalue)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("settingvalue");
            });

            modelBuilder.Entity<SettingGroup>(entity =>
            {
                entity.ToTable("SettingGroup");

                entity.HasIndex(e => e.SettingGroupName, "UniqueSettingGroupName")
                    .IsUnique();

                entity.Property(e => e.SettingGroupId).HasColumnName("SettingGroupID");

                entity.Property(e => e.SettingGroupName)
                    .IsRequired()
                    .HasMaxLength(200);
            });

            modelBuilder.Entity<Settingtype>(entity =>
            {
                entity.ToTable("settingtype", "setting");

                entity.Property(e => e.Settingtypeid)
                    .HasColumnName("settingtypeid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Settingtypename)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("settingtypename");
            });

            modelBuilder.Entity<Settingtypelink>(entity =>
            {
                entity.HasKey(e => e.Settingid);

                entity.ToTable("settingtypelink", "setting");

                entity.Property(e => e.Settingid)
                    .ValueGeneratedNever()
                    .HasColumnName("settingid");

                entity.Property(e => e.Settingtypeid).HasColumnName("settingtypeid");

                entity.HasOne(d => d.Setting)
                    .WithOne(p => p.Settingtypelink)
                    .HasForeignKey<Settingtypelink>(d => d.Settingid)
                    .HasConstraintName("FK_settingtypelink_setting");

                entity.HasOne(d => d.Settingtype)
                    .WithMany(p => p.Settingtypelinks)
                    .HasForeignKey(d => d.Settingtypeid)
                    .HasConstraintName("FK_settingtypelink_settingtype");
            });

            modelBuilder.Entity<Shelf>(entity =>
            {
                entity.ToTable("Shelves", "Ops");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ShelfCode).HasMaxLength(250);

                entity.Property(e => e.ShelfName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("(N'')");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.Shelves)
                    .HasForeignKey(d => d.WarehouseId);
            });

            modelBuilder.Entity<SparBarcode>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("Spar_barcode", "Scanner");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ContractCode)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Contract code");

                entity.Property(e => e.MonthYr)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Month-Yr");

                entity.Property(e => e.Product)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.RegionClusterStore)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Region Cluster Store");

                entity.Property(e => e.ReqNo).HasColumnName("Req No.");

                entity.Property(e => e.SizeDesc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("Size Desc");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<SparScannerDataToolBarcodeRepository>(entity =>
            {
                entity.ToTable("SPAR_ScannerDataTool_BarcodeRepository", "Scanner");

                entity.HasIndex(e => new { e.Product, e.Brand, e.Barcode }, "C_SPAR_ScannerDataTool_BarcodeRepository")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => new { e.Product, e.Brand }, "C_Variant_SPAR_ScannerDataTool_BarcodeRepository")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Product)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.SizeDesc)
                    .HasMaxLength(300)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SparScannerDataToolBarcodeRequest>(entity =>
            {
                entity.ToTable("SPAR_ScannerDataTool_BarcodeRequest", "Scanner");

                entity.HasIndex(e => new { e.Variant, e.Brand }, "C_ContractNumber_SPAR_ScannerDataTool_BarcodeRequest")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => new { e.Variant, e.Brand, e.Barcode }, "C_SPAR_ScannerDataTool_BarcodeRequest")
                    .HasFillFactor((byte)75);

                entity.HasIndex(e => new { e.Variant, e.Brand }, "C_Variant_SPAR_ScannerDataTool_BarcodeRequest")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Brand)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.IsCompetitor).HasColumnName("isCompetitor");

                entity.Property(e => e.Variant)
                    .HasMaxLength(130)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SparScannerDataToolDataRequest>(entity =>
            {
                entity.ToTable("SPAR_ScannerDataTool_DataRequest", "Scanner");

                entity.HasIndex(e => new { e.Id, e.ContractNumber }, "C_SPAR_ScannerDataTool_DataRequest")
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.AlternativeCampaignEndDate).HasColumnType("datetime");

                entity.Property(e => e.AlternativeCampaignStartDate).HasColumnType("datetime");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(300)
                    .IsUnicode(false);

                entity.Property(e => e.PostPeriodEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_EndDate");

                entity.Property(e => e.PostPeriodIsDataReadyForPrc).HasColumnName("PostPeriod_isDataReadyForPRC");

                entity.Property(e => e.PostPeriodIsDataSubmittedForPrc).HasColumnName("PostPeriod_isDataSubmittedForPRC");

                entity.Property(e => e.PostPeriodIsSelectedForScannerData).HasColumnName("PostPeriod_isSelectedForScannerData");

                entity.Property(e => e.PostPeriodReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_ReadyDate");

                entity.Property(e => e.PostPeriodSelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_SelectedForScannerData_Date");

                entity.Property(e => e.PostPeriodStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_StartDate");

                entity.Property(e => e.PostPeriodSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PostPeriod_SubmissionDate");

                entity.Property(e => e.PriorPeriodEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_EndDate");

                entity.Property(e => e.PriorPeriodIsDataReadyForPrc).HasColumnName("PriorPeriod_isDataReadyForPRC");

                entity.Property(e => e.PriorPeriodIsDataSubmittedForPrc).HasColumnName("PriorPeriod_isDataSubmittedForPRC");

                entity.Property(e => e.PriorPeriodIsSelectedForScannerData).HasColumnName("PriorPeriod_isSelectedForScannerData");

                entity.Property(e => e.PriorPeriodReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_ReadyDate");

                entity.Property(e => e.PriorPeriodSelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_SelectedForScannerData_Date");

                entity.Property(e => e.PriorPeriodStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_StartDate");

                entity.Property(e => e.PriorPeriodSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorPeriod_SubmissionDate");

                entity.Property(e => e.PriorYearEndDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_EndDate");

                entity.Property(e => e.PriorYearIsDataReadyForPrc).HasColumnName("PriorYear_isDataReadyForPRC");

                entity.Property(e => e.PriorYearIsDataSubmittedForPrc).HasColumnName("PriorYear_isDataSubmittedForPRC");

                entity.Property(e => e.PriorYearIsSelectedForScannerData).HasColumnName("PriorYear_isSelectedForScannerData");

                entity.Property(e => e.PriorYearReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_ReadyDate");

                entity.Property(e => e.PriorYearSelectedForScannerDataDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_SelectedForScannerData_Date");

                entity.Property(e => e.PriorYearStartDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_StartDate");

                entity.Property(e => e.PriorYearSubmissionDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PriorYear_SubmissionDate");

                entity.Property(e => e.UseAlternativeDate).HasDefaultValueSql("((0))");
            });

            modelBuilder.Entity<SparScannerDataToolStoreLink>(entity =>
            {
                entity.ToTable("SPAR_ScannerDataTool_StoreLink", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.InstoreStoreCode)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("Instore_StoreCode");

                entity.Property(e => e.SparStoreId)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("Spar_StoreId");
            });

            modelBuilder.Entity<SparScannerDataToolStoreList>(entity =>
            {
                entity.ToTable("SPAR_ScannerDataTool_StoreList", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Province)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.StoreIdSpar).HasColumnName("StoreId_Spar");

                entity.Property(e => e.StoreName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SparScannerDataToolXmlErrorLogFile>(entity =>
            {
                entity.ToTable("SparScannerDataToolXmlErrorLogFile", "Scanner");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ErrorMessage).IsUnicode(false);
            });

            modelBuilder.Entity<StockTake>(entity =>
            {
                entity.ToTable("StockTake", "Ops");

                entity.Property(e => e.StockTakeId)
                    .HasColumnName("StockTakeID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.InstallationInstructions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.StockTakeDate).HasColumnType("datetime");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.StockTake)
                    .HasForeignKey(d => d.MediaId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockTake_Media");
            });

            modelBuilder.Entity<Questions>(entity =>
            {
                entity.ToTable("Questions", "Ops");

             

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
                entity.Property(e => e.Dormant)
               .IsRequired()
               .HasDefaultValueSql("((0))");

             

            });

            modelBuilder.Entity<InstallationScheduleQuestionsAndAnswers>(entity =>
            {
                entity.ToTable("InstallationScheduleQuestionsAndAnswers", "Ops");
                modelBuilder.Entity<InstallationScheduleQuestionsAndAnswers>()
                   .HasOne(bc => bc.InstallationScheduleCurrent)
                   .WithMany(b => b.InstallationScheduleQuestionsAndAnswers)
                   .HasForeignKey(bc => bc.InstallationActionId);

            });

            modelBuilder.Entity<QuestionsAndAnswers>(entity =>
            {
                entity.ToTable("QuestionsAndAnswers", "Ops");
                modelBuilder.Entity<QuestionsAndAnswers>()
               .HasKey(bc => bc.QuestionAnswerId);
                modelBuilder.Entity<QuestionsAndAnswers>()
                .HasKey(bc => new { bc.AnswerId, bc.QuestionId });
                modelBuilder.Entity<QuestionsAndAnswers>()
                    .HasOne(bc => bc.Answers)
                    .WithMany(b => b.QuestionsAndAnswers)
                    .HasForeignKey(bc => bc.AnswerId);
                modelBuilder.Entity<QuestionsAndAnswers>()
                    .HasOne(bc => bc.Questions)
                    .WithMany(c => c.QuestionsAndAnswers)
                    .HasForeignKey(bc => bc.QuestionId);

            });

            modelBuilder.Entity<Answers>(entity =>
            {
                entity.ToTable("Answers", "Ops");



                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
                entity.Property(e => e.Dormant)
               .IsRequired()
               .HasDefaultValueSql("((0))");

               

            });

            modelBuilder.Entity<Store>(entity =>
            {
                entity.ToTable("Store", "Store");

                entity.HasIndex(e => e.StoreNumber, "UniqueStoreNumber")
                    .IsUnique();

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.BillingAddressCityId)
                    .HasColumnName("BillingAddressCityID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.BillingAddressLine1)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.BillingAddressLine2)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.BillingAddressPostalCode)
                    .IsRequired()
                    .HasMaxLength(4)
                    .HasDefaultValueSql("((0))")
                    .IsFixedLength(true);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.DormantEffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.HeadOfficeId)
                    .HasColumnName("HeadOfficeID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.InstallationTeamId)
                    .HasColumnName("InstallationTeamID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.NoLongerDormantEndDate).HasColumnType("datetime");

                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PhysicalAddressCityId)
                    .HasColumnName("PhysicalAddressCityID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.PhysicalAddressLine1)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PhysicalAddressLine2)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PhysicalAddressPostalCode)
                    .IsRequired()
                    .HasMaxLength(4)
                    .HasDefaultValueSql("((0))")
                    .IsFixedLength(true);

                entity.Property(e => e.RegionID).HasColumnName("RegionID");

                entity.Property(e => e.SecondaryEmailAddress).HasMaxLength(1024);

                entity.Property(e => e.StoreGlcode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("StoreGLCode")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.StoreManagerEmailAddress).HasMaxLength(1024);

                entity.Property(e => e.StoreManagerName).HasMaxLength(255);

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreNumber)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.StoreVendorNumber)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.WarehouseId).HasColumnName("WarehouseID");

                entity.HasOne(d => d.BillingAddressCity)
                    .WithMany(p => p.StoreBillingAddressCities)
                    .HasForeignKey(d => d.BillingAddressCityId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Store_BillingAddressCity");

                entity.HasOne(d => d.HeadOffice)
                    .WithMany(p => p.Stores)
                    .HasForeignKey(d => d.HeadOfficeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Store_HeadOffice");

                entity.HasOne(d => d.InstallationDaysInstallationDay)
                    .WithMany(p => p.Stores)
                    .HasForeignKey(d => d.InstallationDaysInstallationDayId);

                entity.HasOne(d => d.InstallationTeam)
                    .WithMany(p => p.Stores)
                    .HasForeignKey(d => d.InstallationTeamId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Store_InstallationTeam");

                entity.HasOne(d => d.PhysicalAddressCity)
                    .WithMany(p => p.StorePhysicalAddressCities)
                    .HasForeignKey(d => d.PhysicalAddressCityId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Store_PhysicalAddressCity");

                entity.HasOne(d => d.Region)
                    .WithMany(p => p.Stores)
                    .HasForeignKey(d => d.RegionID)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Store_Region");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.Stores)
                    .HasForeignKey(d => d.WarehouseId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Store_Warehouse");
            });

            modelBuilder.Entity<StoreList>(entity =>
            {
                entity.HasKey(e => new { e.BurstId, e.StoreId });

                entity.ToTable("StoreList", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.HasOne(d => d.Burst)
                    .WithMany(p => p.StoreLists)
                    .HasForeignKey(d => d.BurstId)
                    .HasConstraintName("FK_StoreList_Burst");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StoreLists)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_StoreList_Store");
            });

            modelBuilder.Entity<StoreListBck20200608>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("StoreListBCK20200608", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<StoreMediaCategoryPermission>(entity =>
            {
                entity.ToTable("StoreMediaCategoryPermission", "Store");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.StoreMediaCategoryPermissions)
                    .HasForeignKey(d => d.CategoryId)
                    .HasConstraintName("fk_StoreMediaCategoryPermission_CategoryID");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.StoreMediaCategoryPermission)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("fk_StoreMediaCategoryPermission_MediaID");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StoreMediaCategoryPermissions)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("fk_StoreMediaCategoryPermission_StoreID");
            });

            modelBuilder.Entity<StoreMedia>(entity =>
            {
                entity.HasKey(e => new { e.StoreId, e.MediaId });

                entity.ToTable("StoreMedia", "Store");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.StoreMedia)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_StoreMedia_Media");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StoreMedia)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_StoreMedia_Store");
            });

            modelBuilder.Entity<StoreNotificationsSetup>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("StoreNotificationsSetup", "admin");

                entity.Property(e => e.StoreNotificationId).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<StorePool>(entity =>
            {
                entity.ToTable("StorePool", "Sales");

                entity.Property(e => e.StorePoolId)
                    .HasColumnName("StorePoolID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<StoreRentalRate>(entity =>
            {
                entity.HasKey(e => new { e.StoreId, e.MediaId, e.Homesite, e.EffectiveDate });

                entity.ToTable("StoreRentalRate", "Finance");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.EffectiveDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.RentalPercent).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.RentalRate).HasColumnType("money");

                entity.HasOne(d => d.Media)
                    .WithMany(p => p.StoreRentalRate)
                    .HasForeignKey(d => d.MediaId)
                    .HasConstraintName("FK_StoreRentalRate_Media");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StoreRentalRates)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StoreRentalRate_Store");
            });

            modelBuilder.Entity<StoreRentalRateStaging>(entity =>
            {
                entity.ToTable("StoreRentalRate_Staging");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.RentalPercent).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.RentalRate).HasColumnType("money");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<StoreSelectedForPayment>(entity =>
            {
                entity.ToTable("StoreSelectedForPayment", "TradeRights");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.PaymentClassificationId).HasColumnName("PaymentClassificationID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.HasOne(d => d.Chain)
                    .WithMany(p => p.StoreSelectedForPayments)
                    .HasForeignKey(d => d.ChainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StoreSelectedForPayment_ChainID");

                entity.HasOne(d => d.PaymentClassification)
                    .WithMany(p => p.StoreSelectedForPayments)
                    .HasForeignKey(d => d.PaymentClassificationId)
                    .HasConstraintName("FK_StoreSelectedForPayment_StoreSelectedForPayment_PaymentClassificationID");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StoreSelectedForPayments)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StoreSelectedForPayment_StoreID");
            });

            modelBuilder.Entity<StoreSelectedForPaymentBck>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("StoreSelectedForPaymentBCK", "TradeRights");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.Id)
                    .ValueGeneratedOnAdd()
                    .HasColumnName("ID");

                entity.Property(e => e.PaymentClassificationId).HasColumnName("PaymentClassificationID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<StorelistBckjune>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("StorelistBCKJUNE", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<StorelistBckjunev2>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("StorelistBCKJUNEV2", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<StoresByChainCount>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("StoresByChainCount", "reporting");

                entity.Property(e => e.Burstid).HasColumnName("burstid");

                entity.Property(e => e.ChainId).HasColumnName("chainId");

                entity.Property(e => e.Contractid).HasColumnName("contractid");
            });

            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.ToTable("Supplier", "Ops");

                entity.HasIndex(e => e.SupplierName, "UniqueSupplierName")
                    .IsUnique();

                entity.Property(e => e.SupplierId).HasColumnName("SupplierID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.SupplierAltNo).HasMaxLength(10);

                entity.Property(e => e.SupplierCellNo).HasMaxLength(10);

                entity.Property(e => e.SupplierEmailAddress).HasMaxLength(255);

                entity.Property(e => e.SupplierFaxNo).HasMaxLength(50);

                entity.Property(e => e.SupplierName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.SupplierRegistrationNumber).HasMaxLength(50);

                entity.Property(e => e.SupplierTelNo).HasMaxLength(10);

                entity.Property(e => e.SupplierVatNo).HasMaxLength(50);

                entity.Property(e => e.SupplierWebsite).HasMaxLength(255);
            });

            modelBuilder.Entity<SupplierInventory>(entity =>
            {
                entity.ToTable("SupplierInventory", "Ops");

                entity.Property(e => e.SupplierInventoryId).HasColumnName("SupplierInventoryID");

                entity.Property(e => e.SupplierId).HasColumnName("SupplierID");

                entity.HasOne(d => d.Inventory)
                    .WithMany(p => p.SupplierInventories)
                    .HasForeignKey(d => d.InventoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplierInventory_Inventory");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.SupplierInventories)
                    .HasForeignKey(d => d.SupplierId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplierInventory_Supplier");
            });

            modelBuilder.Entity<Systemuser>(entity =>
            {
                entity.HasKey(e => e.Userid);

                entity.ToTable("systemuser", "master");

                entity.HasIndex(e => e.Username, "unique_username")
                    .IsUnique()
                    .HasFillFactor((byte)75);

                entity.Property(e => e.Userid)
                    .HasColumnName("userid")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("email")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Firstname)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("firstname")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Lastname)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lastname")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Passwordsalt)
                    .IsRequired()
                    .HasMaxLength(55)
                    .HasColumnName("passwordsalt")
                    .HasDefaultValueSql("('')")
                    .IsFixedLength(true);

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("username")
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<Systemuserdeleted>(entity =>
            {
                entity.HasKey(e => new { e.Userid, e.Datemodified });

                entity.ToTable("systemuserdeleted", "security");

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Datemodified)
                    .HasColumnType("datetime")
                    .HasColumnName("datemodified")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Deleted)
                    .IsRequired()
                    .HasColumnName("deleted")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Modifiedbyuserid).HasColumnName("modifiedbyuserid");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.Systemuserdeleteds)
                    .HasForeignKey(d => d.Userid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_systemuserdeleted_systemuser");
            });

            modelBuilder.Entity<Systemuserenabled>(entity =>
            {
                entity.HasKey(e => new { e.Userid, e.Datemodified });

                entity.ToTable("systemuserenabled", "security");

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Datemodified)
                    .HasColumnType("datetime")
                    .HasColumnName("datemodified")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Enabled)
                    .IsRequired()
                    .HasColumnName("enabled")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Modifiedbyuserid).HasColumnName("modifiedbyuserid");

                entity.HasOne(d => d.Modifiedbyuser)
                    .WithMany(p => p.SystemuserenabledModifiedbyusers)
                    .HasForeignKey(d => d.Modifiedbyuserid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_systemuserenabled_systemuser1");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.SystemuserenabledUsers)
                    .HasForeignKey(d => d.Userid)
                    .HasConstraintName("FK_systemuserenabled_systemuser");
            });

            modelBuilder.Entity<TeamManager>(entity =>
            {
                entity.HasKey(e => new { e.TeamManagerId, e.InstallationTeamId });

                entity.ToTable("TeamManager", "Ops");

                entity.Property(e => e.TeamManagerId)
                    .HasColumnName("TeamManagerID")
                    .HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.InstallationTeam)
                    .WithMany(p => p.TeamManagers)
                    .HasForeignKey(d => d.InstallationTeamId);
            });

            modelBuilder.Entity<TempBrandNameWord>(entity =>
            {
                entity.HasKey(e => e.ProductId);

                entity.ToTable("tempBrandNameWords");

                entity.HasComment("A temporary table to store the number of words in the brand name of a product.  This table was created during the process to convert all competitor products for distribution to belong to a proper brand instead of the simple brand called \"Competitor\".");

                entity.Property(e => e.ProductId)
                    .ValueGeneratedNever()
                    .HasColumnName("ProductID");

                entity.Property(e => e.BrandWordCount).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<TempSparStoreStagingTable>(entity =>
            {
                entity.ToTable("TempSparStore_StagingTable");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Region)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StoreIdSpar).HasColumnName("StoreId_Spar");

                entity.Property(e => e.StoreName)
                    .HasMaxLength(1000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TempStoreGrouping>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("temp_StoreGrouping$");

                entity.Property(e => e.Chain).HasMaxLength(255);

                entity.Property(e => e.Group).HasMaxLength(255);

                entity.Property(e => e.NovaCode).HasMaxLength(255);

                entity.Property(e => e.PnPcode)
                    .HasMaxLength(255)
                    .HasColumnName("PnPCode");

                entity.Property(e => e.Region).HasMaxLength(255);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(255)
                    .HasColumnName("Store Name");
            });

            modelBuilder.Entity<Term>(entity =>
            {
                entity.HasKey(e => e.TermsId);

                entity.ToTable("Terms", "Client");

                entity.HasIndex(e => e.TermsName, "UniqueTermsName")
                    .IsUnique();

                entity.Property(e => e.TermsId).HasColumnName("TermsID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.TermsName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<ThisIsMine>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ThisIsMine", "reporting");

                entity.Property(e => e.ContractNumber).HasMaxLength(8);

                entity.Property(e => e._201601)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-01");

                entity.Property(e => e._201602)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-02");

                entity.Property(e => e._201603)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-03");

                entity.Property(e => e._201604)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-04");

                entity.Property(e => e._201605)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-05");

                entity.Property(e => e._201606)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-06");

                entity.Property(e => e._201607)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-07");

                entity.Property(e => e._201608)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-08");

                entity.Property(e => e._201609)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-09");

                entity.Property(e => e._201610)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-10");

                entity.Property(e => e._201611)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-11");

                entity.Property(e => e._201612)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2016-12");

                entity.Property(e => e._201701)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-01");

                entity.Property(e => e._201702)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-02");

                entity.Property(e => e._201703)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-03");

                entity.Property(e => e._201704)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-04");

                entity.Property(e => e._201705)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-05");

                entity.Property(e => e._201706)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-06");

                entity.Property(e => e._201707)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-07");

                entity.Property(e => e._201708)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-08");

                entity.Property(e => e._201709)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-09");

                entity.Property(e => e._201710)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-10");

                entity.Property(e => e._201711)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-11");

                entity.Property(e => e._201712)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2017-12");

                entity.Property(e => e._201801)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-01");

                entity.Property(e => e._201802)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-02");

                entity.Property(e => e._201803)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-03");

                entity.Property(e => e._201804)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-04");

                entity.Property(e => e._201805)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-05");

                entity.Property(e => e._201806)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-06");

                entity.Property(e => e._201807)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-07");

                entity.Property(e => e._201808)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-08");

                entity.Property(e => e._201809)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-09");

                entity.Property(e => e._201810)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-10");

                entity.Property(e => e._201811)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-11");

                entity.Property(e => e._201812)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2018-12");

                entity.Property(e => e._201901)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-01");

                entity.Property(e => e._201902)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-02");

                entity.Property(e => e._201903)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-03");

                entity.Property(e => e._201904)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-04");

                entity.Property(e => e._201905)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-05");

                entity.Property(e => e._201906)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-06");

                entity.Property(e => e._201907)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-07");

                entity.Property(e => e._201908)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-08");

                entity.Property(e => e._201909)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-09");

                entity.Property(e => e._201910)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-10");

                entity.Property(e => e._201911)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-11");

                entity.Property(e => e._201912)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("2019-12");
            });

            modelBuilder.Entity<Transactor>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("transactor", "security");

                entity.HasComment("This table must contain only one row.  The values contained in the row must match the username and password for the SQL login used by the Nova application to connect to the SQL server and execute commands in the database.");

                entity.Property(e => e.Password)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("password")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("username")
                    .HasDefaultValueSql("('')");
            });

            modelBuilder.Entity<TypeOfPayment>(entity =>
            {
                entity.ToTable("TypeOfPayment", "TradeRights");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Type)
                    .IsRequired()
                    .HasMaxLength(250)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<UnitsPerShrink>(entity =>
            {
                entity.HasKey(e => new { e.ProductId, e.PackSizeId });

                entity.ToTable("UnitsPerShrink", "Ops");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DefaultUnitsPerShrink).HasDefaultValueSql("((12))");

                entity.HasOne(d => d.PackSize)
                    .WithMany(p => p.UnitsPerShrinks)
                    .HasForeignKey(d => d.PackSizeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UnitsPerShrink_PackSize");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.UnitsPerShrinks)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UnitsPerShrink_Product");
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("users", "CRM");

                entity.Property(e => e.Accessstring)
                    .HasMaxLength(255)
                    .HasColumnName("accessstring");

                entity.Property(e => e.LoginId)
                    .HasMaxLength(50)
                    .HasColumnName("login_id");

                entity.Property(e => e.Password)
                    .HasMaxLength(50)
                    .HasColumnName("password");
            });

            modelBuilder.Entity<User1>(entity =>
            {
                entity.HasKey(e => e.UserId);

                entity.ToTable("User");

                entity.HasIndex(e => e.Username, "UniqueUsername")
                    .IsUnique();

                entity.Property(e => e.UserId)
                    .HasColumnName("UserID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.UserPassword)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<V2CurrentStoreRentalRate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("v2_CurrentStoreRentalRate", "Finance");

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.PaymentClassificationId).HasColumnName("PaymentClassificationID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.TypeOfPaymentId).HasColumnName("TypeOfPaymentID");
            });

            modelBuilder.Entity<V2StoreRentalDue>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("v2_StoreRentalDue", "Finance");

                entity.Property(e => e.Category)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType)
                    .IsRequired()
                    .HasMaxLength(9)
                    .IsUnicode(false);

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.HeadOfficeId).HasColumnName("HeadOfficeID");

                entity.Property(e => e.HeadOfficeName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.HeadOfficePaymentPercent).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.Media)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.Product)
                    .IsRequired()
                    .HasMaxLength(211);

                entity.Property(e => e.Region)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreDescription)
                    .IsRequired()
                    .HasMaxLength(353);

                entity.Property(e => e.StoreGlcode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("StoreGLCode");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StorePaymentPercent).HasColumnType("decimal(6, 2)");

                entity.Property(e => e.StoreVendorNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Week).HasColumnType("datetime");

                entity.Property(e => e.WeeklyCrossoverRevenuePerBillableWeekPerStore).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.WeeklyRevenuePerBillableWeekPerStore).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<VAccountManagersByPermissionEditMyClient>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vAccountManagersByPermission_EditMyClients", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");
            });

            modelBuilder.Entity<VAccountManagersByPermissionEditMyContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vAccountManagersByPermission_EditMyContracts", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");
            });

            modelBuilder.Entity<VAccountManagersByPermissionEditMyProvisionalBooking>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vAccountManagersByPermission_EditMyProvisionalBookings", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");
            });

            modelBuilder.Entity<VAccountManagersByPermissionViewMyContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vAccountManagersByPermission_ViewMyContracts", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");
            });

            modelBuilder.Entity<VBaccount>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBAccount", "Sales");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");
            });

            modelBuilder.Entity<VBaseRevenue>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBase_Revenue", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.AgencyCommAmount).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.AgencyCommPercentage).HasColumnType("decimal(16, 2)");

                entity.Property(e => e.BasicRental).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.BasicRentalPlusLoadingFees).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.NetRentalAfterDiscount).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.RecognisedRevenue).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.RecognisedWeeklyRevenue).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.RentalRate).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.TotalLoadingFeeAmount).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.TotalLoadingFeePercentage).HasColumnType("decimal(38, 2)");

                entity.Property(e => e.WeeklyRevenuePerBillableWeek).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<VBaseRevenueContracthardcopy>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBase_Revenue_contracthardcopy", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.AgencyCommAmount).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.AgencyCommPercentage).HasColumnType("decimal(16, 2)");

                entity.Property(e => e.BasicRental).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.BasicRentalPlusLoadingFees).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.NetRentalAfterDiscount).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.RecognisedRevenue).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.RecognisedWeeklyRevenue).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.RentalRate).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.TotalLoadingFeeAmount).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.TotalLoadingFeePercentage).HasColumnType("decimal(38, 2)");

                entity.Property(e => e.WeeklyRevenuePerBillableWeek).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<VBaseRevenueUnsigned>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBase_Revenue_unsigned", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.AgencyCommAmount).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.AgencyCommPercentage).HasColumnType("decimal(16, 2)");

                entity.Property(e => e.BasicRental).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.BasicRentalPlusLoadingFees).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.NetRentalAfterDiscount).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.RecognisedRevenue).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.RecognisedWeeklyRevenue).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.RentalRate).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.TotalLoadingFeeAmount).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.TotalLoadingFeePercentage).HasColumnType("decimal(38, 2)");

                entity.Property(e => e.WeeklyRevenuePerBillableWeek).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<VBillingInstructionSplit>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBillingInstructionSplit", "Finance");

                entity.Property(e => e.BillingAmountExcludingProduction).HasColumnType("decimal(21, 4)");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ManagementFee).HasColumnType("money");

                entity.Property(e => e.ProductionAmount).HasColumnType("money");

                entity.Property(e => e.Year)
                    .HasMaxLength(4)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<VBottomEndDistributionStore>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBottomEndDistributionStores", "Ops");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(303);

                entity.Property(e => e.StoreNumber)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.VanId).HasColumnName("VanID");
            });

            modelBuilder.Entity<VBrandAccountManagersDate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBrandAccountManagersDates", "Client");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.From).HasColumnType("datetime");

                entity.Property(e => e.To).HasColumnType("datetime");
            });

            modelBuilder.Entity<VBrandFamiliesByPermissionEditMyClient>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBrandFamiliesByPermission_EditMyClients", "Client");

                entity.Property(e => e.BrandFamilyId).HasColumnName("BrandFamilyID");
            });

            modelBuilder.Entity<VBrandsByPermissionEditMyClient>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBrandsByPermission_EditMyClients", "Client");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");
            });

            modelBuilder.Entity<VBrandsByPermissionEditMyProvisionalBooking>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBrandsByPermission_EditMyProvisionalBookings", "Client");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");
            });

            modelBuilder.Entity<VBurstCategory>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBurstCategory", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");
            });

            modelBuilder.Entity<VBurstDetailsByPermissionViewMyContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBurstDetailsByPermission_ViewMyContracts", "Sales");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(200);
            });

            modelBuilder.Entity<VBurstPeriodCoveredByProvisionalBooking>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBurstPeriodCoveredByProvisionalBookings", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");
            });

            modelBuilder.Entity<VBurstQueuedBehindCompetingBrand>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBurstQueuedBehindCompetingBrand", "Sales");

                entity.Property(e => e.BookTime).HasColumnType("datetime");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ExpiryTime).HasColumnType("datetime");

                entity.Property(e => e.MediaFamilyName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<VBurstWeekQtyCoveredByProvisionalBooking>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vBurstWeekQtyCoveredByProvisionalBookings", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.MediaFamilyId).HasColumnName("MediaFamilyID");
            });

            modelBuilder.Entity<VClientAccountManagerDate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vClientAccountManagerDates", "Client");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.From).HasColumnType("datetime");

                entity.Property(e => e.To).HasColumnType("datetime");
            });

            modelBuilder.Entity<VClientAccountManagerPeriodsWithActivity>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vClientAccountManagerPeriodsWithActivity", "Client");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.From).HasColumnType("datetime");
            });

            modelBuilder.Entity<VClientHistoryReadOnlyContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vClientHistoryReadOnlyContracts", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");
            });

            modelBuilder.Entity<VClientsImPermittedToSee>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vClientsImPermittedToSee", "Client");

                entity.Property(e => e.AccountManagerName)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.AddressLine1)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.AddressLine2)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.CityId).HasColumnName("CityID");

                entity.Property(e => e.CityName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ClassificationId).HasColumnName("ClassificationID");

                entity.Property(e => e.ClassificationName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ClientAbbreviation)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.Fax)
                    .IsRequired()
                    .HasMaxLength(10);

                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasMaxLength(2000);

                entity.Property(e => e.PostalCode)
                    .IsRequired()
                    .HasMaxLength(4);

                entity.Property(e => e.Telephone)
                    .IsRequired()
                    .HasMaxLength(10);

                entity.Property(e => e.TermsId).HasColumnName("TermsID");

                entity.Property(e => e.TermsName)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<VCompetingBrand>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCompetingBrands", "Sales");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CompetingBrandId).HasColumnName("CompetingBrandID");
            });

            modelBuilder.Entity<VCompetingMedium>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCompetingMedia", "Media");

                entity.Property(e => e.CompetingMediaId).HasColumnName("CompetingMediaID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");
            });

            modelBuilder.Entity<VContractActivityDate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContractActivityDate", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.PonumberCaptured).HasColumnName("PONumberCaptured");
            });

            modelBuilder.Entity<VContractDate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContractDates", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastBillableWeek).HasColumnType("datetime");

                entity.Property(e => e.LastInstallWeek).HasColumnType("datetime");
            });

            modelBuilder.Entity<VContractDatesWithBurst>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContractDatesWithBurst", "Sales");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastBillableWeek).HasColumnType("datetime");

                entity.Property(e => e.LastInstallWeek).HasColumnType("datetime");
            });

            modelBuilder.Entity<VContractProduction>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContractProduction", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.PeriodId).HasColumnName("PeriodID");

                entity.Property(e => e.ProductionAmount).HasColumnType("money");
            });

            modelBuilder.Entity<VContractScannerDataStatus>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContract_ScannerData_Status", "Scanner");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Number)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");
            });

            modelBuilder.Entity<VContractScannerDataStatusDetail>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContract_ScannerData_Status_Detail", "Scanner");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Number)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.RequestId).HasColumnName("request_id");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");

                entity.Property(e => e.SubmitTime)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time");

                entity.Property(e => e.SubmitTimeClicks)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Clicks");

                entity.Property(e => e.SubmitTimeSpar)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Spar");
            });

            modelBuilder.Entity<VContractsByPermissionEditMyContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContractsByPermission_EditMyContracts", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");
            });

            modelBuilder.Entity<VContractsByPermissionViewMyContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vContractsByPermission_ViewMyContracts", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");
            });

            modelBuilder.Entity<VCrmStatusView>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCRM_Status_View", "CRM");

                entity.Property(e => e.AccountManager)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("Account_Manager");

                entity.Property(e => e.ClientName).HasMaxLength(150);

                entity.Property(e => e.ContactDate).HasColumnType("datetime");

                entity.Property(e => e.ContactName)
                    .HasMaxLength(50)
                    .HasColumnName("Contact_Name");

                entity.Property(e => e.Country)
                    .IsRequired()
                    .HasMaxLength(30);

                entity.Property(e => e.CreatedDt)
                    .HasColumnType("datetime")
                    .HasColumnName("Created_dt");

                entity.Property(e => e.CreationDate)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("Creation_Date");

                entity.Property(e => e.CreationMonth)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("Creation_Month");

                entity.Property(e => e.InternalId).HasColumnName("InternalID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ProposalStatus)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("Proposal_Status");

                entity.Property(e => e.ProposalValue)
                    .HasColumnType("money")
                    .HasColumnName("Proposal_Value");

                entity.Property(e => e.StatusCount).HasColumnName("Status_count");

                entity.Property(e => e.StatusDate)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("Status_Date");

                entity.Property(e => e.StatusDesc).HasMaxLength(50);

                entity.Property(e => e.StatusMonth)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("Status_Month");
            });

            modelBuilder.Entity<VCurrentBrandAccountManager>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCurrentBrandAccountManager", "Client");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.AccountManagerName)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.BrandId).HasColumnName("BrandID");
            });

            modelBuilder.Entity<VCurrentClientAccountManager>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCurrentClientAccountManager", "Client");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.AccountManagerName)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<VCurrentMediaService>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCurrentMediaServices", "Media");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);
            });

            modelBuilder.Entity<VCurrentStoreRentalRate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vCurrentStoreRentalRate", "Finance");

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.RentalPercent).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.RentalRate).HasColumnType("money");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<VDistFullCycleCheckByVanByStore>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vDist_FullCycleCheckByVanByStore");

                entity.HasComment("A temporary view to fix data capture errors which occured when capturers skipped over products that had no ticks on the distribution check sheet.");

                entity.Property(e => e.PackSizeId).HasColumnName("PackSizeID");

                entity.Property(e => e.ProductId).HasColumnName("ProductID");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.VanId).HasColumnName("VanID");
            });

            modelBuilder.Entity<VEffectiveBurst>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vEffectiveBurst", "Sales");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.Discount).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.InstallationInstructions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.LastBillableWeek).HasColumnType("datetime");

                entity.Property(e => e.LastInstallWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaFamilyId).HasColumnName("MediaFamilyID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.RentalRate).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.StorePoolId).HasColumnName("StorePoolID");
            });

            modelBuilder.Entity<VEffectiveProvisionalBooking>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vEffectiveProvisionalBooking", "Sales");

                entity.Property(e => e.BookTime).HasColumnType("datetime");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ExpiryTime).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaFamilyId).HasColumnName("MediaFamilyID");

                entity.Property(e => e.ProvisionalBookingId).HasColumnName("ProvisionalBookingID");

                entity.Property(e => e.ProvisionalBookingName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<VExecutoryContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vExecutoryContract", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);
            });

            modelBuilder.Entity<VInstallationRevenueByStore>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vInstallationRevenueByStore", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Revenue).HasColumnType("decimal(38, 24)");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<VInstallationsByPolicyByBurst>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vInstallationsByPolicyByBurst", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");
            });

            modelBuilder.Entity<VInventoryQtyPriceDate>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vInventoryQtyPriceDates", "Ops");

                entity.Property(e => e.CostPrice).HasColumnType("money");

                entity.Property(e => e.From).HasColumnType("datetime");

                entity.Property(e => e.ItemQtyId).HasColumnName("ItemQtyID");

                entity.Property(e => e.ItemQtyPriceId).HasColumnName("ItemQtyPriceID");

                entity.Property(e => e.SellPrice).HasColumnType("money");

                entity.Property(e => e.To).HasColumnType("datetime");
            });

            modelBuilder.Entity<VMediaService>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vMediaServices", "Media");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);
            });

            modelBuilder.Entity<VMonthlyRevenueBreakdown>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vMonthlyRevenueBreakdown", "Finance");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.Billingamountexcludingproduction)
                    .HasColumnType("decimal(21, 4)")
                    .HasColumnName("billingamountexcludingproduction");

                entity.Property(e => e.BrandName).HasMaxLength(50);

                entity.Property(e => e.CalendarDate)
                    .IsRequired()
                    .HasMaxLength(69)
                    .IsUnicode(false)
                    .HasColumnName("Calendar_Date");

                entity.Property(e => e.CalendarMonth).HasColumnName("Calendar_month");

                entity.Property(e => e.CalendarYear).HasColumnName("Calendar_year");

                entity.Property(e => e.ClientBilling).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.EndDate).HasColumnType("datetime");

                entity.Property(e => e.MediaName).HasMaxLength(200);

                entity.Property(e => e.MonthName)
                    .HasMaxLength(9)
                    .IsUnicode(false)
                    .HasColumnName("month_name");

                entity.Property(e => e.PreBilling).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.ProductionAmount).HasColumnType("money");

                entity.Property(e => e.RevenueRecognition).HasColumnType("decimal(38, 4)");

                entity.Property(e => e.StartDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<VMyMobilityExport>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vMyMobilityExport", "Ops");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("Category Name");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.CommencementDate).HasColumnName("Commencement Date");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.DayOfCommencementDate)
                    .HasMaxLength(8000)
                    .IsUnicode(false)
                    .HasColumnName("Day of Commencement Date");

                entity.Property(e => e.DayOfLastModified).HasColumnName("Day of Last modified");

                entity.Property(e => e.DayOfTerminationDate)
                    .HasMaxLength(8000)
                    .IsUnicode(false)
                    .HasColumnName("Day of Termination date");

                entity.Property(e => e.ForDate)
                    .HasMaxLength(12)
                    .IsFixedLength(true);

                entity.Property(e => e.Group)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false);

                entity.Property(e => e.JobNumber)
                    .IsRequired()
                    .HasMaxLength(8)
                    .HasColumnName("Job Number");

                entity.Property(e => e.LastModified)
                    .HasMaxLength(19)
                    .HasColumnName("Last Modified");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last modified by");

                entity.Property(e => e.MediaType)
                    .IsRequired()
                    .HasMaxLength(213)
                    .HasColumnName("Media Type");

                entity.Property(e => e.MonthOfCommencementDate).HasColumnName("Month of Commencement date");

                entity.Property(e => e.MonthOfLastModified).HasColumnName("Month of Last modified");

                entity.Property(e => e.MonthOfTerminationDate).HasColumnName("Month of Termination date");

                entity.Property(e => e.NumberOfWeeks).HasColumnName("Number of weeks");

                entity.Property(e => e.Product)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.QuarterOfCommencementDate).HasColumnName("Quarter of Commencement date");

                entity.Property(e => e.QuarterOfLastModified).HasColumnName("Quarter of Last modified");

                entity.Property(e => e.QuarterOfTerminationDate).HasColumnName("Quarter of Termination date");

                entity.Property(e => e.Region)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.SpecialInstructions)
                    .HasMaxLength(8000)
                    .IsUnicode(false)
                    .HasColumnName("Special Instructions");

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoId).HasColumnName("sto_id");

                entity.Property(e => e.Store)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreAndChain).HasColumnName("Store and Chain");

                entity.Property(e => e.Teamlist).HasColumnName("teamlist");

                entity.Property(e => e.TerminationDate).HasColumnName("Termination Date");

                entity.Property(e => e.WeekOfCommencementDate).HasColumnName("Week of Commencement date");

                entity.Property(e => e.WeekOfLastModified).HasColumnName("Week of Last modified");

                entity.Property(e => e.WeekOfTerminationDate).HasColumnName("Week of Termination date");

                entity.Property(e => e.YearOfCommencementDate).HasColumnName("Year of Commencement date");

                entity.Property(e => e.YearOfLastModified).HasColumnName("Year of Last modified");

                entity.Property(e => e.YearOfTerminationDate).HasColumnName("Year of Termination date");
            });

            modelBuilder.Entity<VMyMobilityExportWithDays>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vMyMobilityExportWithDays", "Ops");

                entity.Property(e => e.CategoryName)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("Category Name");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.CommencementDate).HasColumnName("Commencement Date");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.DayOfCommencementDate)
                    .HasMaxLength(8000)
                    .IsUnicode(false)
                    .HasColumnName("Day of Commencement Date");

                entity.Property(e => e.DayOfLastModified).HasColumnName("Day of Last modified");

                entity.Property(e => e.DayOfTerminationDate)
                    .HasMaxLength(8000)
                    .IsUnicode(false)
                    .HasColumnName("Day of Termination date");

                entity.Property(e => e.ForDate)
                    .HasMaxLength(12)
                    .IsFixedLength(true);

                entity.Property(e => e.Group)
                    .IsRequired()
                    .HasMaxLength(6)
                    .IsUnicode(false);

                entity.Property(e => e.InstallationDay)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Installationteamid).HasColumnName("installationteamid");

                entity.Property(e => e.JobNumber)
                    .IsRequired()
                    .HasMaxLength(8)
                    .HasColumnName("Job Number");

                entity.Property(e => e.LastModified)
                    .HasMaxLength(19)
                    .HasColumnName("Last Modified");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last modified by");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaType)
                    .IsRequired()
                    .HasMaxLength(213)
                    .HasColumnName("Media Type");

                entity.Property(e => e.MonthOfCommencementDate).HasColumnName("Month of Commencement date");

                entity.Property(e => e.MonthOfLastModified).HasColumnName("Month of Last modified");

                entity.Property(e => e.MonthOfTerminationDate).HasColumnName("Month of Termination date");

                entity.Property(e => e.NumberOfWeeks).HasColumnName("Number of weeks");

                entity.Property(e => e.Product)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.QuarterOfCommencementDate).HasColumnName("Quarter of Commencement date");

                entity.Property(e => e.QuarterOfLastModified).HasColumnName("Quarter of Last modified");

                entity.Property(e => e.QuarterOfTerminationDate).HasColumnName("Quarter of Termination date");

                entity.Property(e => e.Region)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.SpecialInstructions)
                    .HasMaxLength(8000)
                    .IsUnicode(false)
                    .HasColumnName("Special Instructions");

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoId).HasColumnName("sto_id");

                entity.Property(e => e.Store)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreAndChain).HasColumnName("Store and Chain");

                entity.Property(e => e.Teamlist).HasColumnName("teamlist");

                entity.Property(e => e.TerminationDate).HasColumnName("Termination Date");

                entity.Property(e => e.WeekOfCommencementDate).HasColumnName("Week of Commencement date");

                entity.Property(e => e.WeekOfLastModified).HasColumnName("Week of Last modified");

                entity.Property(e => e.WeekOfTerminationDate).HasColumnName("Week of Termination date");

                entity.Property(e => e.YearOfCommencementDate).HasColumnName("Year of Commencement date");

                entity.Property(e => e.YearOfLastModified).HasColumnName("Year of Last modified");

                entity.Property(e => e.YearOfTerminationDate).HasColumnName("Year of Termination date");
            });

            modelBuilder.Entity<VNovaStoreList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vNova_StoreList", "Scanner");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.NovaAddress)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaCity)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaHeadOffice)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaId).HasColumnName("Nova_ID");

                entity.Property(e => e.NovaName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaNumber)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaRegion)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.PnpRegion)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region");

                entity.Property(e => e.PnpstoreCode)
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.ScannerStoreId).HasColumnName("ScannerStoreID");

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");

                entity.Property(e => e.StoreType1)
                    .HasMaxLength(50)
                    .HasColumnName("StoreType");
            });

            modelBuilder.Entity<VNovaStoreListAll>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vNova_StoreList_All", "Scanner");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.NovaAddress)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaCity)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaHeadOffice)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaId).HasColumnName("Nova_ID");

                entity.Property(e => e.NovaName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaNumber)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaRegion)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.PnpRegion)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region");

                entity.Property(e => e.PnpRegion2)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region2");

                entity.Property(e => e.PnpstoreCode)
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.PnpstoreCode2)
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode2");

                entity.Property(e => e.ScannerStoreId).HasColumnName("ScannerStoreID");

                entity.Property(e => e.ScannerStoreId2).HasColumnName("ScannerStoreID2");

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");
            });

            modelBuilder.Entity<VNovaStoreListLiquor>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vNova_StoreList_liquor", "Scanner");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.NovaAddress)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaCity)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaHeadOffice)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaId).HasColumnName("Nova_ID");

                entity.Property(e => e.NovaName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaNumber)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaRegion)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.PnpRegion)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region");

                entity.Property(e => e.PnpstoreCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.ScannerStoreId).HasColumnName("ScannerStoreID");

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");
            });

            modelBuilder.Entity<VPnPchainSplit>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vPnPChainSplit", "Sales");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.StoreId).HasColumnName("StoreID");
            });

            modelBuilder.Entity<VPolicyBurst>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vPolicyBursts", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");
            });

            modelBuilder.Entity<VProductionByContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vProductionByContract", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ProductionAmount).HasColumnType("money");
            });

            modelBuilder.Entity<VProductionRevenue>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vProductionRevenue");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.Period)
                    .HasMaxLength(7)
                    .IsUnicode(false);

                entity.Property(e => e.PriorProductionRevenue).HasColumnType("money");

                entity.Property(e => e.ProductionRevenue).HasColumnType("money");
            });

            modelBuilder.Entity<VRentalBreakdown>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vRentalBreakdown", "Sales");

                entity.Property(e => e.AssignedAccountManagerId).HasColumnName("AssignedAccountManagerID");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractAccountManagerId).HasColumnName("ContractAccountManagerID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CurrentAccountManagerId).HasColumnName("CurrentAccountManagerID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.ProductionAmount).HasColumnType("money");

                entity.Property(e => e.Rental).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<VRentalBreakdownUnsigned>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vRentalBreakdownUnsigned", "Sales");

                entity.Property(e => e.AssignedAccountManagerId).HasColumnName("AssignedAccountManagerID");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractAccountManagerId).HasColumnName("ContractAccountManagerID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CurrentAccountManagerId).HasColumnName("CurrentAccountManagerID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.Rental).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<VRentalBreakdownWithPriorYear>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vRentalBreakdownWithPriorYear", "Sales");

                entity.Property(e => e.AssignedAccountManagerId).HasColumnName("AssignedAccountManagerID");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractAccountManagerId).HasColumnName("ContractAccountManagerID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CurrentAccountManagerId).HasColumnName("CurrentAccountManagerID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.PriorProductionActual).HasColumnType("money");

                entity.Property(e => e.PriorProductionForecast).HasColumnType("money");

                entity.Property(e => e.PriorRentalActual).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorRentalForecast).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.ProductionAmount).HasColumnType("money");

                entity.Property(e => e.Rental).HasColumnType("decimal(38, 6)");
            });

            modelBuilder.Entity<VRentalBreakdownWithPriorYearUnsigned>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vRentalBreakdownWithPriorYearUnsigned", "Sales");

                entity.Property(e => e.AssignedAccountManagerId).HasColumnName("AssignedAccountManagerID");

                entity.Property(e => e.BrandId).HasColumnName("BrandID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ClientId).HasColumnName("ClientID");

                entity.Property(e => e.ContractAccountManagerId).HasColumnName("ContractAccountManagerID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CurrentAccountManagerId).HasColumnName("CurrentAccountManagerID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.PriorRentalActual).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.PriorRentalForecast).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.Rental).HasColumnType("decimal(38, 6)");
            });

            modelBuilder.Entity<VRevenueCalculation>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vRevenueCalculations", "reporting");

                entity.Property(e => e.ClientSBillingInstruction)
                    .HasColumnType("decimal(20, 4)")
                    .HasColumnName("Client's Billing Instruction");

                entity.Property(e => e.ContractNo)
                    .IsRequired()
                    .HasMaxLength(8)
                    .HasColumnName("Contract No");

                entity.Property(e => e.MonthNames).HasMaxLength(30);

                entity.Property(e => e.MonthSRevenueRecognition)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("Month's Revenue Recognition");

                entity.Property(e => e.PreBilling)
                    .HasColumnType("decimal(38, 4)")
                    .HasColumnName("Pre Billing");
            });

            modelBuilder.Entity<VRevenuePercentageByCategory>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vRevenuePercentageByCategory", "Sales");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType).HasMaxLength(9);

                entity.Property(e => e.PercentOfRevenue).HasColumnType("decimal(38, 6)");
            });

            modelBuilder.Entity<VScannerContractList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vScanner_Contract_list", "Scanner");

                entity.Property(e => e.AcountManager)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.Am)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("AM");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Brand).HasMaxLength(200);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaName).HasMaxLength(200);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoreCount).HasColumnName("Store_Count");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.TerminationDate).HasColumnType("datetime");

                entity.Property(e => e.TotalWeeks).HasColumnName("Total Weeks");
            });

            modelBuilder.Entity<VSelectsummarySet>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vSelectsummarySets", "Scanner");

                entity.Property(e => e.AccountManager)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("Account_Manager");

                entity.Property(e => e.AccountManagerId).HasColumnName("AccountManagerID");

                entity.Property(e => e.ClicksCount).HasColumnName("Clicks_Count");

                entity.Property(e => e.ContractCount).HasColumnName("Contract_Count");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDatePnp)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_PNP");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.ScannerGroup)
                    .HasMaxLength(150)
                    .HasColumnName("Scanner_Group");

                entity.Property(e => e.SparCount).HasColumnName("Spar_Count");

                entity.Property(e => e.StoreCount).HasColumnName("Store_Count");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDatePnp)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_PNP");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");

                entity.Property(e => e.SubmitTime)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time");

                entity.Property(e => e.SubmitTimeClicks)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Clicks");

                entity.Property(e => e.SubmitTimeSpar)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Spar");
            });

            modelBuilder.Entity<VSparContractList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vSpar_Contract_list", "Scanner");

                entity.Property(e => e.AcountManager)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.Am)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("AM");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Brand).HasMaxLength(200);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaName).HasMaxLength(200);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoreCount).HasColumnName("Store_Count");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.TerminationDate).HasColumnType("datetime");

                entity.Property(e => e.TotalWeeks).HasColumnName("Total Weeks");
            });

            modelBuilder.Entity<VStorePaymentRatesExport>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vStorePaymentRatesExport", "TradeRights");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.Media)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.Region)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.RentalPercent).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.RentalRate).HasColumnType("money");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreNumber)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<VStoreRentalDue>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vStoreRentalDue", "Finance");

                entity.Property(e => e.Category)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.CategoryId).HasColumnName("CategoryID");

                entity.Property(e => e.CategoryType)
                    .IsRequired()
                    .HasMaxLength(9)
                    .IsUnicode(false);

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.HeadOfficeId).HasColumnName("HeadOfficeID");

                entity.Property(e => e.HeadOfficeName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.HeadOfficeRentalDue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.Media)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.Product)
                    .IsRequired()
                    .HasMaxLength(211);

                entity.Property(e => e.Region)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreDescription)
                    .IsRequired()
                    .HasMaxLength(353);

                entity.Property(e => e.StoreGlcode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("StoreGLCode");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreRentalDue).HasColumnType("decimal(38, 6)");

                entity.Property(e => e.StoreVendorNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.TotalRentalDue).HasColumnType("decimal(38, 8)");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<VStoreUpdateList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vStoreUpdateList", "Sales");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.EffectiveDate).HasColumnType("datetime");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.RegionName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.RentalPercent).HasColumnType("decimal(5, 2)");

                entity.Property(e => e.RentalRate).HasColumnType("money");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreNumber)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<VStorecountByChainByMedium>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vStorecountByChainByMedia", "Store");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.MediaId).HasColumnName("MediaID");
            });

            modelBuilder.Entity<VViewableContractInfo>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vViewableContractInfo", "Sales");

                entity.Property(e => e.AccountManager)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.ProjectName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<VViewableProvisionalBookingInfo>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vViewableProvisionalBookingInfo", "Sales");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ProvisionalBookingId).HasColumnName("ProvisionalBookingID");

                entity.Property(e => e.ProvisionalBookingName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<VVisibleContractList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vVisibleContractList", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");
            });

            modelBuilder.Entity<VWeeklyProductionRevenue>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vWeeklyProductionRevenue", "reporting");

                entity.Property(e => e.AssignedAm)
                    .HasMaxLength(151)
                    .HasColumnName("AssignedAM");

                entity.Property(e => e.ClientName).HasMaxLength(150);

                entity.Property(e => e.ContractAm)
                    .HasMaxLength(151)
                    .HasColumnName("ContractAM");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber).HasMaxLength(8);

                entity.Property(e => e.CurrentAm)
                    .HasMaxLength(151)
                    .HasColumnName("CurrentAM");

                entity.Property(e => e.CurrentProduction).HasColumnType("money");

                entity.Property(e => e.Period).HasMaxLength(7);

                entity.Property(e => e.PriorForecastProduction).HasColumnType("money");

                entity.Property(e => e.PriorProduction).HasColumnType("money");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<Van>(entity =>
            {
                entity.ToTable("Van", "Distribution");

                entity.Property(e => e.VanId)
                    .HasColumnName("VanID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.VanName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.VanRegionId).HasColumnName("VanRegionID");

                entity.HasOne(d => d.VanRegion)
                    .WithMany(p => p.Vans)
                    .HasForeignKey(d => d.VanRegionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Van_VanRegion");
            });

            modelBuilder.Entity<Van1>(entity =>
            {
                entity.HasKey(e => e.VanId);

                entity.ToTable("Van", "Ops");

                entity.HasIndex(e => e.VanName, "UniqueVanName")
                    .IsUnique();

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.Property(e => e.DefaultOperatorName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.DistributionRegionId)
                    .HasColumnName("DistributionRegionID")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.VanName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasDefaultValueSql("('')");

                entity.HasOne(d => d.DistributionRegion)
                    .WithMany(p => p.Van1s)
                    .HasForeignKey(d => d.DistributionRegionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Van_DistributionRegion");
            });

            modelBuilder.Entity<VanChain>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("VanChain", "Ops");

                entity.Property(e => e.ChainId).HasColumnName("ChainID");

                entity.Property(e => e.VanId).HasColumnName("VanID");
            });

            modelBuilder.Entity<VanRegion>(entity =>
            {
                entity.ToTable("VanRegion", "Distribution");

                entity.Property(e => e.VanRegionId)
                    .HasColumnName("VanRegionID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.VanRegionName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<VanUser>(entity =>
            {
                entity.HasKey(e => new { e.VanId, e.UserId, e.DateEffective });

                entity.ToTable("VanUser", "Distribution");

                entity.Property(e => e.VanId).HasColumnName("VanID");

                entity.Property(e => e.UserId).HasColumnName("UserID");

                entity.Property(e => e.DateEffective)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.VanUsers)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VanUser_User");

                entity.HasOne(d => d.Van)
                    .WithMany(p => p.VanUsers)
                    .HasForeignKey(d => d.VanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VanUser_Van");
            });

            modelBuilder.Entity<VpicknpayContractList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vpicknpay_Contract_list", "Scanner");

                entity.Property(e => e.AcountManager)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.Am)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("AM");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Brand).HasMaxLength(200);

                entity.Property(e => e.ChainName).HasMaxLength(200);

                entity.Property(e => e.ClicksCount).HasColumnName("Clicks_Count");

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaName).HasMaxLength(200);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SparCount).HasColumnName("Spar_Count");

                entity.Property(e => e.StoreCount).HasColumnName("Store_Count");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");

                entity.Property(e => e.TerminationDate).HasColumnType("datetime");

                entity.Property(e => e.TotalWeeks).HasColumnName("Total Weeks");
            });

            modelBuilder.Entity<VpicknpayContractListDetail>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vpicknpay_Contract_list_Detail", "Scanner");

                entity.Property(e => e.AcountManager)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.Am)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("AM");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Brand)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ClicksCount).HasColumnName("Clicks_Count");

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.PcrReadyDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Clicks");

                entity.Property(e => e.PcrReadyDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate_Spar");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SparCount).HasColumnName("Spar_Count");

                entity.Property(e => e.StoreCount).HasColumnName("Store_Count");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitDateClicks)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Clicks");

                entity.Property(e => e.SubmitDateSpar)
                    .HasColumnType("datetime")
                    .HasColumnName("SubmitDate_Spar");

                entity.Property(e => e.SubmitTime)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time");

                entity.Property(e => e.SubmitTimeClicks)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Clicks");

                entity.Property(e => e.SubmitTimeSpar)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time_Spar");

                entity.Property(e => e.TerminationDate).HasColumnType("datetime");

                entity.Property(e => e.TotalWeeks).HasColumnName("Total Weeks");
            });

            modelBuilder.Entity<VpicknpayStoreList>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vpicknpay_store_list", "Scanner");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.NovaAddress)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaCity)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaHeadOffice)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaNumber)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaRegion)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");
            });

            modelBuilder.Entity<VpicknpaysparContractListDetail>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vpicknpayspar_Contract_list_Detail", "Scanner");

                entity.Property(e => e.AcountManager)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.Am)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("AM");

                entity.Property(e => e.Barcodecount).HasColumnName("barcodecount");

                entity.Property(e => e.Brand)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Client)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Contract)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.FirstWeek).HasColumnType("datetime");

                entity.Property(e => e.LastWeek).HasColumnType("datetime");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoreCount).HasColumnName("Store_Count");

                entity.Property(e => e.SubmitDate).HasColumnType("datetime");

                entity.Property(e => e.SubmitTime)
                    .HasMaxLength(15)
                    .HasColumnName("Submit_Time");

                entity.Property(e => e.TerminationDate).HasColumnType("datetime");

                entity.Property(e => e.TotalWeeks).HasColumnName("Total Weeks");
            });

            modelBuilder.Entity<Vproposal>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vproposal", "CRM");

                entity.Property(e => e.AccountManager)
                    .IsRequired()
                    .HasMaxLength(151)
                    .HasColumnName("Account_Manager");

                entity.Property(e => e.CancellationReason)
                    .HasMaxLength(200)
                    .HasColumnName("Cancellation_Reason");

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.ContactDetailId).HasColumnName("ContactDetailID");

                entity.Property(e => e.Country)
                    .IsRequired()
                    .HasMaxLength(30);

                entity.Property(e => e.Date).HasColumnType("datetime");

                entity.Property(e => e.EndDate)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("End_Date");

                entity.Property(e => e.Fyear)
                    .HasMaxLength(10)
                    .HasColumnName("FYear");

                entity.Property(e => e.InternalId)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("InternalID");

                entity.Property(e => e.MediaName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Month)
                    .HasMaxLength(7)
                    .IsUnicode(false);

                entity.Property(e => e.ProposalCreated)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("Proposal_Created");

                entity.Property(e => e.ProposalLikelyness)
                    .HasMaxLength(10)
                    .HasColumnName("Proposal_Likelyness");

                entity.Property(e => e.ProposalStatus)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("Proposal_Status");

                entity.Property(e => e.Quarter).HasMaxLength(10);

                entity.Property(e => e.Rate).HasColumnType("money");

                entity.Property(e => e.StartDate)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("Start_Date");

                entity.Property(e => e.Week)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<VstoreAllocation>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vstore_allocation", "Scanner");

                entity.Property(e => e.Chain).HasMaxLength(150);

                entity.Property(e => e.NovaAddress)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaCity)
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaHeadOffice)
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaId).HasColumnName("Nova_ID");

                entity.Property(e => e.NovaName)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaNumber)
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaRegion)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.PnpRegion)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region");

                entity.Property(e => e.PnpstoreCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");
            });

            modelBuilder.Entity<VstoreAllocationAll>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vstore_allocation_all", "Scanner");

                entity.Property(e => e.Chain).HasMaxLength(150);

                entity.Property(e => e.Chain2).HasMaxLength(150);

                entity.Property(e => e.NovaAddress)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaAddress2)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address2");

                entity.Property(e => e.NovaCity)
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaCity2)
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City2");

                entity.Property(e => e.NovaHeadOffice)
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaId).HasColumnName("Nova_ID");

                entity.Property(e => e.NovaName)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaName2)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name2");

                entity.Property(e => e.NovaNumber)
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaNumber2)
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number2");

                entity.Property(e => e.NovaRegion)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.NovaRegion2)
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region2");

                entity.Property(e => e.PnpRegion)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region");

                entity.Property(e => e.PnpstoreCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreIdLiqour).HasColumnName("StoreID_Liqour");

                entity.Property(e => e.StoreName).HasMaxLength(50);

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreStatus2)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status2");

                entity.Property(e => e.StoreType).HasMaxLength(50);

                entity.Property(e => e.StoreType1)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");

                entity.Property(e => e.StoreType2)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type2");
            });

            modelBuilder.Entity<VstoreAllocationLiquor>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vstore_allocation_liquor", "Scanner");

                entity.Property(e => e.Chain)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.NovaAddress)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Address");

                entity.Property(e => e.NovaCity)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Nova_City");

                entity.Property(e => e.NovaHeadOffice)
                    .IsRequired()
                    .HasMaxLength(150)
                    .HasColumnName("Nova_Head_Office");

                entity.Property(e => e.NovaId).HasColumnName("Nova_ID");

                entity.Property(e => e.NovaName)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Name");

                entity.Property(e => e.NovaNumber)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("Nova_Number");

                entity.Property(e => e.NovaRegion)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("Nova_Region");

                entity.Property(e => e.PnpRegion)
                    .HasMaxLength(50)
                    .HasColumnName("PNP_Region");

                entity.Property(e => e.PnpstoreCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("PNPStoreCode");

                entity.Property(e => e.ScannerStoreId).HasColumnName("ScannerStoreID");

                entity.Property(e => e.StoreStatus)
                    .HasMaxLength(15)
                    .HasColumnName("Store_Status");

                entity.Property(e => e.StoreType)
                    .HasMaxLength(50)
                    .HasColumnName("Store_Type");
            });

            modelBuilder.Entity<Vunallocatedrequest>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vunallocatedrequests", "Scanner");

                entity.Property(e => e.BarcodeCount).HasColumnName("barcodeCount");

                entity.Property(e => e.CorpStoresData).HasColumnName("Corp_stores_Data");

                entity.Property(e => e.FamilyStoresData).HasColumnName("Family_stores_Data");

                entity.Property(e => e.FromDate1).HasColumnType("datetime");

                entity.Property(e => e.FromDate2).HasColumnType("datetime");

                entity.Property(e => e.MaxBarcode).HasMaxLength(30);

                entity.Property(e => e.MinBarcode).HasMaxLength(30);

                entity.Property(e => e.Number)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.PcrReadyDate)
                    .HasColumnType("datetime")
                    .HasColumnName("PCR_ReadyDate");

                entity.Property(e => e.RequestId).HasColumnName("request_id");

                entity.Property(e => e.ToDate1).HasColumnType("datetime");

                entity.Property(e => e.ToDate2).HasColumnType("datetime");
            });

            modelBuilder.Entity<VwFixedPaymentCalenderIteration>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vwFixedPaymentCalenderIteration", "TradeRights");

                entity.Property(e => e.CalendarMonth)
                    .HasMaxLength(9)
                    .IsUnicode(false);

                entity.Property(e => e.PaymentClassificationId).HasColumnName("PaymentClassificationID");
            });

            modelBuilder.Entity<VwInventoryqtycostwhensigned>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_inventoryqtycostwhensigned", "Sales");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.CostWhenSigned)
                    .HasColumnType("money")
                    .HasColumnName("Cost When Signed");

                entity.Property(e => e.ItemQtyId).HasColumnName("ItemQtyID");
            });

            modelBuilder.Entity<VwListofContract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_listof_contracts", "Sales");

                entity.Property(e => e.AccountManagerName)
                    .IsRequired()
                    .HasMaxLength(151);

                entity.Property(e => e.CancelDate).HasColumnType("datetime");

                entity.Property(e => e.CancelledBy).HasMaxLength(50);

                entity.Property(e => e.ClientName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNotes)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.InstallDate).HasColumnType("datetime");

                entity.Property(e => e.ProjectName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.RemoveDate).HasColumnType("datetime");

                entity.Property(e => e.SignDate).HasColumnType("datetime");

                entity.Property(e => e.SignedBy)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SpecialConditions)
                    .IsRequired()
                    .HasMaxLength(2000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<VwListofProductlocatorcontract>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_listof_productlocatorcontracts", "productlocator");

                entity.Property(e => e.BrandName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.CategoryName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.ContractNumber)
                    .IsRequired()
                    .HasMaxLength(8);

                entity.Property(e => e.InstallDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<VwListofProductlocatorstore>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_listof_productlocatorstores", "productlocator");

                entity.Property(e => e.ChainName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.CityName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Consoleid).HasColumnName("consoleid");

                entity.Property(e => e.ContractId).HasColumnName("ContractID");

                entity.Property(e => e.Dateprogrammed)
                    .HasColumnType("datetime")
                    .HasColumnName("dateprogrammed");

                entity.Property(e => e.InstallationTeamName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.InstallationTeamTypeId).HasColumnName("InstallationTeamTypeID");

                entity.Property(e => e.Programmedby)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("programmedby");

                entity.Property(e => e.Programmingconfirmed).HasColumnName("programmingconfirmed");

                entity.Property(e => e.StoreDescription)
                    .IsRequired()
                    .HasMaxLength(607);

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreName)
                    .IsRequired()
                    .HasMaxLength(250);

                entity.Property(e => e.StoreNumber)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.WarehouseName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<VwListofSystemuser>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_listof_systemusers", "security");

                entity.Property(e => e.Dateofexpiration)
                    .HasColumnType("datetime")
                    .HasColumnName("dateofexpiration");

                entity.Property(e => e.Dateoflastsuccessfullogin)
                    .HasColumnType("datetime")
                    .HasColumnName("dateoflastsuccessfullogin");

                entity.Property(e => e.Dateofpasswordcreation)
                    .HasColumnType("datetime")
                    .HasColumnName("dateofpasswordcreation");

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(250)
                    .HasColumnName("email");

                entity.Property(e => e.Enabled).HasColumnName("enabled");

                entity.Property(e => e.Firstname)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("firstname");

                entity.Property(e => e.Fullname)
                    .IsRequired()
                    .HasMaxLength(101)
                    .HasColumnName("fullname");

                entity.Property(e => e.Identity)
                    .HasMaxLength(354)
                    .HasColumnName("identity");

                entity.Property(e => e.Lastname)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lastname");

                entity.Property(e => e.Passwordexpired).HasColumnName("passwordexpired");

                entity.Property(e => e.Userid).HasColumnName("userid");

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("username");
            });

            modelBuilder.Entity<VwPnpstoretype>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_pnpstoretype", "Store");

                entity.Property(e => e.StoreId).HasColumnName("StoreID");

                entity.Property(e => e.StoreType)
                    .IsRequired()
                    .HasMaxLength(9)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<VwRolemember>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_rolemember", "security");

                entity.Property(e => e.Datemodified)
                    .HasColumnType("datetime")
                    .HasColumnName("datemodified");

                entity.Property(e => e.Ismember).HasColumnName("ismember");

                entity.Property(e => e.Roleid).HasColumnName("roleid");

                entity.Property(e => e.Userid).HasColumnName("userid");
            });

            modelBuilder.Entity<VwRoleowner>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_roleowner", "security");

                entity.Property(e => e.Isowner).HasColumnName("isowner");

                entity.Property(e => e.Roleid).HasColumnName("roleid");

                entity.Property(e => e.Userid).HasColumnName("userid");
            });

            modelBuilder.Entity<VwStoreinstallationsperburstperweek>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_storeinstallationsperburstperweek", "reporting");

                entity.Property(e => e.BurstId).HasColumnName("BurstID");

                entity.Property(e => e.Week).HasColumnType("datetime");
            });

            modelBuilder.Entity<VwSystemuserpassword>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_systemuserpassword", "security");

                entity.Property(e => e.Dateofcreation)
                    .HasColumnType("datetime")
                    .HasColumnName("dateofcreation");

                entity.Property(e => e.Dateofexpiration)
                    .HasColumnType("datetime")
                    .HasColumnName("dateofexpiration");

                entity.Property(e => e.Passwordexpired).HasColumnName("passwordexpired");

                entity.Property(e => e.Passwordhash)
                    .IsRequired()
                    .HasMaxLength(88)
                    .HasColumnName("passwordhash")
                    .IsFixedLength(true);

                entity.Property(e => e.Userid).HasColumnName("userid");
            });

            modelBuilder.Entity<VwSystemuserstatus>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_systemuserstatus", "security");

                entity.Property(e => e.Deleted).HasColumnName("deleted");

                entity.Property(e => e.Enabled).HasColumnName("enabled");

                entity.Property(e => e.Userid).HasColumnName("userid");
            });

            modelBuilder.Entity<Warehouse>(entity =>
            {
                entity.ToTable("Warehouse", "Store");

                entity.HasIndex(e => e.WarehouseName, "UniqueWarehouseName")
                    .IsUnique();

                entity.Property(e => e.WarehouseId).HasColumnName("WarehouseID");

                entity.Property(e => e.ContactNumber).HasMaxLength(250);

                entity.Property(e => e.ContactPerson).HasMaxLength(250);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.PhysicalAddressCityId).HasColumnName("PhysicalAddressCityID");

                entity.Property(e => e.PhysicalAddressLine1).HasMaxLength(250);

                entity.Property(e => e.PhysicalAddressLine2).HasMaxLength(250);

                entity.Property(e => e.WarehouseName)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<Wholesaler>(entity =>
            {
                entity.ToTable("Wholesaler", "Distribution");

                entity.Property(e => e.WholesalerId)
                    .HasColumnName("WholesalerID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.DateCreated)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateModified)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DateRequested).HasColumnType("datetime");

                entity.Property(e => e.WholesalerName)
                    .IsRequired()
                    .HasMaxLength(250);
            });


            modelBuilder.Entity<InstallationScheduleDates>(entity =>
            {
                entity.ToTable("InstallationScheduleDates", "Ops");

               
                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

               
            });

            modelBuilder.Entity<InventoryStockTake>(entity =>
            {
                entity.ToTable("InventoryStockTake", "Ops");


                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");


            });


           

            modelBuilder.Entity<InventoryStockTakeRole>(entity =>
            {
                entity.ToTable("InventoryStockTakeRole", "Ops");


                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Dormant)
                   .IsRequired()
                   .HasDefaultValueSql("((0))");
            });


            modelBuilder.Entity<InventoryStockTakeDetails>(entity =>
            {
                entity.ToTable("InventoryStockTakeDetails", "Ops");

                entity.Property(e => e.CreationDate)
                   .HasColumnType("datetime")
                   .HasDefaultValueSql("(getdate())");

            });

            modelBuilder.Entity<InventoryStockTakeDetailsBarcodes>(entity =>
            {
                entity.ToTable("InventoryStockTakeDetailsBarcodes", "Ops");

                 entity.Property(e => e.isNonExisting)
                  .IsRequired()
                  .HasDefaultValueSql("((0))");

                entity.Property(e => e.isWrongCapex)
                  .IsRequired()
                  .HasDefaultValueSql("((0))");


                entity.HasOne(d => d.InventoryStockTakeDetails)
                  .WithMany(p => p.InventoryStockTakeDetailsBarcodes)
                  .HasForeignKey(d => d.InventoryStockTakeDetailId)
                  .HasConstraintName("FK_InventoryStockTakeDetailsBarcodes_InventoryStockTakeDetail");

            });

            modelBuilder.Entity<InventoryItemTransactionType>(entity =>
            {
                entity.ToTable("InventoryItemTransactionType", "Ops");


                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasDefaultValueSql("(suser_sname())");

                entity.Property(e => e.CreationDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");


            });

            modelBuilder.Entity<MediaMonitor>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .HasName("PK_Ops.MediaMonitor");
                entity.ToTable("MediaMonitor", "Ops");
            });

            modelBuilder.Entity<IncomingMediaPerRegion>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .HasName("PK_Ops.IncomingMediaPerRegion");
                entity.ToTable("IncomingMediaPerRegion", "Ops");
            });

            modelBuilder.Entity<IncomingMediaPerRegionRevised>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .HasName("PK_Ops.IncomingMediaPerRegionRevised");
                entity.ToTable("IncomingMediaPerRegionRevised", "Ops");
            });

            modelBuilder.Entity<PhoenixAPI.Models.IncomingMedia.IncomingMedia>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .HasName("PK_Ops.IncomingMedia");
                entity.ToTable("IncomingMedia", "Ops");
            });

            modelBuilder.Entity<OpsMediaMonitorRegion>(entity =>
            {
                entity.HasKey(e => e.RegionId)
                    .HasName("PK_Ops.MediaMonitorRegions");
                entity.ToTable("MediaMonitorRegions", "Ops");
            });

            modelBuilder.Entity<WarehouseManager>(entity =>
            {
                entity.HasKey(e => new { e.WarehouseManagerId });

                entity.ToTable("WarehouseManager", "Ops");

              

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.WarehouseManagers)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_WarehouseManager_Warehouse");

               
            });

            modelBuilder.Entity<InstallationCounts>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<PhoenixAPI.Models.Demonstrations.Demonstration>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<PhoenixAPI.Models.Demonstrations.StoreResult>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<PhoenixAPI.Models.Demonstrations.StoreListResult>(entity =>
            {
                entity.HasNoKey();
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
