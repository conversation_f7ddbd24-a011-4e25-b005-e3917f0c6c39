﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BurstCategory
    {
        public Guid BurstId { get; set; }
        public int CategoryId { get; set; }
        public short Priority { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Burst Burst { get; set; }
        public virtual Category1 Category { get; set; }
    }
}
