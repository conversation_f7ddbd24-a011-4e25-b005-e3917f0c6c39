﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ClicksScannerDataToolAllRawDatum
    {
        public int Id { get; set; }
        public string Barcode { get; set; }
        public string ArticleNo { get; set; }
        public string Description { get; set; }
        public string Country { get; set; }
        public string Province { get; set; }
        public string StoreCode { get; set; }
        public string StoreName { get; set; }
        public string Brand { get; set; }
        public string BrandType { get; set; }
        public string BrandDescription { get; set; }
        public int? PackSize { get; set; }
        public string Department { get; set; }
        public string Category { get; set; }
        public string SubCategory { get; set; }
        public decimal? Sales { get; set; }
        public double? Units { get; set; }
        public DateTime? WeekendDate { get; set; }
        public int? PacksInCase { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public int? Segment { get; set; }
        public string PriceRegion { get; set; }
        public string FileName { get; set; }
    }
}
