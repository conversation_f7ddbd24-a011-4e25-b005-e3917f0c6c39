﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MasterItemGroups
    {
        public MasterItemGroups()
        {
            MasterItemGroupMembers = new HashSet<MasterItemGroupMember>();
            MediaCapexOpices = new HashSet<MediaCapexOpex>();
        }
        [Key]
        public int MasterItemGroupId { get; set; }
        public string MasterItemGroupName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<MasterItemGroupMember> MasterItemGroupMembers { get; set; }
        public virtual ICollection<MediaCapexOpex> MediaCapexOpices { get; set; }
    }
}
