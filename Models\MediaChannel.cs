﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaChannel
    {
        public MediaChannel()
        {
            MediaChannelGroupMembers = new HashSet<MediaChannelGroupMember>();
        }

        public int MediaChannelId { get; set; }
        public string MediaChannelName { get; set; }
        public bool Dormant { get; set; }

        public virtual ICollection<MediaChannelGroupMember> MediaChannelGroupMembers { get; set; }
    }
}
