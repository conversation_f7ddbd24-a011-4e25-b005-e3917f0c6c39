﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddIsSignedOffToStockTakeDetailsSignature : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "signedOffBy",
                schema: "Ops",
                table: "InventoryStockTakeDetails",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "signedOffBy",
                schema: "Ops",
                table: "InventoryStockTakeDetails");
        }
    }
}
