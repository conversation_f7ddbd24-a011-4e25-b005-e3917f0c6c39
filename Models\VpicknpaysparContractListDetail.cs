﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VpicknpaysparContractListDetail
    {
        public string ChainName { get; set; }
        public string AcountManager { get; set; }
        public string Contract { get; set; }
        public string Am { get; set; }
        public string Client { get; set; }
        public int? TotalWeeks { get; set; }
        public DateTime FirstWeek { get; set; }
        public DateTime LastWeek { get; set; }
        public DateTime? TerminationDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string SignedBy { get; set; }
        public DateTime? SignDate { get; set; }
        public string MediaName { get; set; }
        public int? StoreCount { get; set; }
        public string Brand { get; set; }
        public DateTime? PcrReadyDate { get; set; }
        public int? Barcodecount { get; set; }
        public DateTime? SubmitDate { get; set; }
        public string SubmitTime { get; set; }
        public bool? Selected { get; set; }
        public bool Signed { get; set; }
        public bool Cancelled { get; set; }
    }
}
