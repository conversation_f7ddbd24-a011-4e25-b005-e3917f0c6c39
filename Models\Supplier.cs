﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Supplier
    {
        public Supplier()
        {
            SupplierInventories = new HashSet<SupplierInventory>();
        }

        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string SupplierEmailAddress { get; set; }
        public string SupplierWebsite { get; set; }
        public string SupplierVatNo { get; set; }
        public string SupplierRegistrationNumber { get; set; }
        public bool SupplierVatRegistered { get; set; }
        public string SupplierTelNo { get; set; }
        public string SupplierCellNo { get; set; }
        public string SupplierAltNo { get; set; }
        public string SupplierFaxNo { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<SupplierInventory> SupplierInventories { get; set; }
    }
}
