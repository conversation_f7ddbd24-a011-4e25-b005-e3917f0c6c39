﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BillingScheduleGroupType
    {
        public BillingScheduleGroupType()
        {
            BillingScheduleGroups = new HashSet<BillingScheduleGroup>();
        }

        public int GroupTypeId { get; set; }
        public string GroupTypeName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }

        public virtual ICollection<BillingScheduleGroup> BillingScheduleGroups { get; set; }
    }
}
