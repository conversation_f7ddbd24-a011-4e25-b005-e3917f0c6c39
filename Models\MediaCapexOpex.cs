﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaCapexOpex
    {
        public int MediaCapexOpexId { get; set; }
        public int AmountRequired { get; set; }
        public int MediaId { get; set; }
        public int? MasterItemId { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public int? MasterItemGroupId { get; set; }

        public bool isOptional { get; set; }

        public virtual MasterItem MasterItem { get; set; }
        public virtual MasterItemGroups MasterItemGroup { get; set; }
        public virtual Media Media { get; set; }
    }
}
