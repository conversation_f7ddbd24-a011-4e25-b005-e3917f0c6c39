﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VMyMobilityExport
    {
        public string Group { get; set; }
        public string DayOfCommencementDate { get; set; }
        public int? DayOfLastModified { get; set; }
        public string DayOfTerminationDate { get; set; }
        public int? QuarterOfCommencementDate { get; set; }
        public int? MonthOfCommencementDate { get; set; }
        public int? WeekOfCommencementDate { get; set; }
        public int? QuarterOfLastModified { get; set; }
        public int? MonthOfLastModified { get; set; }
        public int? WeekOfLastModified { get; set; }
        public int? QuarterOfTerminationDate { get; set; }
        public int? MonthOfTerminationDate { get; set; }
        public int? WeekOfTerminationDate { get; set; }
        public int? YearOfCommencementDate { get; set; }
        public int? YearOfLastModified { get; set; }
        public int? YearOfTerminationDate { get; set; }
        public string JobNumber { get; set; }
        public string SpecialInstructions { get; set; }
        public int? Cycle { get; set; }
        public int? NumberOfWeeks { get; set; }
        public int? CommencementDate { get; set; }
        public int? TerminationDate { get; set; }
        public string Store { get; set; }
        public string Region { get; set; }
        public string Product { get; set; }
        public string MediaType { get; set; }
        public string Client { get; set; }
        public string CategoryName { get; set; }
        public string LastModified { get; set; }
        public string Chain { get; set; }
        public int? Quantity { get; set; }
        public int? LastModifiedBy { get; set; }
        public string Status { get; set; }
        public int? StoreAndChain { get; set; }
        public string CreatedBy { get; set; }
        public int? Section { get; set; }
        public string ForDate { get; set; }
        public int StoId { get; set; }
        public int? Teamlist { get; set; }
        public int QtyToInstall { get; set; }
        public Guid BurstId { get; set; }
    }
}
