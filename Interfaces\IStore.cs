﻿using PhoenixAPI.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Interfaces
{
    public interface IStore
    {
        Store GetStore(int StoreId);
        IEnumerable<Store> GetStores();
        Store Add(Store store);
        Store Update(Store store);
        Store Delete(Store store);

        IEnumerable<Store> GetTeamStores(int TeamId);
    }
}
