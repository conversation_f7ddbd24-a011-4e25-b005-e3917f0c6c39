﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class IRCodes
    {
        public IRCodes()
        {
            //InstallationScheduleCurrents = new HashSet<InstallationScheduleCurrent>();
        }

        [Key]
        [Column("IRCodeID")]
        public int IRCodeID { get; set; }
        [Required]
        [StringLength(250)]
        public string IRCodeName { get; set; }
        public bool Dormant { get; set; }

        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreationDate { get; set; }

        public bool hasComment { get; set; }

        public bool countsForStrikeRate { get; set; }

        [StringLength(255)]
        public string DefaultComment { get; set; }

        public bool forceCapexScan { get; set; }

        public int IrCodeSortOrder { get; set; }

        public bool needsPicture { get; set; }

        public bool isStoreSkip { get; set; }

        //public virtual ICollection<InstallationScheduleCurrent> InstallationScheduleCurrents { get; set; }
    }
}
