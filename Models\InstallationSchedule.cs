﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationSchedule
    {
        public DateTime Week { get; set; }
        public Guid BurstId { get; set; }
        public int StoreId { get; set; }
        public int CategoryId { get; set; }
        public bool Homesite { get; set; }
        public string InstallationInstructions { get; set; }
        public string Action { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Burst Burst { get; set; }
        public virtual Category1 Category { get; set; }
        public virtual Store Store { get; set; }
    }
}
