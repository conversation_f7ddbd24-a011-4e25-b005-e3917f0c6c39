﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Rolemember
    {
        public Guid Userid { get; set; }
        public Guid Roleid { get; set; }
        public DateTime Datemodified { get; set; }
        public bool? Ismember { get; set; }
        public Guid Modifiedbyuserid { get; set; }

        public virtual Systemuser Modifiedbyuser { get; set; }
        public virtual Role Role { get; set; }
        public virtual Systemuser User { get; set; }
    }
}
