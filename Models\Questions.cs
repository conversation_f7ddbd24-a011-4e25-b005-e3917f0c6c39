﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Questions
    {

        [Key]
        [Column("QuestionId")]
        public int QuestionId { get; set; }

        public string Quetion { get; set; }
        public DateTime DateCreated { get; set; }
       
        public string CreatedBy { get; set; }
        public bool Dormant { get; set; }

        public int? MediaId { get; set; }

        //[ForeignKey(nameof(MediaId))]
        ///public virtual Media Media { get; set; }

        public virtual ICollection<QuestionsAndAnswers> QuestionsAndAnswers { get; set; }
    }
}
