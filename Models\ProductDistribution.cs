﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ProductDistribution
    {
        public DateTime CheckDate { get; set; }
        public int DistributionRegionId { get; set; }
        public string DistributionRegionName { get; set; }
        public int VanId { get; set; }
        public string VanName { get; set; }
        public Guid BrandId { get; set; }
        public string BrandName { get; set; }
        public Guid ProductId { get; set; }
        public string ProductName { get; set; }
        public Guid PackSizeId { get; set; }
        public string PackSizeName { get; set; }
        public int StoreId { get; set; }
        public string StoreNumber { get; set; }
        public string StoreName { get; set; }
        public int? InStock { get; set; }
    }
}
