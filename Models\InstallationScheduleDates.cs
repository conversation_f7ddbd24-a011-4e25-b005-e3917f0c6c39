﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationScheduleDates
    {

        [Key]
        [Column("InstallationScheduleDateId")]
        public int InstallationScheduleDateId { get; set; }
        public DateTime ScheduleDate { get; set; }
        public DateTime CreationDate { get; set; }
        public string CreatedBy { get; set; }

        public bool isCurrent { get; set; }

        public bool isLatest { get; set; }


    }
}
