﻿using PhoenixAPI.Models.Demonstrations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Services.Interfaces
{
    public interface IDemonstrationsService
    {
        Task<object> CheckIfBurstExist(DemonstrationRequest request);
        Task<object> CheckIfStoreListIsConfirmed(DemonstrationRequest request);
        Task<object> InteractionNotification(DemonstrationRequest request, string user);
        Task<object> GetChains();
        Task<object> CheckIfContractExist(string contractNumber);
    }
}
