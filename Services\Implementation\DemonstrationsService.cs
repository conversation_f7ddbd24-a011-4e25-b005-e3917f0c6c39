﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Models;
using PhoenixAPI.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using TheArtOfDev.HtmlRenderer.Core.Entities;
using PdfSharp.Drawing;
using System.Net;
using System.Drawing;
using System.Reflection;
using ClosedXML.Excel;
using BurstNotification = PhoenixAPI.Models.Demonstrations.BurstNotification;
using PhoenixAPI.Models.Demonstrations;
using System.Linq.Expressions;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Configurations;
using Microsoft.Extensions.Options;
using System.Net.Mail;
using System.Net.Mime;
using System.Globalization;

namespace PhoenixAPI.Services.Implementation
{
    public class DemonstrationsService : IDemonstrationsService
    {
        private readonly Configuration _emailConfiguration;
        private readonly ConnectionConfiguration _configuration;
        private readonly NovaDBContext _context;
        private static readonly Attachment footer = new Attachment(@"\\************\API Resources\Resources\Images\instore.jpg");       
        private static readonly string SummaryTemplatePDF = @"\\************\API Resources\Resources\Templates\SummaryPDFTemplate.html";
        private static List<BurstNotification> lstBurstNotifications = new List<BurstNotification>();
        private static List<ContractIssueNotification> lstContractIssueNotification = new List<ContractIssueNotification>();
        private static List<BurstNotification> lstBurstNotificationsSent = new List<BurstNotification>();
        private static List<BurstNotification> lstBurstNotificationCancelled = new List<BurstNotification>();
        private static string MailText;
        private static bool isInDev = false;
        private static string contentID;
        public DemonstrationsService(NovaDBContext context, IOptions<ConnectionConfiguration> configuration, IOptions<Configuration> emailConfiguration)
        {
            _context = context;
            _emailConfiguration = emailConfiguration.Value;
            _configuration = configuration.Value;
            contentID = "Image";
            footer.ContentId = contentID;
            footer.ContentDisposition.Inline = true;
            footer.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
        }
        public async Task<object> InteractionNotification(DemonstrationRequest request, string user)
        {
            var chekIfContractExist = await _context.Contract.Where(x => x.ContractNumber == request.contractNumber).FirstOrDefaultAsync();
            if (chekIfContractExist == null)
            {
                return new JsonResult(new { error = "The contract number provided does not exist." });
            }

            var sqlQuery = "declare	@return_value int exec @return_value = ops.InteractionsNotificationScriptManual @ContractNumber = " + chekIfContractExist.ContractNumber + " select 'ReturnValue' = @return_value";
            _context.Demonstrations.FromSqlRaw<PhoenixAPI.Models.Demonstrations.Demonstration>(sqlQuery).AsEnumerable().FirstOrDefault();

            UpdateBurstNotificationStores(request, user);
            GetBurstNotifications(request.contractNumber);
            ProcessBurstNotifications(request, user);
            ProcessNotificationsResponse();

            return chekIfContractExist;
        }
        public async Task<object> GetChains()
        {
            return await _context.Chain.Where(x => x.Dormant == false).ToListAsync();
        }
        public async Task<object> CheckIfContractExist(string contractNumber)
        {
            var checkIfContractExist = await _context.Contract.Where(x => x.ContractNumber == contractNumber).FirstOrDefaultAsync();

            if (checkIfContractExist == null)
            {
                return new JsonResult(new { error = "The contract number provided does not exist." });
            }

            return checkIfContractExist;
        }

        public async Task<object> CheckIfStoreListIsConfirmed(DemonstrationRequest request)
        {
            var checkIfContractExist = await _context.Contract.Where(x => x.ContractNumber == request.contractNumber).FirstOrDefaultAsync();

            if (checkIfContractExist == null)
            {
                return new JsonResult(new { error = "The contract number provided does not exist." });
            }

            var checkBurst = _context.Bursts.Where(s => s.ContractID == checkIfContractExist.ContractId).ToList();
            var burst = checkBurst.Where(s => ConvertDateToString(s.FirstWeek) == request.firstWeek).FirstOrDefault();

            if (burst == null)
            {
                return new JsonResult(new { error = "The burst provided does not exist." });
            }

            return burst.StoreListConfirmed == true ? new JsonResult(new { isConfirmed = true }) : new JsonResult(new { isConfirmed = false });
        }

        public async Task<object> CheckIfBurstExist(DemonstrationRequest request)
        {
            var checkIfContractExist = await _context.Contract.Where(x => x.ContractNumber == request.contractNumber).FirstOrDefaultAsync();

            if (checkIfContractExist == null)
            {
                return new JsonResult(new { error = "The contract number provided does not exist." });
            }

            var checkBurst = _context.Bursts.Where(s => s.ContractID == checkIfContractExist.ContractId).ToList();
            var bursts = checkBurst.Where(s => ConvertDateToString(s.FirstWeek) == request.firstWeek).ToList();

            if (bursts.Count == 0)
            {
                return new JsonResult(new { error = "The burst provided does not exist." });
            }

            return bursts;
        }
        private void UpdateBurstNotificationStores(DemonstrationRequest request, string user)
        {         
            var chekIfContractExist = _context.Contract.Where(x => x.ContractNumber == request.contractNumber).FirstOrDefault();
            var checkBurst = _context.Bursts.Where(s => s.ContractID == chekIfContractExist.ContractId).ToList();
            var bursts = checkBurst.Where(s => ConvertDateToString(s.FirstWeek) == request.firstWeek && s.StoreListConfirmed == true).ToList();

            foreach(var burst in bursts)
            {
                var results = _context.StoreListResults.FromSqlRaw($"SELECT DISTINCT Sales.StoreList.BurstID, Sales.StoreList.StoreID, Store.Store.StoreNumber + N' ' + Sales.Burst.ChainName + N' ' + Store.Store.StoreName AS StoreDescription, CONVERT(NVARCHAR, CONCAT(CONVERT(DATE, Sales.Burst.FirstWeek), ' ',CONVERT(NVARCHAR(8), CONVERT(TIME, Sales.Burst.FirstWeek)))) AS FirstWeek, Sales.Burst.ChainName FROM Sales.StoreList INNER JOIN Sales.Burst ON Sales.StoreList.BurstID = Sales.Burst.BurstID INNER JOIN Store.Store ON Sales.StoreList.StoreID = Store.Store.StoreID INNER JOIN Ops.BurstNotificationStores ON Sales.Burst.BurstID = Ops.BurstNotificationStores.BurstId WHERE Sales.Burst.ContractID = '" + chekIfContractExist.ContractId + "' and FirstWeek = '" + ConvertStringToDatetime(request.firstWeek) + "'").ToList();
                var burstNotificationStores = _context.BurstNotificationStores.Where(s => s.BurstId == burst.BurstId).ToList();
                var stores = burstNotificationStores.Where(s => results.Any(x => x.BurstID == s.BurstId && x.StoreID == s.StoreId)).ToList();
                var updatedStores = burstNotificationStores.Where(s => !results.Any(x => x.BurstID == s.BurstId && x.StoreID == s.StoreId)).ToList();

                foreach (var store in stores)
                {
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(_configuration.NovaDB))
                        {
                            var query = "update Ops.BurstNotificationStores set hasBeenRemoved = 0 where BurstID = '" + store.BurstId + "' and StoreId = '" + store.StoreId + "'";
                            SqlCommand sqlCmd = new SqlCommand(query, conn);
                            conn.Open();
                            SqlDataAdapter da = new SqlDataAdapter(sqlCmd);
                            sqlCmd.ExecuteNonQuery();
                            da.Dispose();
                            conn.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        string error = ex.Message;
                    }
                }

                foreach (var store in updatedStores)
                {
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(_configuration.NovaDB))
                        {
                            var query = "update Ops.BurstNotificationStores set hasBeenRemoved = 1 where BurstID = '" + store.BurstId + "' and StoreId = '" + store.StoreId + "'";
                            SqlCommand sqlCmd = new SqlCommand(query, conn);
                            conn.Open();
                            SqlDataAdapter da = new SqlDataAdapter(sqlCmd);
                            sqlCmd.ExecuteNonQuery();
                            da.Dispose();
                            conn.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        string error = ex.Message;
                    }
                }
            }         
        }
        private void GetBurstNotifications(string contractNumber)
        {
            using (SqlConnection conn = new SqlConnection(_configuration.NovaDB))
            {
                string sql = "select top 500  bns.*, s.StoreManagerEmailAddress as EmailAddress,s.storename " +
                            " , s.StoreManagerName as storeManagerName,s.SecondaryEmailAddress as secondaryEmailAddres " +
                            " , m.mediaName,b.FirstWeek,b.InstallationInstructions, b.brandname, c.projectName as ProjectName, s.StoreNumber, [dbo].[udfGetInstallationDaysForBurst](b.burstid) as InstallationDays, [dbo].[udfCategoryListByBurst](b.burstid) as CategoryList, am.Email as Email, c.contractNumber as ContractNumber, c.demoOwner" +
                            " ,bn.FirstNotificationDateOfBurst, bn.SecondNotificationDateOfBurst " +
                            " ,case when bn.FirstNotificationDateOfBurst = (DATEADD(week, DATEDIFF(day, 0, getdate())/7, 0)) then 1 when bn.SecondNotificationDateOfBurst = (DATEADD(week, DATEDIFF(day, 0, getdate())/7, 0)) then 2  else 0 end as NotificationNumber " +
                            " from Ops.BurstNotificationStores BNS " +
                            "  inner " +
                            " join Store.Store s on s.storeid = bns.storeId " +
                            " inner " +
                            " join Sales.burst b on b.BurstID = bns.BurstId " +
                            " inner  " +
                            " join Sales.Contract c on c.ContractId = b.ContractId " +
                            " inner  " +
                            " join Sales.AccountManager am on c.AccountManagerID = am.AccountManagerID " +
                            " inner  " +
                            " join Media.Media m on m.MediaID = b.MediaID " +
                            " inner " +
                            " join Ops.BurstNotifications bn on bn.BurstId = bns.BurstId and bn.ContractId = bns.ContractId " +
                            " where (case when bn.FirstNotificationDateOfBurst = (DATEADD(week, DATEDIFF(day, 0, getdate())/7, 0)) then 1 end != isnull(bns.FirstNotificationSent,0) " +
                            " or case when bn.SecondNotificationDateOfBurst = (DATEADD(week, DATEDIFF(day, 0, getdate())/7, 0)) then 1 end != isnull(bns.secondNotificationSent,0) or c.contractnumber = '" + contractNumber + "' ) and hasbeenremoved != 1" +
                            " and isnull(hasbeenremoved,0)= 0 " +
                             " order by StoreId,BurstId";
                SqlCommand sqlCmd = new SqlCommand(sql, conn);
                conn.Open();
                DataTable dt = new DataTable();
                SqlDataAdapter da = new SqlDataAdapter(sqlCmd);
                da.Fill(dt);
                lstBurstNotifications = ConvertDataTable<BurstNotification>(dt);
                da.Dispose();
                conn.Close();
            }
        }
        private void ProcessBurstNotifications(DemonstrationRequest request, string user)
        {
            List<BurstNotificationBrandWeek> lstBrandWeeks = new List<BurstNotificationBrandWeek>();
            List<BurstNotification> stores = new List<BurstNotification>();
            lstBrandWeeks = lstBurstNotifications.GroupBy(x => new { x.BrandName, x.FirstWeek }).Select(y => new BurstNotificationBrandWeek() { FirstWeek = y.Key.FirstWeek, BrandName = y.Key.BrandName }).OrderBy(z => z.BrandName).ToList();
            var chekIfContractExist = _context.Contract.Where(x => x.ContractNumber == request.contractNumber).FirstOrDefault();
            var checkBurst = _context.Bursts.Where(s => s.ContractID == chekIfContractExist.ContractId).ToList();
            var bursts = checkBurst.Where(s => ConvertDateToString(s.FirstWeek) == request.firstWeek).ToList();
            stores = lstBurstNotifications.Where(s => bursts.Any(x => x.BurstId.ToString() == s.BurstId && x.BrandName == s.BrandName && x.FirstWeek.ToString("dd/MM/yyyy") == request.firstWeek)).ToList();
            GenerateBrandWeekPDF(stores, user);
        }
        private void GenerateBrandWeekPDF(List<BurstNotification> lstBrandStores, string user)
        {
            foreach (BurstNotification bn in lstBrandStores)
            {
                bn.StoreName = bn.StoreName.Replace("NGCORP", "Super").Replace("CORP", "Super");
                bn.StoreNumber = bn.StoreNumber.Replace("-NG1", "").Replace("-NG", "").Replace("-1", "");
            }
            lstBrandStores = lstBrandStores.OrderBy(x => x.StoreName).ToList();
            StreamReader strSummary = new StreamReader(SummaryTemplatePDF);
            MailText = strSummary.ReadToEnd();
            strSummary.Close();

            string burstTable = "";
            foreach (BurstNotification burstNotification in lstBrandStores)
            {
                string daysAdded = "";
                if (burstNotification.InstallationDays != "")
                {
                    daysAdded = burstNotification.InstallationDays;
                    daysAdded = daysAdded.Replace("Monday", "Mon").Replace("Tuesday", "Tue").Replace("Wednesday", "Wed").Replace("Thursday", "Thu").Replace("Friday", "Fri").Replace("Saturday", "Sat").Replace("Sunday", "Sun");
                }
                burstTable += "<tr style=\"page -break-inside: avoid; \"><td style=\"page -break-inside: avoid;padding-right:10px;border: 1px solid black; \"><strong>" + burstNotification.StoreName + " </strong></td><td style=\"page -break-inside: avoid;border: 1px solid black; \"> " + burstNotification.StoreNumber + "</td><td style=\"page -break-inside: avoid;border: 1px solid black; \"> " + daysAdded + " </td><td style=\"page -break-inside: avoid;border: 1px solid black; \"> " + burstNotification.CategoryList + " </td></tr>";
                lstBurstNotificationsSent.Add(burstNotification);
            }

            string mailTextForSummaryPdf = "<p>This letter serves as formal confirmation that brand ambassadors from " + lstBrandStores[0].demoOwner + " will be conducting product demonstrations in your store and that these have been approved by PNP Head Office. Demonstration requirements include sufficient stock of the promotional product/s as well as access for brand ambassadors to set up the demonstration equipment in the category/s mentioned below. The brand ambassadors will provide all the necessary demonstration equipment and sampling kits. Details are as follows: </p>";
            mailTextForSummaryPdf += "</br></br>";
            mailTextForSummaryPdf += "<p style=\"line-height: 1.0;\">BRAND: " + lstBrandStores[0].BrandName + "<br/>";
            mailTextForSummaryPdf += "WEEK: " + lstBrandStores[0].FirstWeek.ToString("dd MMMM yyyy") + " to " + lstBrandStores[0].FirstWeek.AddDays(7).ToString("dd MMMM yyyy") + "</p><br/><br/>";
            MailText = MailText.Replace("[[FullText]]", mailTextForSummaryPdf);
            MailText = MailText.Replace("[[DateText]]", System.DateTime.Now.ToString("dd MMMM yyyy"));
            MailText = MailText.Replace("[[SelectedStores]]", burstTable);
            MailText = MailText.Replace("[[footerImage]]", "<img src=\"cid:" + contentID + "\">");

            string path = @"\\************\API Resources\Resources\PNP Interactions Files\Demonstrations-" + lstBrandStores[0].FirstWeek.ToString("dd MMMM") + " for " + lstBrandStores[0].BrandName.Replace(":", "") + ".pdf";
            string name = "Demonstrations - " + lstBrandStores[0].FirstWeek.ToString("dd MMMM") + " for " + lstBrandStores[0].BrandName.Replace(":", "") + ".pdf";
  
              byte[] bytes = PdfSharpConvert(MailText);

            File.WriteAllBytes(path, bytes);
            SendMailAsync(user, bytes, name).ConfigureAwait(false);
        }
        private void ProcessNotificationsResponse()
        {
            foreach (BurstNotification burstNotification in lstBurstNotificationsSent)
            {
                string sql = "";
                if (burstNotification.NotificationNumber == 1)
                {
                    sql = "update Ops.BurstNotificationStores set firstNotificationSent = 1, firstNotificationSentDate = getdate(), firstNotificationSentDetails = '', isNewlyAdded = 0 "
                        + "where burstid = '" + burstNotification.BurstId + "' and contractid = '" + burstNotification.ContractId + "' and storeid = '" + burstNotification.StoreId + "'";
                }
                else if (burstNotification.NotificationNumber == 2)
                {
                    sql = "update Ops.BurstNotificationStores set secondNotificationSent = 1, secondNotificationSentDate = getdate(), secondNotificationSentDetails = ''"
                       + "where burstid = '" + burstNotification.BurstId + "' and contractid = '" + burstNotification.ContractId + "' and storeid = '" + burstNotification.StoreId + "'";
                }
                else
                {
                    sql = "update Ops.BurstNotificationStores set firstNotificationSent = 1, firstNotificationSentDate = getdate(), firstNotificationSentDetails = '', isNewlyAdded = 0 "
                        + "where burstid = '" + burstNotification.BurstId + "' and contractid = '" + burstNotification.ContractId + "' and storeid = '" + burstNotification.StoreId + "'";
                }

                try
                {
                    using (SqlConnection conn = new SqlConnection(_configuration.NovaDB))
                    {
                        SqlCommand sqlCmd = new SqlCommand(sql, conn);
                        conn.Open();
                        SqlDataAdapter da = new SqlDataAdapter(sqlCmd);
                        sqlCmd.ExecuteNonQuery();
                        da.Dispose();
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    string error = ex.Message;
                }
            }

            foreach (BurstNotification burstNotification in lstBurstNotificationCancelled)
            {
                var sql = "update Ops.BurstNotificationStores set cancellationNoticeSent = 1 "
                       + "where burstid = '" + burstNotification.BurstId + "' and contractid = '" + burstNotification.ContractId + "' and storeid = '" + burstNotification.StoreId + "'";

                try
                {
                    using (SqlConnection conn = new SqlConnection(_configuration.NovaDB))
                    {
                        SqlCommand sqlCmd = new SqlCommand(sql, conn);
                        conn.Open();
                        SqlDataAdapter da = new SqlDataAdapter(sqlCmd);
                        sqlCmd.ExecuteNonQuery();
                        da.Dispose();
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    string error = ex.Message;
                }
            }
        }
        private List<T> ConvertDataTable<T>(DataTable dt)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dt.Rows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }
        private T GetItem<T>(DataRow dr)
        {
            Type temp = typeof(T);
            T obj = Activator.CreateInstance<T>();

            foreach (DataColumn column in dr.Table.Columns)
            {
                foreach (PropertyInfo pro in temp.GetProperties())
                {
                    if (pro.Name.ToLower() == column.ColumnName.ToLower())
                        if ((column.DataType.Name == "Guid"))
                        {
                            pro.SetValue(obj, dr[column.ColumnName].ToString(), null);
                        }
                        else
                        {
                            if (dr[column.ColumnName].ToString() == "")
                            {
                                pro.SetValue(obj, dr[column.ColumnName].ToString(), null);
                            }
                            else
                            {
                                pro.SetValue(obj, dr[column.ColumnName], null);
                            }
                        }
                    else
                        continue;
                }
            }
            return obj;
        }
        public Byte[] PdfSharpConvert(String html)
        {
            TheArtOfDev.HtmlRenderer.PdfSharp.PdfGenerateConfig pdfGenerateConfig = new TheArtOfDev.HtmlRenderer.PdfSharp.PdfGenerateConfig();

            pdfGenerateConfig.PageSize = PdfSharp.PageSize.Letter;
            pdfGenerateConfig.MarginLeft = 10;
            pdfGenerateConfig.MarginRight = 10;
            pdfGenerateConfig.SetMargins(20);
            pdfGenerateConfig.MarginBottom = 75;
            Byte[] res = null;
            using (MemoryStream ms = new MemoryStream())
            {
                var pdf = TheArtOfDev.HtmlRenderer.PdfSharp.PdfGenerator.GeneratePdf(html, config: pdfGenerateConfig, imageLoad: OnImageLoad);
                pdf.Save(ms);
                res = ms.ToArray();
            }
            return res;
        }
        private void OnImageLoad(object sender, HtmlImageLoadEventArgs e)
        {
            using (var client = new WebClient())
            {
                var url = e.Src;
                if (!e.Src.StartsWith("http://") && !e.Src.StartsWith("https://"))
                {
                    //url = Properties.Settings.Default.BaseUrl.TrimEnd('/') + e.Src;
                }
                using (var stream = new MemoryStream(client.DownloadData(url)))
                {
                    e.Callback(XImage.FromStream(stream));
                }
            }
        }
        private void generateExelFileFromColumn<T>(IEnumerable<T> data, string filePathToCreate, string workBookName)
        {
            var props = typeof(T).GetProperties();
            var sb = Expression.Parameter(typeof(StringBuilder));
            var obj = Expression.Parameter(typeof(T));
            Expression body = sb;
            var workbook = new XLWorkbook();
            workbook.AddWorksheet(workBookName);
            var ws = workbook.Worksheet(workBookName);
            int row = 1;
            int columnNumber = 1;
            string rowString = row.ToString();
            foreach (var prop in props)
            {

                string columnInExcel = Number2String(columnNumber, true);
                ws.Cell(columnInExcel + rowString).Value = prop.Name;
                columnNumber += 1;
            }

            foreach (T rowData in data)
            {
                columnNumber = 1;

                foreach (var prop in props)
                {
                    //string value = rowData.GetType().GetProperty(prop.Name).GetValue(rowData, null);
                    string columnInExcel = Number2String(columnNumber, true);
                    ws.Cell(columnInExcel + (row + 1)).Value = rowData.GetType().GetProperty(prop.Name).GetValue(rowData, null);
                    columnNumber += 1;
                }
                row += 1;
                //add the data here
            }
            workbook.SaveAs(filePathToCreate);
            //now we can actually create the stuff in the file.
        }
        private String Number2String(int number, bool isCaps)
        {
            Char c = (Char)((isCaps ? 65 : 97) + (number - 1));

            return c.ToString();
        }

        private async Task<bool> SendAsync(string user, string attachmentFile)
        {
            bool isEmailSend = false;

            var message = new MailMessage(_emailConfiguration.Sender, user)
            {
                Subject = "Demonstration Letter",
                IsBodyHtml = true
            };

            string MailText = string.Format("Hi " + FirstLetterToUpper(user) + ",<br/><br/> " + " We have attached the requested demonstrations letter." + "</a> " + "<br/><br/>" + "<img src=\"cid:" + contentID + "\">");

            Attachment attachment = new Attachment(attachmentFile);
            message.Attachments.Add(attachment);
            message.Attachments.Add(footer);
            message.Body = MailText;

            using (var client = new SmtpClient(_emailConfiguration.Host, _emailConfiguration.Port))
            {
                client.Credentials = new NetworkCredential(_emailConfiguration.Username, _emailConfiguration.Password);
                client.EnableSsl = true;

                await client.SendMailAsync(message);
                isEmailSend = true;
            }

            return isEmailSend;
        }

        private async Task<bool> SendMailAsync(string user, byte[] bytes, string name)
        {
            bool isEmailSend = false;

            var message = new MailMessage(_emailConfiguration.Sender, user)
            {
                Subject = "Demonstration Letter",
                IsBodyHtml = true
            };

            string MailText = string.Format("Hi " + FirstLetterToUpper(user) + ",<br/><br/> " + " We have attached the requested demonstrations letter." + "</a> " + "<br/><br/>" + "<img src=\"cid:" + contentID + "\">");

            Attachment attachment = new Attachment(new MemoryStream(bytes), name);
            message.Attachments.Add(attachment);
            message.Attachments.Add(footer);
            message.Body = MailText;

            using (var client = new SmtpClient(_emailConfiguration.Host, _emailConfiguration.Port))
            {
                client.Credentials = new NetworkCredential(_emailConfiguration.Username, _emailConfiguration.Password);
                client.EnableSsl = true;

                await client.SendMailAsync(message);
                isEmailSend = true;
            }

            return isEmailSend;
        }

        private string FirstLetterToUpper(string name)
        {
            if (name == null)
            {
                return null;
            }

            if (name.Length > 1)
            {
                var text = char.ToUpper(name[0]) + name.Substring(1);

                return text.Replace("@primeinstore.co.za", "");
            }

            return name.ToUpper().Replace("@primeinstore.co.za", "");
        }

        private string ConvertDateToString(DateTime date)
        {
            return date.ToString("dd/MM/yyyy");
        }

        private string ConvertStringToDatetime(string date)
        {
            var newDate = Convert.ToDateTime(DateTime.Parse(date, CultureInfo.CreateSpecificCulture("fr-FR")));
            return newDate.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}
