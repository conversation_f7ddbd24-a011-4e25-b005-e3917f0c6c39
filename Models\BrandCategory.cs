﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BrandCategory
    {
        public BrandCategory()
        {
            BrandCategoryMembers = new HashSet<BrandCategoryMember>();
        }

        public int BrandCategoryId { get; set; }
        public string BrandCategoryName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<BrandCategoryMember> BrandCategoryMembers { get; set; }
    }
}
