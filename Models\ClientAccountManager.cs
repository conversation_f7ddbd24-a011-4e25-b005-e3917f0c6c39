﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ClientAccountManager
    {
        public int ClientId { get; set; }
        public DateTime EffectiveDate { get; set; }
        public int AccountManagerId { get; set; }
        public bool CareTaker { get; set; }
        public int CareTakerWeeks { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual AccountManager AccountManager { get; set; }
        public virtual Client Client { get; set; }
    }
}
