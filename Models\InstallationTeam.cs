﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationTeam
    {
        public InstallationTeam()
        {
            InstallationScheduleCurrents = new HashSet<InstallationScheduleCurrent>();
            InventoryItemMovementFromVans = new HashSet<InventoryItemMovement>();
            InventoryItemMovementToVans = new HashSet<InventoryItemMovement>();
            MasterItemInventoryItems = new HashSet<MasterItemInventoryItem>();
            Stores = new HashSet<Store>();
            TeamManagers = new HashSet<TeamManager>();
        }

        public int InstallationTeamId { get; set; }
        public string InstallationTeamUser { get; set; }
        public int InstallationTeamTypeId { get; set; }
        public string InstallationTeamName { get; set; }
        public int? RegionId { get; set; }

        public virtual InstallationTeamType InstallationTeamType { get; set; }
        public virtual ICollection<InstallationScheduleCurrent> InstallationScheduleCurrents { get; set; }
        public virtual ICollection<InventoryItemMovement> InventoryItemMovementFromVans { get; set; }
        public virtual ICollection<InventoryItemMovement> InventoryItemMovementToVans { get; set; }
        public virtual ICollection<InventoryItemTransactions> InventoryItemTransactionsFromVans { get; set; }
        public virtual ICollection<InventoryItemTransactions> InventoryItemTransactionsToVans { get; set; }
        public virtual ICollection<MasterItemInventoryItem> MasterItemInventoryItems { get; set; }
        public virtual ICollection<Store> Stores { get; set; }
        public virtual ICollection<TeamManager> TeamManagers { get; set; }
        public virtual OpsRegion Region { get; set; }
    }
}
