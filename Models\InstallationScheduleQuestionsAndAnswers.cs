﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationScheduleQuestionsAndAnswers
    {

        [Key]
        [Column("InstallationScheduleQuestionsAndAnswersId")]
        public int InstallationScheduleQuestionsAndAnswersId { get; set; }

        public Guid InstallationActionId { get; set; }
        public int? AnswerId { get; set; }
        public int? QuestionId { get; set; }

        public string Comment { get; set; }

        [ForeignKey(nameof(InstallationActionId))]
        public virtual InstallationScheduleCurrent InstallationScheduleCurrent { get; set; }

       
    }
}
