﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ImportedInstallationActions
    {
        [Key]
        [Column("ImportedInstallationActionsID")]
        public Guid ImportedInstallationActionsID { get; set; }

        [Column("ScheduleDate")]
        public DateTime ScheduleDate { get; set; }

        [Column("ContractNumber")]
        public string ContractNumber { get; set; }

        [Column("Action")]
        public string Action { get; set; }

        [Column("Category")]
        public string Category { get; set; }

        [Column("MyMobilityCategory")]
        public string MyMobilityCategory { get; set; }

        [Column("MediaType")]
        public string MediaType { get; set; }

        [Column("DateCompleted")]
        public DateTime DateCompleted { get; set; }

        [Column("IRCode")]
        public string IRCode { get; set; }

        [Column("IRNotes")]
        public string IRNotes { get; set; }

        [Column("HasPicture")]
        public bool HasPicture { get; set; }

        [Column("PictureURL")]
        public string PictureURL { get; set; }

        [Column("Chain")]
        public string Chain { get; set; }

        [Column("Store")]
        public string Store { get; set; }

        [Column("Region")]
        public string Region { get; set; }

        [Column("Owner")]
        public string Owner { get; set; }

        public bool? strikeRateReportGenerated { get; set; }


    }
}
