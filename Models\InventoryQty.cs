﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryQty
    {
        public InventoryQty()
        {
            ContractInventoryQties = new HashSet<ContractInventoryQty>();
            InventoryQtyPartItemQties = new HashSet<InventoryQtyPart>();
            InventoryQtyPartItemQtyParts = new HashSet<InventoryQtyPart>();
            InventoryQtyPrices = new HashSet<InventoryQtyPrice>();
        }

        public int ItemQtyId { get; set; }
        public int ItemId { get; set; }
        public int ItemQty { get; set; }

        public virtual Inventory Item { get; set; }
        public virtual ICollection<ContractInventoryQty> ContractInventoryQties { get; set; }
        public virtual ICollection<InventoryQtyPart> InventoryQtyPartItemQties { get; set; }
        public virtual ICollection<InventoryQtyPart> InventoryQtyPartItemQtyParts { get; set; }
        public virtual ICollection<InventoryQtyPrice> InventoryQtyPrices { get; set; }
    }
}
