﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Contract
    {
        public Contract()
        {
            BillingInstructionsNavigation = new HashSet<BillingInstruction>();
            Bursts = new HashSet<Burst>();
            Buttons = new HashSet<Button>();
            Consoles = new HashSet<Console>();
            ContractCostEstimates = new HashSet<ContractCostEstimate>();
            ContractInventoryQties = new HashSet<ContractInventoryQty>();
            ContractInvoices = new HashSet<ContractInvoice>();
            ContractMediaCosts = new HashSet<ContractMediaCost>();
            ContractMiscellaneousCharges = new HashSet<ContractMiscellaneousCharge>();
            ContractProducts = new HashSet<ContractProduct>();
            InstallationInstructions = new HashSet<InstallationInstruction>();
            PurchaseOrderNumbers = new HashSet<PurchaseOrderNumber>();
            Receivers = new HashSet<Receiver>();
            ResearchCategories = new HashSet<ResearchCategory>();
        }

        public Guid ContractId { get; set; }
        public int AccountManagerId { get; set; }
        public int ClientId { get; set; }
        public string ClientName { get; set; }
        public string ClientBillingAddress { get; set; }
        public string ContractNumber { get; set; }
        public string ContractType { get; set; }
        public DateTime? ContractDate { get; set; }
        public bool Signed { get; set; }
        public DateTime? SignDate { get; set; }
        public string SignedBy { get; set; }
        public string SpecialConditions { get; set; }
        public string ProjectName { get; set; }
        public bool ApplyAgencyComm { get; set; }
        public int? AgencyId { get; set; }
        public string AgencyName { get; set; }
        public decimal AgencyCommPercentage { get; set; }
        public bool AgencyCommIsPercentageOfNetRental { get; set; }
        public bool PrintAgencyComm { get; set; }
        public bool Cancelled { get; set; }
        public DateTime? CancelDate { get; set; }
        public string CancelledBy { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string BillingInstructions { get; set; }
        public string ContractNotes { get; set; }
        public bool RollForward { get; set; }
        public bool AddedValue { get; set; }
        public bool? IsCloned { get; set; }
        public string ClonedContractNumber { get; set; }
        public int? ContractClassificationId { get; set; }
        public int? ContractProposalHeatId { get; set; }
        public bool? IsReplacement { get; set; }
        public string ReasonsForCloningContract { get; set; }
        public bool? AllowCancelAndReplace { get; set; }
        public string DemoProvider { get; set; }
        public string DemoOwner { get; set; }

        public virtual AccountManager AccountManager { get; set; }
        public virtual Client Agency { get; set; }
        public virtual Client Client { get; set; }
        public virtual ContractClassification ContractClassification { get; set; }
        public virtual ContractProposalHeat ContractProposalHeat { get; set; }
        public virtual ContractDate ContractDateNavigation { get; set; }
        public virtual ContractApproved ContractApproved { get; set; }
        public virtual Burst Burst { get; set; }
       // public virtual Store Store { get; set; }
        public virtual InstallationInstruction InstallationInstruction { get; set; }
        public virtual ICollection<BillingInstruction> BillingInstructionsNavigation { get; set; }
        public virtual ICollection<Burst> Bursts { get; set; }
        public virtual ICollection<Button> Buttons { get; set; }
        public virtual ICollection<Console> Consoles { get; set; }
        public virtual ICollection<ContractCostEstimate> ContractCostEstimates { get; set; }
        public virtual ICollection<ContractInventoryQty> ContractInventoryQties { get; set; }
        public virtual ICollection<ContractInvoice> ContractInvoices { get; set; }
        public virtual ICollection<ContractMediaCost> ContractMediaCosts { get; set; }
        public virtual ICollection<ContractMiscellaneousCharge> ContractMiscellaneousCharges { get; set; }
        public virtual ICollection<ContractProduct> ContractProducts { get; set; }
        public virtual ICollection<InstallationInstruction> InstallationInstructions { get; set; }
        public virtual ICollection<PurchaseOrderNumber> PurchaseOrderNumbers { get; set; }
        public virtual ICollection<Receiver> Receivers { get; set; }
        public virtual ICollection<ResearchCategory> ResearchCategories { get; set; }
    }
}
