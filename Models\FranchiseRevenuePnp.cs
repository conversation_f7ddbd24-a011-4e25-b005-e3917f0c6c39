﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class FranchiseRevenuePnp
    {
        public string StoreName { get; set; }
        public string Period { get; set; }
        public decimal? TotalRentalDue { get; set; }
        public decimal? HeadOfficeRentalDue { get; set; }
        public decimal WeeklyRevenuePerBillableWeekPerStore { get; set; }
        public decimal? WeeklyCrossoverRevenuePerBillableWeekPerStore { get; set; }
        public decimal? TotalPerStore { get; set; }
        public string Chain { get; set; }
        public DateTime Week { get; set; }
        public Guid ContractId { get; set; }
        public string TypeOfStore { get; set; }
    }
}
