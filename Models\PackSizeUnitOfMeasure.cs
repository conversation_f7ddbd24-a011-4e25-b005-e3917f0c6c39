﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class PackSizeUnitOfMeasure
    {
        public PackSizeUnitOfMeasure()
        {
            BemsTasks = new HashSet<BemsTask>();
        }

        public Guid PackSizeUnitOfMeasureId { get; set; }
        public string PackSizeUnitOfMeasureName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<BemsTask> BemsTasks { get; set; }
    }
}
