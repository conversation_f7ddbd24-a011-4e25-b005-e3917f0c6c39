﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class AccountManagerPermission
    {
        public AccountManagerPermission()
        {
            AccountManagerPermissionUsers = new HashSet<AccountManagerPermissionUser>();
        }

        public int AccountManagerPermissionId { get; set; }
        public string AccountManagerPermissionName { get; set; }

        public virtual ICollection<AccountManagerPermissionUser> AccountManagerPermissionUsers { get; set; }
    }
}
