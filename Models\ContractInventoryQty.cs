﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ContractInventoryQty
    {
        public Guid ContractItemQtyId { get; set; }
        public Guid ContractId { get; set; }
        public int ItemQtyId { get; set; }
        public Guid BrandId { get; set; }
        public decimal SellPrice { get; set; }
        public string Notes { get; set; }

        public virtual Brand Brand { get; set; }
        public virtual Contract Contract { get; set; }
        public virtual InventoryQty ItemQty { get; set; }
    }
}
