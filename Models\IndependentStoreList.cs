﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class IndependentStoreList
    {
        public IndependentStoreList()
        {
            IndependentStoreListMembers = new HashSet<IndependentStoreListMember>();
        }

        public Guid IndependentStoreListId { get; set; }
        public string IndependentStoreListName { get; set; }
        public int PrincipalId { get; set; }

        public virtual ICollection<IndependentStoreListMember> IndependentStoreListMembers { get; set; }
    }
}
