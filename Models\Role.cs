﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Role
    {
        public Role()
        {
            Rolemembers = new HashSet<Rolemember>();
            Roleowner1s = new HashSet<Roleowner1>();
        }

        public Guid Roleid { get; set; }
        public string Rolename { get; set; }
        public string Roledescription { get; set; }

        public virtual ICollection<Rolemember> Rolemembers { get; set; }
        public virtual ICollection<Roleowner1> Roleowner1s { get; set; }
    }
}
