﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ThisIsMine
    {
        public decimal? _201601 { get; set; }
        public decimal? _201602 { get; set; }
        public decimal? _201603 { get; set; }
        public decimal? _201604 { get; set; }
        public decimal? _201605 { get; set; }
        public decimal? _201606 { get; set; }
        public decimal? _201607 { get; set; }
        public decimal? _201608 { get; set; }
        public decimal? _201609 { get; set; }
        public decimal? _201610 { get; set; }
        public decimal? _201611 { get; set; }
        public decimal? _201612 { get; set; }
        public decimal? _201701 { get; set; }
        public decimal? _201702 { get; set; }
        public decimal? _201703 { get; set; }
        public decimal? _201704 { get; set; }
        public decimal? _201705 { get; set; }
        public decimal? _201706 { get; set; }
        public decimal? _201707 { get; set; }
        public decimal? _201708 { get; set; }
        public decimal? _201709 { get; set; }
        public decimal? _201710 { get; set; }
        public decimal? _201711 { get; set; }
        public decimal? _201712 { get; set; }
        public decimal? _201801 { get; set; }
        public decimal? _201802 { get; set; }
        public decimal? _201803 { get; set; }
        public decimal? _201804 { get; set; }
        public decimal? _201805 { get; set; }
        public decimal? _201806 { get; set; }
        public decimal? _201807 { get; set; }
        public decimal? _201808 { get; set; }
        public decimal? _201809 { get; set; }
        public decimal? _201810 { get; set; }
        public decimal? _201811 { get; set; }
        public decimal? _201812 { get; set; }
        public decimal? _201901 { get; set; }
        public decimal? _201902 { get; set; }
        public decimal? _201903 { get; set; }
        public decimal? _201904 { get; set; }
        public decimal? _201905 { get; set; }
        public decimal? _201906 { get; set; }
        public decimal? _201907 { get; set; }
        public decimal? _201908 { get; set; }
        public decimal? _201909 { get; set; }
        public decimal? _201910 { get; set; }
        public decimal? _201911 { get; set; }
        public decimal? _201912 { get; set; }
        public string ContractNumber { get; set; }
    }
}
