﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class User1
    {
        public User1()
        {
            BemsTasks = new HashSet<BemsTask>();
            VanUsers = new HashSet<VanUser>();
        }

        public Guid UserId { get; set; }
        public string Username { get; set; }
        public string UserPassword { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<BemsTask> BemsTasks { get; set; }
        public virtual ICollection<VanUser> VanUsers { get; set; }
    }
}
