﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using static PhoenixAPI.Controllers.OpsManagementController;

namespace PhoenixAPI.Controllers
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "Admin")]
    public class OpsUniversalController : ControllerBase
    {

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetIRCodes")]
        [HttpGet]
        public async Task<object> GetIRCodes(bool isStoreSkipVisit = false)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    if(isStoreSkipVisit)
                    {
                        var result = await db.IRCodes.Where(x => x.isStoreSkip).OrderBy(x => x.IrCodeSortOrder).ThenBy(x => x.IRCodeID).ToListAsync();
                        return result;
                    }
                    else
                    {
                        var result = await db.IRCodes.OrderBy(x => x.IrCodeSortOrder).ThenBy(x => x.IRCodeID).ToListAsync();
                        return result;
                    }
                 
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser, Storeman")]
        [Route("GetPendingMasteritemMovementByWarehouse/{userId}")]
        [HttpGet]
        public async Task<object> GetPendingMasteritemMovementByWarehouse(string userId)
        {
            //Querying with LINQ to Entities 
            //we need to check for a user here
            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    //var results = await db.InventoryItemMovements
                    //.Include(a => a.FromWarehouse)
                    //.Include(b => b.ToWarehouse)
                    //.Include(c => c.MasterItem)
                    //.Include(d => d.ToInstallationTeam)
                    //.Include(e => e.FromInstallationTeam)
                    

                    //.Where(x => x.ToWarehouse.ApplicationUserId == userId || x.ToInstallationTeam.InstallationTeamUser == userId)
                    //    .Select(x => x.Barcode).ToListAsync();

                    return null;
                   // return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }
        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser")]
        [Route("GetPendingMasteritemMovementByWarehouseGrouped/{userId}")]
        [HttpGet]
        public async Task<object> GetPendingMasteritemMovementByWarehouseGrouped(string userId)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryItemMovements
                    .Include(a => a.FromWarehouse)
                    .Include(b => b.ToWarehouse)
                    .Include(c => c.MasterItem)
                      .Include(d => d.ToInstallationTeam)
                    .Include(e => e.FromInstallationTeam)

                   .Where(x => x.ToWarehouse.ApplicationUserId == userId || x.ToInstallationTeam.InstallationTeamUser == userId)
                        .ToListAsync();
                    List<GroupedItemsRecievable> groupedItemsRecievables = new List<GroupedItemsRecievable>();
                    foreach (var item in results)
                    {
                        GroupedItemsRecievable groupedItemsRecievable = new GroupedItemsRecievable();
                        groupedItemsRecievable.AmountToRecieve = 1;
                        groupedItemsRecievable.id = item.MasterItemId;
                        groupedItemsRecievable.MasterItemName = item.MasterItem.MasterItemName;

                        //from installation Team
                        if(item.FromInstallationTeam != null)
                        {
                            groupedItemsRecievable.FromInstallationTeamId = item.FromInstallationTeam.InstallationTeamId;
                            groupedItemsRecievable.FromInstallationTeamName = item.FromInstallationTeam.InstallationTeamName;
                        }
                        else
                        {
                            groupedItemsRecievable.FromInstallationTeamId = 0;
                            groupedItemsRecievable.FromInstallationTeamName = "";
                        }

                        //to installation team
                        if (item.ToInstallationTeam != null)
                        {
                            groupedItemsRecievable.ToInstallationTeamId = item.ToInstallationTeam.InstallationTeamId;
                            groupedItemsRecievable.ToInstallationTeamName = item.ToInstallationTeam.InstallationTeamName;
                        }
                        else
                        {
                            groupedItemsRecievable.ToInstallationTeamId = 0;
                            groupedItemsRecievable.ToInstallationTeamName = "";
                        }

                        //from warehouse 
                        if (item.FromWarehouse != null)
                        {
                            groupedItemsRecievable.FromWarehouseId = item.FromWarehouse.WarehouseId;
                            groupedItemsRecievable.FromWarehouseName = item.FromWarehouse.WarehouseName;
                        }
                        else
                        {
                            groupedItemsRecievable.FromWarehouseId = 0;
                            groupedItemsRecievable.FromWarehouseName = "";
                        }

                        //to warehouse
                        if (item.ToWarehouse != null)
                        {
                            groupedItemsRecievable.ToWarehouseId = item.ToWarehouse.WarehouseId;
                            groupedItemsRecievable.ToWarehouseName = item.ToWarehouse.WarehouseName;
                        }
                        else
                        {
                            groupedItemsRecievable.ToWarehouseId = 0;
                            groupedItemsRecievable.ToWarehouseName = "";
                        }

                        groupedItemsRecievables.Add(groupedItemsRecievable);

                    }

                    return groupedItemsRecievables.GroupBy(x => new { MasterItemName = x.MasterItemName, x.id, FromWarehouseName = x.FromWarehouseName , FromWarehouseId = x.FromWarehouseId, toWarehouseId = x.ToWarehouseId, x.FromInstallationTeamId, FromInstallationTeamName = x.FromInstallationTeamName, x.ToInstallationTeamId, ToInstallationTeamName = x.ToInstallationTeamName })
                        .Select(x => new { x.Key.id, x.Key.MasterItemName, x.Key.FromWarehouseId, x.Key.FromWarehouseName, x.Key.toWarehouseId, x.Key.FromInstallationTeamId, x.Key.FromInstallationTeamName, x.Key.ToInstallationTeamName, x.Key.ToInstallationTeamId, qtyToRecieve = x.Count() }).ToList()
                        .OrderBy(x => x.id).ToList();;
                    //we need something different here.
                    //var myResulst = results.Select(x => new { x.MasterItem.MasterItemName, x.MasterItem.Id, FromWarehouseName = x.FromWarehouse.WarehouseName, FromWarehouseId = x.FromWarehouse.WarehouseId, toWarehouseId = x.ToWarehouseId }).ToList();
                    //return results.GroupBy(x => new { MasterItemName =  x.MasterItem.MasterItemName, x.MasterItem.Id, FromWarehouseName = x.FromWarehouse.WarehouseName ?? "", FromWarehouseId = x.FromWarehouseId, toWarehouseId = x.ToWarehouseId , x.FromVanId, FromInstallationTeamName = x.FromInstallationTeam.InstallationTeamName ?? "", x.ToVanId, ToInstallationTeamName = x.ToInstallationTeam.InstallationTeamName ?? ""})
                    //    .Select(x => new { x.Key.Id, x.Key.MasterItemName, x.Key.FromWarehouseId, x.Key.FromWarehouseName, x.Key.toWarehouseId, x.Key.FromVanId, x.Key.FromInstallationTeamName, x.Key.ToInstallationTeamName, x.Key.ToVanId, qtyToRecieve = x.Count() }).ToList()
                    //    .OrderBy(x => x.Id).ToList();



                    groupedItemsRecievables.AddRange(results.Select(x => new GroupedItemsRecievable { MasterItemName = x.MasterItem.MasterItemName, id = x.MasterItem.Id, FromWarehouseName = x.FromWarehouse.WarehouseName ?? "", FromWarehouseId = x.FromWarehouseId, ToWarehouseId = x.ToWarehouseId, FromInstallationTeamId =  x.FromVanId, FromInstallationTeamName = x.FromInstallationTeam.InstallationTeamName , ToInstallationTeamId = x.ToVanId, ToInstallationTeamName = x.ToInstallationTeam.InstallationTeamName })
                        .ToList());


                    return groupedItemsRecievables;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser")]
        [Route("GetBarcodesForReceiving")]
        [HttpPost]
        public async Task<object> GetBarcodesForReceiving(BarcodesForReceivingSearch search)
        {
            //Querying with LINQ to Entities 

            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryItemMovements
                    .Include(a => a.FromWarehouse)
                    .Include(b => b.ToWarehouse)
                    .Include(c => c.MasterItem)
                      .Include(d => d.ToInstallationTeam)
                    .Include(e => e.FromInstallationTeam)
                    .Where(x => (x.ToWarehouse.ApplicationUserId == email || x.ToInstallationTeam.InstallationTeamUser == email) && x.MasterItem.Id == search.Id && (x.ToWarehouse.WarehouseId == search.toWarehouseId || x.ToVanId == search.toInstallationTeamId))
                        .ToListAsync();


                    return results;


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }


        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser")]
        [Route("ReceiveItems")]
        [HttpPost]

        public async Task<object> ReceiveItems(List<InventoryItemMovement> itemMovements)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    //what we need to do is remove them from the movement, but update their location.

                    //warehouse.CreatedBy = email;
                    //db.InventoryItemMovements.RemoveRange(itemMovements);
                    //db.SaveChanges();

                    //lets update
                    foreach (var itemToUpdate in itemMovements)
                    {

                        var itemToRemove = db.InventoryItemMovements.Where(x => x.Id == itemToUpdate.Id).FirstOrDefault();
                        db.InventoryItemMovements.Remove(itemToRemove);

                        var thisItem = db.MasterItemInventoryItems.Where(x => x.MasterItemId == itemToUpdate.MasterItemId && x.Id == itemToUpdate.MasterItemInvenoryItemId).FirstOrDefault();
                        thisItem.WarehouseId = itemToUpdate.ToWarehouseId;
                        thisItem.InstallationTeamId = itemToUpdate.ToVanId;
                        db.MasterItemInventoryItems.Update(thisItem);
                        db.SaveChanges();
                    }

                    var results = await db.InventoryItemMovements
                        .ToListAsync();


                    return results.Select(x => x.Barcode).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetStockTakeRoles")]
        [HttpGet]
        public async Task<object> GetStockTakeRoles()
        {
            //check if there is a stock take in progress
            //we also want to limit this to roles that are not already selected for the loctation
            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryStockTakeRole.OrderBy(x => x.RoleName).ToListAsync();


                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("CreateStockTakeDetail")]
        [HttpPost]

        public async Task<object> CreateStockTakeDetail(InventoryStockTakeDetails inventoryStockTakeDetails)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //we should ensure we only create one at a time
                    inventoryStockTakeDetails.userName = email;
                    inventoryStockTakeDetails.CreatedBy = email;
                    inventoryStockTakeDetails.CreationDate = System.DateTime.Now;
                    //ok, we should ensure it doesn't create it twice

                    var isExisting = db.InventoryStockTakeDetails.Where(x => x.isCompleted == false && x.userName == email).FirstOrDefault();

                    if(isExisting != null)
                    {
                        return isExisting;

                    }
                    else
                    {
                        db.InventoryStockTakeDetails.Add(inventoryStockTakeDetails);
                        db.SaveChanges();
                    }


                    var results = await db.InventoryStockTakeDetails.Where(x => x.isCompleted == false && x.userName == email).Include(x => x.InstallationTeam).Include(x => x.Warehouse).Include(x => x.InventoryStockTakeRole).FirstOrDefaultAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }



        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetStockTakeDetails")]
        [HttpGet]
        public async Task<object> GetStockTakeDetails()
        {
            //check if there is a stock take in progress
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryStockTakeDetails.Include(x => x.InventoryStockTakeDetailsBarcodes).ThenInclude(x => x.MasterItem).Include(x => x.InstallationTeam).Include(x => x.Warehouse).Include(x => x.InventoryStockTakeRole).Where(x => x.isCompleted == false && x.userName == email).FirstOrDefaultAsync();


                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetWarehouses")]
        [HttpGet]
        public async Task<object> GetWarehouses()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.Warehouse.Include(x => x.LocationType).Include(x => x.WarehouseManagers).OrderBy(x => x.WarehouseName).Include(x => x.Region).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetBarcodes")]
        [HttpGet]
        public async Task<object> GetBarcodes()
        {
            using (var context = new NovaDBContext())
            {
                var results = await context.MasterItemInventoryItems.ToListAsync();

                return results;
            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetVans")]
        [HttpGet]
        public async Task<object> GetVans()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.InstallationTeam.OrderBy(x => x.InstallationTeamName).Include(x => x.Region).ToListAsync();

                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetInventoryStockTakeBarcodes")]
        [HttpGet]
        public async Task<object> GetInventoryStockTakeBarcodes(string user, int stockTakeId)
        {
            using(var db = new NovaDBContext())
            {
                return await db.InventoryStockTakeDetailsBarcodes.Where(x => x.userName == user && x.InventoryStockTakeDetails.InventoryStockTakeRoleId == 1 
                && x.InventoryStockTakeDetails.InventoryStockTakeId == stockTakeId).Include(x => x.InventoryStockTakeDetails).Select(x => new
                {
                    Barcode = x.barcode,
                    InventoryStockTakeRole = x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName,
                    InventoryStockTakeRoleId = x.InventoryStockTakeDetails.InventoryStockTakeRole.InventoryStockTakeRoleId,
                    MasterItemId = x.MasterItemId,
                    LocationId = x.InstallationTeamId != null ? x.InstallationTeam.InstallationTeamId : x.Warehouse.WarehouseId,
                    Location = x.InstallationTeam != null ? x.InstallationTeam.InstallationTeamName : x.Warehouse.WarehouseName,
                    CreatedBy = x.userName,
                    CreationDate = x.CreationDate
                }).ToListAsync();
            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetBarcodeByValue")]
        [HttpGet]
        public async Task<object> GetBarcodeByValue(string barcode)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = db.MasterItemInventoryItems.Where(x => x.Barcode == barcode).Include(x => x.Shelf).Include(x => x.Warehouse).Include(x => x.MasterItem).FirstOrDefault();
                    // .Where(x => x.Barcode == barcode).FirstOrDefault();
                    //we need to check that it is not currently being moved.
                    //check that result isn't currently being moved
                    if (results != null)
                    {
                        var isMoved = db.InventoryItemMovements.Where(x => x.MasterItemInvenoryItemId == results.Id).FirstOrDefault();
                        if (isMoved != null)
                        {
                            return results;
                        }
                        else
                        {
                            return results;
                        }

                    }

                    return results;


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("CreateBarcodeScannedForStockTake")]
        [HttpPost]

        public async Task<object> CreateBarcodeScannedForStockTake(List<InventoryStockTakeDetailsBarcodes> inventoryStockTakeDetailsBarcodes)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    List<InventoryStockTakeDetailsBarcodes> lstInventoryStockTakeDetailsBarcodes = new List<InventoryStockTakeDetailsBarcodes>();
                    //we should ensure we only create one at a time
                    var stockTakeId = db.InventoryStockTake.Where(x => x.isCompleted == false).OrderByDescending(x => x.InventoryStockTakeId).FirstOrDefaultAsync().Result.InventoryStockTakeId;
                    foreach (InventoryStockTakeDetailsBarcodes inventoryStockTakeDetails in inventoryStockTakeDetailsBarcodes)
                    {
                        inventoryStockTakeDetails.userName = email;
                        //ensure it doesnt exist here.
                        

                        var isExisting = db.InventoryStockTakeDetailsBarcodes.Where(x => x.barcode == inventoryStockTakeDetails.barcode && x.userName == inventoryStockTakeDetails.userName && x.InventoryStockTakeDetails.InventoryStockTakeId == stockTakeId).FirstOrDefault();

                        if (isExisting != null && inventoryStockTakeDetails.MasterItemId != isExisting.MasterItemId)
                        {
                            isExisting.MasterItemId = inventoryStockTakeDetails.MasterItemId;
                            isExisting.isWrongCapex = inventoryStockTakeDetails.isWrongCapex;
                            db.InventoryStockTakeDetailsBarcodes.Update(isExisting);
                            db.SaveChanges();
                        }
                        if(isExisting == null)
                        {
                            inventoryStockTakeDetails.CreationDate = System.DateTime.Now;
                            lstInventoryStockTakeDetailsBarcodes.Add(inventoryStockTakeDetails);
                        }
                        else
                        {
                            
                        }
                       
                    }
                    db.InventoryStockTakeDetailsBarcodes.AddRange(lstInventoryStockTakeDetailsBarcodes);
                    db.SaveChanges();
                    //ok, we should ensure it doesn't create it twice
                    //check if it exists

                    //this should be careful off media, but we will implement media later



                    //var results = await db.InventoryStockTakeDetails.Include(x => x.InventoryStockTakeDetailsBarcodes).ThenInclude(x => x.MasterItem).Where(x => x.isCompleted == false && x.userName == email)
                    var results = db.InventoryStockTakeDetails.Include(x => x.InventoryStockTakeDetailsBarcodes).ThenInclude(x => x.MasterItem).Where(x => x.isCompleted == false && x.userName == email).ToList();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetMasterItems")]
        [HttpGet]
        public async Task<object> GetMasterItems()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = db.MasterItems.Include(x => x.MasterItemType).Include(y => y.MasterItemGroupMembers).ThenInclude(z => z.MasterItemGroup).ToList().OrderBy(x => x.MasterItemName);
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("FinishStockTake")]
        [HttpPost]

        public async Task<object> FinishStockTake(InventoryStockTakeDetails inventoryStockTakeDetails)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {

                    var isExisting = db.InventoryStockTakeDetails.Where(x => x.isCompleted == false && x.userName == email && inventoryStockTakeDetails.InventoryStockTakeDetailId == x.InventoryStockTakeDetailId).FirstOrDefault();

                    if (isExisting != null)
                    {
                        isExisting.isCompleted = true;
                        db.InventoryStockTakeDetails.Update(isExisting);
                        db.SaveChanges();

                    }
                    return isExisting;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

    }

    public class GroupedItemsRecievable
    {
        public int? id { get; set; }
        public string MasterItemName { get; set; }
        public int? FromWarehouseId { get; set; }
        public string FromWarehouseName { get; set; }
        public int? ToWarehouseId { get; set; }
        public string ToWarehouseName { get; set; }
        public int? FromInstallationTeamId { get; set; }
        public string FromInstallationTeamName { get; set; }
        public int? ToInstallationTeamId { get; set; }
        public string ToInstallationTeamName { get; set; }
        public int AmountToRecieve { get; set; }
        

    }
    public class StockTakeCounterLoccations
    {
        public int WarehouseId { get; set; }
        public int InstallationTeamId { get; set; }
    }
}
