﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Inventory
    {
        public Inventory()
        {
            InventoryQties = new HashSet<InventoryQty>();
            MediaInventories = new HashSet<MediaInventory>();
            SupplierInventories = new HashSet<SupplierInventory>();
        }

        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public bool Dormant { get; set; }
        public bool? AllowInContracts { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<InventoryQty> InventoryQties { get; set; }
        public virtual ICollection<MediaInventory> MediaInventories { get; set; }
        public virtual ICollection<SupplierInventory> SupplierInventories { get; set; }
    }
}
