﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class DistributionRegion
    {
        public DistributionRegion()
        {
            Van1s = new HashSet<Van1>();
        }

        public int DistributionRegionId { get; set; }
        public string DistributionRegionName { get; set; }

        public virtual ICollection<Van1> Van1s { get; set; }
    }
}
