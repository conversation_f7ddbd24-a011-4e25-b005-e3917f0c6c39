﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryStockTakeRole
    {

        [Key]
        [Column("InventoryStockTakeRoleId")]
        public int InventoryStockTakeRoleId { get; set; }

        public string RoleName { get; set; }
       
        public DateTime CreationDate { get; set; }
        public string CreatedBy { get; set; }
        public bool Dormant { get; set; }


    }
}
