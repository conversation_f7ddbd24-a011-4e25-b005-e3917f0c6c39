﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ResearchCategory
    {
        public Guid ResearchCategoryId { get; set; }
        public Guid ContractId { get; set; }
        public int CategoryId { get; set; }
        public DateTime FromDate { get; set; }
        public int Months { get; set; }
        public decimal Fee { get; set; }
        public decimal Discount { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Category1 Category { get; set; }
        public virtual Contract Contract { get; set; }
    }
}
