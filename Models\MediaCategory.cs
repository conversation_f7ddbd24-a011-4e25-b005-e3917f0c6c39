﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaCategory
    {
        public int MediaId { get; set; }
        public int CategoryId { get; set; }
        public int? MediaLimit { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Category1 Category { get; set; }
        public virtual Media Media { get; set; }
    }
}
