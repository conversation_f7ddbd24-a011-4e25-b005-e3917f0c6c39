﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Answers
    {

        [Key]
        [Column("AnswerId")]
        public int AnswerId { get; set; }

        public string Answer { get; set; }
        public DateTime DateCreated { get; set; }
       
        public string CreatedBy { get; set; }
        public bool Dormant { get; set; }

        public bool? NeedsComment { get; set; }

        public virtual ICollection<QuestionsAndAnswers> QuestionsAndAnswers { get; set; }
    }
}
