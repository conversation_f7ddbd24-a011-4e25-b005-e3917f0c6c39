﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Vunallocatedrequest
    {
        public string Number { get; set; }
        public DateTime FromDate1 { get; set; }
        public DateTime ToDate1 { get; set; }
        public DateTime FromDate2 { get; set; }
        public DateTime ToDate2 { get; set; }
        public DateTime? PcrReadyDate { get; set; }
        public string MinBarcode { get; set; }
        public string MaxBarcode { get; set; }
        public int? BarcodeCount { get; set; }
        public Guid? RequestId { get; set; }
        public bool? FamilyStoresData { get; set; }
        public bool? CorpStoresData { get; set; }
    }
}
