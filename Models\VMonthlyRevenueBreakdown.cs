﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VMonthlyRevenueBreakdown
    {
        public string Code { get; set; }
        public string ContractNumber { get; set; }
        public int? CalendarMonth { get; set; }
        public string MonthName { get; set; }
        public string MediaName { get; set; }
        public decimal ClientBilling { get; set; }
        public decimal RevenueRecognition { get; set; }
        public decimal? PreBilling { get; set; }
        public int? CalendarYear { get; set; }
        public string ClientName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int WeeksThisMonth { get; set; }
        public decimal ProductionAmount { get; set; }
        public string BrandName { get; set; }
        public decimal Billingamountexcludingproduction { get; set; }
        public int ClientId { get; set; }
        public int? ContractClassificationId { get; set; }
        public int AccountManagerId { get; set; }
        public Guid ContractId { get; set; }
        public string CalendarDate { get; set; }
    }
}
