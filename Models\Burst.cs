﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Burst
    {
        public Burst()
        {
            BurstCategories = new HashSet<BurstCategory>();
            BurstInstallationDays = new HashSet<BurstInstallationDay>();
            BurstLoadingFees = new HashSet<BurstLoadingFee>();
            ContractMediaCosts = new HashSet<ContractMediaCost>();
            InstallationSchedules = new HashSet<InstallationSchedule>();
            StoreLists = new HashSet<StoreList>();
        }

        public Guid BurstId { get; set; }
        public Guid ContractID { get; set; }
        public int ChainId { get; set; }
        public string ChainName { get; set; }
        public Guid StorePoolId { get; set; }
        public int MediaId { get; set; }
        public string MediaName { get; set; }
        public Guid BrandId { get; set; }
        public string BrandName { get; set; }
        public string ProductName { get; set; }
        public DateTime FirstWeek { get; set; }
        public int InstallWeeks { get; set; }
        public int InstallStoreQty { get; set; }
        public int BillableStoreQty { get; set; }
        public decimal RentalRate { get; set; }
        public int BillableWeeks { get; set; }
        public decimal Discount { get; set; }
        public int CrossoverQty { get; set; }
        public bool InstallAtHomesite { get; set; }
        public string InstallationInstructions { get; set; }
        public int AdsPerInstallation { get; set; }
        public int AdsPerCrossover { get; set; }
        public int? AdsPerShelfTalk { get; set; }
        public bool? InstallRegardlessOfStock { get; set; }
        public bool StoreListConfirmed { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public bool? ApplyInstructionsAcrossAllBursts { get; set; }
        public bool? TempIgnoreDefaultInstructions { get; set; }

        public virtual Brand Brand { get; set; }
        public virtual Chain Chain { get; set; }
        public virtual Contract Contract { get; set; }
        public virtual Media Media { get; set; }
        public virtual StorePool StorePool { get; set; }
        public virtual ICollection<BurstCategory> BurstCategories { get; set; }
        public virtual ICollection<BurstInstallationDay> BurstInstallationDays { get; set; }
        public virtual ICollection<BurstLoadingFee> BurstLoadingFees { get; set; }
        public virtual ICollection<ContractMediaCost> ContractMediaCosts { get; set; }
        public virtual ICollection<InstallationSchedule> InstallationSchedules { get; set; }
        public virtual ICollection<StoreList> StoreLists { get; set; }
    }
}
