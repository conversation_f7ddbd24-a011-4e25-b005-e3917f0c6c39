﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class FindDuplicate
    {
        public string Period { get; set; }
        public string AssignedAm { get; set; }
        public string CurrentAm { get; set; }
        public string ContractAm { get; set; }
        public string Client { get; set; }
        public string Brand { get; set; }
        public string MediaService { get; set; }
        public string Contract { get; set; }
        public string Chain { get; set; }
        public string ChainType { get; set; }
        public string Category { get; set; }
        public string CategoryType { get; set; }
        public decimal? CurrentForecastRevenue { get; set; }
        public decimal? PriorForecastRevenue { get; set; }
        public decimal? PriorActualRevenue { get; set; }
        public bool? AgencyComm { get; set; }
        public bool? Signed { get; set; }
        public bool? Approved { get; set; }
        public string ProposalHeat { get; set; }
    }
}
