﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddInventoryItemTransactions : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PreviousIRCodeComment",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PreviousIRCodeId",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "InventoryItemTransactions",
                schema: "Ops",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Barcode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    MasterItemInvenoryItemId = table.Column<int>(type: "int", nullable: true),
                    FromWarehouseId = table.Column<int>(type: "int", nullable: true),
                    ToWarehouseId = table.Column<int>(type: "int", nullable: true),
                    MasterItemId = table.Column<int>(type: "int", nullable: true),
                    createdBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, defaultValueSql: "(suser_sname())"),
                    creationDate = table.Column<DateTime>(type: "datetime", nullable: false, defaultValueSql: "(getdate())"),
                    deletedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    deletionDate = table.Column<DateTime>(type: "datetime", nullable: true),
                    FromVanId = table.Column<int>(type: "int", nullable: true),
                    ToVanId = table.Column<int>(type: "int", nullable: true),
                    ToStoreId = table.Column<int>(type: "int", nullable: true),
                    FromStoreId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryItemTransactions", x => x.id);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_InstallationTeam_FromVanId",
                        column: x => x.FromVanId,
                        principalSchema: "Ops",
                        principalTable: "InstallationTeam",
                        principalColumn: "InstallationTeamID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_InstallationTeam_ToVanId",
                        column: x => x.ToVanId,
                        principalSchema: "Ops",
                        principalTable: "InstallationTeam",
                        principalColumn: "InstallationTeamID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_MasterItem_MasterItemId",
                        column: x => x.MasterItemId,
                        principalSchema: "Ops",
                        principalTable: "MasterItem",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_MasterItemInventoryItem_MasterItemInvenoryItemId",
                        column: x => x.MasterItemInvenoryItemId,
                        principalSchema: "Ops",
                        principalTable: "MasterItemInventoryItem",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_Store_FromStoreId",
                        column: x => x.FromStoreId,
                        principalSchema: "Store",
                        principalTable: "Store",
                        principalColumn: "StoreID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_Store_ToStoreId",
                        column: x => x.ToStoreId,
                        principalSchema: "Store",
                        principalTable: "Store",
                        principalColumn: "StoreID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_Warehouse_FromWarehouseId",
                        column: x => x.FromWarehouseId,
                        principalSchema: "Store",
                        principalTable: "Warehouse",
                        principalColumn: "WarehouseID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryItemTransactions_Warehouse_ToWarehouseId",
                        column: x => x.ToWarehouseId,
                        principalSchema: "Store",
                        principalTable: "Warehouse",
                        principalColumn: "WarehouseID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InstallationScheduleCurrent_PreviousIRCodeId",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                column: "PreviousIRCodeId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_FromStoreId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "FromStoreId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_FromVanId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "FromVanId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_FromWarehouseId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "FromWarehouseId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_MasterItemId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "MasterItemId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_MasterItemInvenoryItemId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "MasterItemInvenoryItemId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_ToStoreId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "ToStoreId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_ToVanId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "ToVanId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_ToWarehouseId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "ToWarehouseId");

            migrationBuilder.AddForeignKey(
                name: "FK_InstallationScheduleCurrent_IRCodes_PreviousIRCodeId",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                column: "PreviousIRCodeId",
                principalSchema: "Ops",
                principalTable: "IRCodes",
                principalColumn: "IRCodeID",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InstallationScheduleCurrent_IRCodes_PreviousIRCodeId",
                schema: "Ops",
                table: "InstallationScheduleCurrent");

            migrationBuilder.DropTable(
                name: "InventoryItemTransactions",
                schema: "Ops");

            migrationBuilder.DropIndex(
                name: "IX_InstallationScheduleCurrent_PreviousIRCodeId",
                schema: "Ops",
                table: "InstallationScheduleCurrent");

            migrationBuilder.DropColumn(
                name: "PreviousIRCodeComment",
                schema: "Ops",
                table: "InstallationScheduleCurrent");

            migrationBuilder.DropColumn(
                name: "PreviousIRCodeId",
                schema: "Ops",
                table: "InstallationScheduleCurrent");
        }
    }
}
