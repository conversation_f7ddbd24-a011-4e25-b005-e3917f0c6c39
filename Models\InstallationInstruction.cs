﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationInstruction
    {
        public int Id { get; set; }
        public Guid? Contractid { get; set; }
        public string Instruction { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? StoreID { get; set; }
        public DateTime? InstructionExecutionDate { get; set; }
        public Guid? BurstId { get; set; }
        public int? CategoryId { get; set; }
        public bool? Homesite { get; set; }

        public virtual Contract Contract { get; set; }
        public virtual Store Store { get; set; }
    }
}
