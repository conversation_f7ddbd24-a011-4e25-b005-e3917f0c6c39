﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class LoadingFee
    {
        public LoadingFee()
        {
            BurstLoadingFees = new HashSet<BurstLoadingFee>();
        }

        public int LoadingFeeId { get; set; }
        public string LoadingFeeName { get; set; }
        public decimal DefaultPercentage { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<BurstLoadingFee> BurstLoadingFees { get; set; }
    }
}
