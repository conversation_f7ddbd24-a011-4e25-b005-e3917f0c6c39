﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Models
{
    public partial class IncomingMediaPerRegion
    {
        public int Id { get; set; }
        public Guid ContractId { get; set; }
        public string? Campaign { get; set; }
        public string? Chain { get; set; }
        public string? ContractNumber { get; set; }
        public DateTime? CreationDate { get; set; }
        public string? MediaType { get; set; }
        public int? RegionId { get; set; }
        public string? RegionName { get; set; }
        public string? DeliveryNote { get; set; }
        public int? BalanceQty { get; set; }
        public int? ReceivedInitiallyQty { get; set; }
    }
}
