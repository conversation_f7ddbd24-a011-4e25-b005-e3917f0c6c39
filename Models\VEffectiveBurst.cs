﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VEffectiveBurst
    {
        public Guid BurstId { get; set; }
        public Guid ContractId { get; set; }
        public string ContractNumber { get; set; }
        public bool Signed { get; set; }
        public DateTime? SignDate { get; set; }
        public bool Cancelled { get; set; }
        public int ChainId { get; set; }
        public string ChainName { get; set; }
        public int CategoryId { get; set; }
        public bool? Homesite { get; set; }
        public Guid StorePoolId { get; set; }
        public int? MediaFamilyId { get; set; }
        public int MediaId { get; set; }
        public string MediaName { get; set; }
        public Guid BrandId { get; set; }
        public string ProductName { get; set; }
        public string BrandName { get; set; }
        public DateTime FirstWeek { get; set; }
        public DateTime? LastInstallWeek { get; set; }
        public DateTime? LastBillableWeek { get; set; }
        public int InstallWeeks { get; set; }
        public int InstallStoreQty { get; set; }
        public int BillableStoreQty { get; set; }
        public decimal RentalRate { get; set; }
        public int BillableWeeks { get; set; }
        public decimal Discount { get; set; }
        public string InstallationInstructions { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public bool StoreListConfirmed { get; set; }
        public bool InstallAtHomesite { get; set; }
        public bool SecondaryPlacement { get; set; }
    }
}
