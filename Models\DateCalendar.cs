﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class DateCalendar
    {
        public DateTime CalendarDate { get; set; }
        public int CalendarYear { get; set; }
        public int CalendarMonth { get; set; }
        public int CalendarDay { get; set; }
        public int CalendarQuarter { get; set; }
        public DateTime FirstDayInWeek { get; set; }
        public DateTime LastDayInWeek { get; set; }
        public int IsWeekInSameMonth { get; set; }
        public DateTime FirstDayInMonth { get; set; }
        public DateTime LastDayInMonth { get; set; }
        public int IsLastDayInMonth { get; set; }
        public DateTime FirstDayInQuarter { get; set; }
        public DateTime LastDayInQuarter { get; set; }
        public int IsLastDayInQuarter { get; set; }
        public int DayOfWeek { get; set; }
        public int WeekOfMonth { get; set; }
        public int WeekOfQuarter { get; set; }
        public int WeekOfYear { get; set; }
        public int DaysInMonth { get; set; }
        public int MonthDaysRemaining { get; set; }
        public int WeekdaysInMonth { get; set; }
        public int MonthWeekdaysRemaining { get; set; }
        public int MonthWeekdaysCompleted { get; set; }
        public int DaysInQuarter { get; set; }
        public int QuarterDaysRemaining { get; set; }
        public int QuarterDaysCompleted { get; set; }
        public int WeekdaysInQuarter { get; set; }
        public int QuarterWeekdaysRemaining { get; set; }
        public int QuarterWeekdaysCompleted { get; set; }
        public int DayOfYear { get; set; }
        public int YearDaysRemaining { get; set; }
        public int IsWeekday { get; set; }
        public int IsLeapYear { get; set; }
        public string DayName { get; set; }
        public int MonthDayNameInstance { get; set; }
        public int QuarterDayNameInstance { get; set; }
        public int YearDayNameInstance { get; set; }
        public string MonthName { get; set; }
        public string YearWeek { get; set; }
        public string YearMonth { get; set; }
        public string YearQuarter { get; set; }
    }
}
