﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VanUser
    {
        public Guid VanId { get; set; }
        public Guid UserId { get; set; }
        public DateTime DateEffective { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual User1 User { get; set; }
        public virtual Van Van { get; set; }
    }
}
