﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ContractCostEstimate
    {
        public int CostEstimateId { get; set; }
        public Guid ContractId { get; set; }
        public string CostEstimateNumber { get; set; }
        public decimal CostEstimateAmount { get; set; }
        public int? MediaId { get; set; }

        public virtual Contract Contract { get; set; }
    }
}
