﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VanRegion
    {
        public VanRegion()
        {
            Vans = new HashSet<Van>();
        }

        public Guid VanRegionId { get; set; }
        public string VanRegionName { get; set; }
        public DateTime DateModified { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<Van> Vans { get; set; }
    }
}
