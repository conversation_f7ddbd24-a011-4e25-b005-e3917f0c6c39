﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class UpdateInstallationScheduleToHaveFirstOpenedAndFinishedDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "FinishedDate",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "OpenedDate",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FinishedDate",
                schema: "Ops",
                table: "InstallationScheduleCurrent");

            migrationBuilder.DropColumn(
                name: "OpenedDate",
                schema: "Ops",
                table: "InstallationScheduleCurrent");
        }
    }
}
