﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class SettingGroup
    {
        public SettingGroup()
        {
            Settings = new HashSet<Setting>();
        }

        public int SettingGroupId { get; set; }
        public string SettingGroupName { get; set; }

        public virtual ICollection<Setting> Settings { get; set; }
    }
}
