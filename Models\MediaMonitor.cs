﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Models
{
    public partial class MediaMonitor
    {
        public int Id { get; set; }
        public Guid ContractId { get; set; }
        public string? ImagePath { get; set; }
        public int? BalanceQty { get; set; }
        public int? ReceivedBackQty { get; set; }
        public int? ReceivedInitiallyQty { get; set; }
        public int? LostQty { get; set; }
        public string? RegionName { get; set; }
        public string? CampaignNote { get; set; }
        public string? DeliveryNote { get; set; }
        public DateTime? CreationDate { get; set; }
        public string? Chain { get; set; }
    }
}
