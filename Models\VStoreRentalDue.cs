﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VStoreRentalDue
    {
        public DateTime Week { get; set; }
        public string CategoryType { get; set; }
        public string Product { get; set; }
        public string Category { get; set; }
        public string Chain { get; set; }
        public string Region { get; set; }
        public string Media { get; set; }
        public string StoreDescription { get; set; }
        public int? Month { get; set; }
        public int? Year { get; set; }
        public string HeadOfficeName { get; set; }
        public decimal? HeadOfficeRentalDue { get; set; }
        public decimal? StoreRentalDue { get; set; }
        public decimal? TotalRentalDue { get; set; }
        public string StoreVendorNumber { get; set; }
        public string StoreGlcode { get; set; }
        public int ChainId { get; set; }
        public int HeadOfficeId { get; set; }
        public int StoreId { get; set; }
        public Guid ContractId { get; set; }
        public int MediaId { get; set; }
        public int CategoryId { get; set; }
        public bool Homesite { get; set; }
    }
}
