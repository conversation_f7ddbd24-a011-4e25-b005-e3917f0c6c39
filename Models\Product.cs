﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Product
    {
        public Product()
        {
            ContractProducts = new HashSet<ContractProduct>();
            DistributionCheckDetails = new HashSet<DistributionCheckDetail>();
            PurchaseDetails = new HashSet<PurchaseDetail>();
            SaleDetails = new HashSet<SaleDetail>();
            UnitsPerShrinks = new HashSet<UnitsPerShrink>();
        }

        public Guid ProductId { get; set; }
        public Guid BrandId { get; set; }
        public string ProductName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual Brand Brand { get; set; }
        public virtual ICollection<ContractProduct> ContractProducts { get; set; }
        public virtual ICollection<DistributionCheckDetail> DistributionCheckDetails { get; set; }
        public virtual ICollection<PurchaseDetail> PurchaseDetails { get; set; }
        public virtual ICollection<SaleDetail> SaleDetails { get; set; }
        public virtual ICollection<UnitsPerShrink> UnitsPerShrinks { get; set; }
    }
}
