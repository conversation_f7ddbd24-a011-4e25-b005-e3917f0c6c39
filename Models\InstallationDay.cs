﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationDays
    {
        public InstallationDays()
        {
            Stores = new HashSet<Store>();
        }
        [Key]
        public int InstallationDayId { get; set; }
        public string InstallationDay1 { get; set; }

        public virtual ICollection<Store> Stores { get; set; }
    }
}
