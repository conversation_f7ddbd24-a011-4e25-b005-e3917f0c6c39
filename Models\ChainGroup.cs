﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ChainGroup
    {
        public ChainGroup()
        {
            ChainGroupChains = new HashSet<ChainGroupChain>();
        }

        public int ChainGroupId { get; set; }
        public string ChainGroupName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<ChainGroupChain> ChainGroupChains { get; set; }
    }
}
