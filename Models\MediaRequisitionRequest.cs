﻿using PhoenixAPI.Services.EmailModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Models
{
    public class MediaRequisitionRequest
    {
        public int mediaRequestedQty { get; set; }
        public string regionRequestingMedia { get; set; }
        public string regionDistributingMedia { get; set; }
        public string campaign { get; set; }
        public string mediaType { get; set; }
        public string chain { get; set; }
        public string contractNumber { get; set; }
        public string contractId { get; set; }
        public Email Email { get; set; }
    }
}
