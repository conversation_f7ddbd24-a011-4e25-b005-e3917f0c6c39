﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaRule
    {
        public MediaRule()
        {
            MediaRuleRelationships = new HashSet<MediaRuleRelationship>();
        }

        public int Id { get; set; }
        public string Rule { get; set; }
        public string Description { get; set; }
        public bool? Dormant { get; set; }

        public virtual ICollection<MediaRuleRelationship> MediaRuleRelationships { get; set; }
    }
}
