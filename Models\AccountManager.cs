﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class AccountManager
    {
        public AccountManager()
        {
            AccountManagerBudgets = new HashSet<AccountManagerBudget>();
            AccountManagerPermissionUsers = new HashSet<AccountManagerPermissionUser>();
            BrandAccountManagers = new HashSet<BrandAccountManager>();
            ClientAccountManagers = new HashSet<ClientAccountManager>();
            Contracts = new HashSet<Contract>();
        }

        public int AccountManagerId { get; set; }
        public int PrincipalId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Code { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<AccountManagerBudget> AccountManagerBudgets { get; set; }
        public virtual ICollection<AccountManagerPermissionUser> AccountManagerPermissionUsers { get; set; }
        public virtual ICollection<BrandAccountManager> BrandAccountManagers { get; set; }
        public virtual ICollection<ClientAccountManager> ClientAccountManagers { get; set; }
        public virtual ICollection<Contract> Contracts { get; set; }
    }
}
