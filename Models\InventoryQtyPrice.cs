﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryQtyPrice
    {
        public int ItemQtyPriceId { get; set; }
        public int ItemQtyId { get; set; }
        public DateTime EffectiveDate { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SellPrice { get; set; }

        public virtual InventoryQty ItemQty { get; set; }
    }
}
