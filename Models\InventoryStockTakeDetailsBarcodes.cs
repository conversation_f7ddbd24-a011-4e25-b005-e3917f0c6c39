﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryStockTakeDetailsBarcodes
    {

        [Key]
        [Column("InventoryStockTakeDetailsBarcodesId")]
        public int InventoryStockTakeDetailsBarcodesId { get; set; }
        public int InventoryStockTakeDetailId { get; set; }

        public int? WarehouseId { get; set; }
        public int? InstallationTeamId { get; set; }
          
        public string userName { get; set; }

        public string barcode { get; set; }

        public DateTime CreationDate { get; set; }
      
       
        public bool isWrongCapex { get; set; }
        public bool isNonExisting { get; set; }
        public int? MasterItemId { get; set; }
        public int? MasterItemInventoryId { get; set; }
        public int? ShelfId { get; set; }

        [ForeignKey(nameof(InventoryStockTakeDetailId))]
        public virtual InventoryStockTakeDetails InventoryStockTakeDetails { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        public virtual Warehouse Warehouse { get; set; }
        [ForeignKey(nameof(InstallationTeamId))]
        public virtual InstallationTeam InstallationTeam { get; set; }

        [ForeignKey(nameof(MasterItemId))]
        public virtual MasterItem MasterItem { get; set; }

        [ForeignKey(nameof(MasterItemInventoryId))]
        public virtual MasterItemInventoryItem MasterItemInventoryItem { get; set; }

        [ForeignKey(nameof(ShelfId))]
        public virtual Shelf Shelf { get; set; }
    }
}
