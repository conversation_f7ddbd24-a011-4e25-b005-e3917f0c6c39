﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class StorePool
    {
        public StorePool()
        {
            Bursts = new HashSet<Burst>();
        }

        public Guid StorePoolId { get; set; }
        public int StorePoolQty { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<Burst> Bursts { get; set; }
    }
}
