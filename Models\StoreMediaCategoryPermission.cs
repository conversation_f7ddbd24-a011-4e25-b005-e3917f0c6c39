﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class StoreMediaCategoryPermission
    {
        public int Id { get; set; }
        public int CategoryId { get; set; }
        public int StoreId { get; set; }
        public int MediaId { get; set; }
        public DateTime? CreatedDate { get; set; }

        public virtual Category1 Category { get; set; }
        public virtual Media Media { get; set; }
        public virtual Store Store { get; set; }
    }
}
