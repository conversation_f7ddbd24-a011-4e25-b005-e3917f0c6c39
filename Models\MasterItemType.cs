﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MasterItemType
    {
        public MasterItemType()
        {
            MasterItems = new HashSet<MasterItem>();
        }

        public int MasterItemTypeId { get; set; }
        public string MasterItemTypeName { get; set; }
        public string MasterItemTypeDescription { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<MasterItem> MasterItems { get; set; }
    }
}
