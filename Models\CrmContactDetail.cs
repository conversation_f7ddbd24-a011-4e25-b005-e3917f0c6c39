﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class CrmContactDetail
    {
        public int ContactDetailId { get; set; }
        public int? CrmClientId { get; set; }
        public int? AccountManagerId { get; set; }
        public int? ContactStatusId { get; set; }
        public DateTime? ContactDate { get; set; }
        public string Notes { get; set; }
        public int? ContactId { get; set; }
        public DateTime? CreatedDt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModDt { get; set; }
        public string ModBy { get; set; }
        public string Brand { get; set; }
        public int? MediaId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? WeeklyRate { get; set; }
        public int? BillableWeeks { get; set; }
        public string ProposalLikelyness { get; set; }
        public string ProposalStatus { get; set; }
        public string Country { get; set; }
        public string CancellationStatus { get; set; }
    }
}
