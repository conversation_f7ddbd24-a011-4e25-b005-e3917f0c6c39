﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Brand
    {
        public Brand()
        {
            BrandAccountManagers = new HashSet<BrandAccountManager>();
            BrandCategoryMembers = new HashSet<BrandCategoryMember>();
            BrandFamilyMembers = new HashSet<BrandFamilyMember>();
            Bursts = new HashSet<Burst>();
            ClientBrands = new HashSet<ClientBrand>();
            ContractInventoryQties = new HashSet<ContractInventoryQty>();
            Products = new HashSet<Product>();
            ProvisionalBookings = new HashSet<ProvisionalBooking>();
        }

        public Guid BrandId { get; set; }
        public string BrandName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<BrandAccountManager> BrandAccountManagers { get; set; }
        public virtual ICollection<BrandCategoryMember> BrandCategoryMembers { get; set; }
        public virtual ICollection<BrandFamilyMember> BrandFamilyMembers { get; set; }
        public virtual ICollection<Burst> Bursts { get; set; }
        public virtual ICollection<ClientBrand> ClientBrands { get; set; }
        public virtual ICollection<ContractInventoryQty> ContractInventoryQties { get; set; }
        public virtual ICollection<Product> Products { get; set; }
        public virtual ICollection<ProvisionalBooking> ProvisionalBookings { get; set; }
    }
}
