{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Email": {
    "Port": 25,
    "Host": "za-smtp-outbound-1.mimecast.co.za",
    "Username": "<EMAIL>",
    "Password": "wSay42-mN8W7",
    "Sender": "<EMAIL>",
    //If working locally replace Url with localhost
    //"Url": "http://localhost:4200/MediaMonitor/MediaRequisition/RequestForm"
    "Url": "https://phoenixtest.primeinstore.co.za/MediaMonitor/MediaRequisition/RequestForm"

  },
  "ConnectionStrings": {
    //"NovaDB": "Server=**********;Initial Catalog=NovaDB;Persist Security Info=True;User ID=sa;Password=************"
    "NovaDB": "Server=************\\LIVE;Initial Catalog=NovaDB;Persist Security Info=True;User ID=SSIS;Password=*****************$&*"
  },
  "AllowedHosts": "*",
  "JwtKey": "superSecretKey@345",
  // "JwtIssuer": "http://localhost:44301",
  "JwtIssuer": "http://************:7798",
  "JwtExpireDays": 30
}
