﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Category1
    {
        public Category1()
        {
            BurstCategories = new HashSet<BurstCategory>();
            InstallationSchedules = new HashSet<InstallationSchedule>();
            MediaCategories = new HashSet<MediaCategory>();
            ProvisionalBookings = new HashSet<ProvisionalBooking>();
            ResearchCategories = new HashSet<ResearchCategory>();
            StoreMediaCategoryPermissions = new HashSet<StoreMediaCategoryPermission>();
        }

        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual ICollection<BurstCategory> BurstCategories { get; set; }
        public virtual ICollection<InstallationSchedule> InstallationSchedules { get; set; }
        public virtual ICollection<MediaCategory> MediaCategories { get; set; }
        public virtual ICollection<ProvisionalBooking> ProvisionalBookings { get; set; }
        public virtual ICollection<ResearchCategory> ResearchCategories { get; set; }
        public virtual ICollection<StoreMediaCategoryPermission> StoreMediaCategoryPermissions { get; set; }
    }
}
