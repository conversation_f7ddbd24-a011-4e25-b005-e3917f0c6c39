﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationScheduleCurrent
    {

        [Key]
        [Column("InstallationScheduleCurrentID")]
        public Guid InstallationScheduleCurrentID { get; set; }
        [Required]
        [StringLength(6)]
        public string Group { get; set; }
        [Column("Day of Commencement Date")]
        [StringLength(8000)]
        public string DayOfCommencementDate { get; set; }
        [Column("Day of Last modified")]
        public int? DayOfLastModified { get; set; }
        [Column("Day of Termination date")]
        [StringLength(8000)]
        public string DayOfTerminationDate { get; set; }
        [Column("Quarter of Commencement date")]
        public int? QuarterOfCommencementDate { get; set; }
        [Column("Month of Commencement date")]
        public int? MonthOfCommencementDate { get; set; }
        [Column("Week of Commencement date")]
        public int? WeekOfCommencementDate { get; set; }
        [Column("Quarter of Last modified")]
        public int? QuarterOfLastModified { get; set; }
        [Column("Month of Last modified")]
        public int? MonthOfLastModified { get; set; }
        [Column("Week of Last modified")]
        public int? WeekOfLastModified { get; set; }
        [Column("Quarter of Termination date")]
        public int? QuarterOfTerminationDate { get; set; }
        [Column("Month of Termination date")]
        public int? MonthOfTerminationDate { get; set; }
        [Column("Week of Termination date")]
        public int? WeekOfTerminationDate { get; set; }
        [Column("Year of Commencement date")]
        public int? YearOfCommencementDate { get; set; }
        [Column("Year of Last modified")]
        public int? YearOfLastModified { get; set; }
        [Column("Year of Termination date")]
        public int? YearOfTerminationDate { get; set; }
        [Required]
        [Column("Job Number")]
        [StringLength(8)]
        public string JobNumber { get; set; }
        [Column("Special Instructions")]
        [StringLength(8000)]
        public string SpecialInstructions { get; set; }
        public int? Cycle { get; set; }
        [Column("Number of weeks")]
        public int? NumberOfWeeks { get; set; }
        [Column("Commencement Date")]
        public int? CommencementDate { get; set; }
        [Column("Termination Date")]
        public int? TerminationDate { get; set; }
        [Required]
        [StringLength(250)]
        public string Store { get; set; }
        [Required]
        [StringLength(250)]
        public string Region { get; set; }
        [Required]
        [StringLength(200)]
        public string Product { get; set; }
        [Required]
        [Column("Media Type")]
        [StringLength(213)]
        public string MediaType { get; set; }
        [Required]
        [StringLength(150)]
        public string Client { get; set; }
        [Required]
        [Column("Category Name")]
        [StringLength(200)]
        public string CategoryName { get; set; }
        [Column("Last Modified")]
        [StringLength(19)]
        public string LastModified { get; set; }

        public DateTime OpenedDate { get; set; }

        public DateTime FinishedDate { get; set; }
        [Required]
        [StringLength(150)]
        public string Chain { get; set; }
        public int? Quantity { get; set; }
        [Column("Last modified by")]
        public int? LastModifiedBy { get; set; }
        [Required]
        [StringLength(50)]
        public string Status { get; set; }
        [Column("Store and Chain")]
        public int? StoreAndChain { get; set; }
        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }
        public int? Section { get; set; }
        [StringLength(12)]
        public string ForDate { get; set; }
        [Column("sto_id")]
        public int StoId { get; set; }
        [Column("teamlist")]
        public int? Teamlist { get; set; }
        public int QtyToInstall { get; set; }

        public string InstallationDay { get; set; }

        [Column("IRCodeComment")]
        public string IRCodeComment { get; set; }

        //add new columns here
        [Column(TypeName = "datetime")]
        public DateTime? GeneratedScheduleDate { get; set; }

        public bool CampaignSpecialInstructionsRead { get; set; }
        public bool CampaignPictureTaken { get; set; }
        public bool CampaignIRCodeSelected { get; set; }

        public bool CampaignFinished { get; set; }

        public string CampaignPicturePath { get; set; }
        public int IRCodeID { get; set; }

        public int? PreviousIRCodeId { get; set; }

        public string PreviousIRCodeComment { get; set; }

        public string SalesProblemMessage { get; set; }

        [ForeignKey(nameof(IRCodeID))]
        public virtual IRCodes SelectedIRCode { get; set; } = new IRCodes { };

        [ForeignKey(nameof(PreviousIRCodeId))]
        public virtual IRCodes PreviousIRCode { get; set; } = new IRCodes { };
        public int MediaId { get; set; }

        [ForeignKey(nameof(MediaId))]
        public virtual Media Media { get; set; }

        public int Installationteamid { get; set; }
        [ForeignKey(nameof(Installationteamid))]
        public virtual InstallationTeam InstallationTeam { get; set; }


        public bool IsCurrent { get; set; }

        public bool AllowPictureAfterwards { get; set; }

        public string imageURL { get; set; }

        public ICollection<InstallationScheduleQuestionsAndAnswers > InstallationScheduleQuestionsAndAnswers { get; set; }

        //[ForeignKey(nameof(IRCodeID))]


        //    CampaignSpecialInstructionsRead: boolean;
        //CampaignPictureTaken: boolean;
        //CampaignIRCodeSelected: boolean;


    }
}
