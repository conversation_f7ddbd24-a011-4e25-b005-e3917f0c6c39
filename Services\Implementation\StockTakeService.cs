﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using PhoenixAPI.Configurations;
using PhoenixAPI.Models;
using PhoenixAPI.Services.EmailModel;
using PhoenixAPI.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Services.Implementation
{
    public class StockTakeService : IStockTakeService
    {
        private readonly NovaDBContext _context;
        private readonly ConnectionConfiguration _configuration;
        public StockTakeService(NovaDBContext context, IOptions<ConnectionConfiguration> configuration)
        {
            _context = context;
            _configuration = configuration.Value;
        }
        public async Task<MasterItemInventoryItem> CreateMasterInventoryItemAsync(MasterItemInventoryItem item, string user)
        {
            var model = new MasterItemInventoryItem
            {
                Id = item.Id,
                MasterItemId = item.MasterItemId,
                Barcode = item.Barcode,
                CreatedBy = user,
                CreationDate = System.DateTime.Now,
                Dormant = false,
                InstallationTeamId = item.InstallationTeamId != null ? item.InstallationTeamId : null,
                StoreId = item.StoreId != null ? item.StoreId : null,
                WarehouseId = item.WarehouseId != null ? item.WarehouseId : null,
                ShelfId = item.ShelfId != null ? item.ShelfId : null
            };

            _context.MasterItemInventoryItems.Add(model);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return model;
        }

        public async Task<IActionResult> CreateStockTakeAsync(InventoryStockTake inventoryStockTake, string user)
        {
            var model = new InventoryStockTake
            {
                InventoryStockTakeId = inventoryStockTake.InventoryStockTakeId,
                StockTakeName = inventoryStockTake.StockTakeName,
                StartDate = inventoryStockTake.StartDate,
                EndDate = inventoryStockTake.EndDate,
                CreatedBy = user,
                CreationDate = System.DateTime.Now,
                isCompleted = false
            };

            if (_context.InventoryStockTake.Where(x => x.isCompleted != true).FirstOrDefault() != null)
            {
                return new JsonResult(new { error = "Cannot create a stock take if there is already one in progress."});
            }
            else
            {
                await _context.InventoryStockTake.AddAsync(model);
                _context.SaveChanges();

                return new OkResult();
            }
        }

        public async Task<IActionResult> UpdateMasterItemAsync(string barcode, int masterItemId)
        {
            var model = await _context.MasterItemInventoryItems.Where(s => s.Barcode == barcode).FirstOrDefaultAsync();

            if (model != null)
            {
                model.MasterItemId = masterItemId;

                _context.MasterItemInventoryItems.Update(model);
                _context.SaveChanges();
            }

            return new OkResult();
        }

        //public object UpdateMasterItemLocationAsync(MasterItemLocation model)
        //{
        //    var query = "UPDATE Ops.MasterItemInventoryItem SET WarehouseId=@WarehouseId, InstallationTeamId=@InstallationTeamId, StoreId=@StoreId WHERE Barcode =@Barcode";

        //    try {
        //        using (SqlConnection conn = new SqlConnection(_configuration.NovaDB))
        //        {
        //            SqlDataAdapter adapter = new SqlDataAdapter();
        //            SqlCommand sqlCmd = new SqlCommand(query, conn);
        //            sqlCmd.CommandType = CommandType.Text;

        //            if (model.WarehouseId != null)
        //            {
        //                sqlCmd.Parameters.Add("@WarehouseId", SqlDbType.Int).Value = model.WarehouseId;
        //                sqlCmd.Parameters.Add("@InstallationTeamId", SqlDbType.Int).Value = DBNull.Value;
        //                sqlCmd.Parameters.Add("@StoreId", SqlDbType.Int).Value = DBNull.Value;
        //                sqlCmd.Parameters.Add("@Barcode", SqlDbType.NVarChar, 20).Value = model.Barcode;            
        //            } else
        //            {
        //                sqlCmd.Parameters.Add("@WarehouseId", SqlDbType.Int).Value = DBNull.Value;
        //                sqlCmd.Parameters.Add("@InstallationTeamId", SqlDbType.Int).Value = model.InstallationTeamId;
        //                sqlCmd.Parameters.Add("@StoreId", SqlDbType.Int).Value = DBNull.Value;
        //                sqlCmd.Parameters.Add("@Barcode", SqlDbType.NVarChar, 20).Value = model.Barcode;
        //            }

        //            conn.Open();
        //            SqlDataReader reader = sqlCmd.ExecuteReader();
        //            adapter.UpdateCommand = sqlCmd;
        //            conn.Close();

        //            return new OkResult();
        //        }
        //    } catch (Exception ex) {
        //        return ex.Message;
        //    }
        //}

        public async Task<IActionResult> UpdateMasterItemOnStockTakeDetailsBarcodesAsync(string barcode, int masterItemId, int inventoryStockTakeDetailId)
        {
            var model = await _context.InventoryStockTakeDetailsBarcodes.Where(s => s.barcode == barcode && s.InventoryStockTakeDetailId == inventoryStockTakeDetailId).FirstOrDefaultAsync();

            if (model != null)
            {
                model.MasterItemId = masterItemId;

                _context.InventoryStockTakeDetailsBarcodes.Update(model);
                _context.SaveChanges();
            }

            return new OkResult();
        }

        public async Task<IActionResult> StockTakeInProgress()
        {
            var result = await _context.InventoryStockTake.Where(x => x.isCompleted == false && x.EndDate >= System.DateTime.Now).FirstOrDefaultAsync();

            return result != null ? new JsonResult(result) : new JsonResult("No stock take is currently open!");
        }

        public async Task<IActionResult> CompleteStockTake(int stockTakeId)
        {
            var result = await _context.InventoryStockTake.Where(x => x.InventoryStockTakeId == stockTakeId).FirstOrDefaultAsync();

            if (result != null)
            {
                result.isCompleted = true;

                _context.InventoryStockTake.Update(result);
                _context.SaveChanges();
            }

            return new OkResult();
        }
    }
}
