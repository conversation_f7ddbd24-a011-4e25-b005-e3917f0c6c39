﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaLifeCycle
    {
        public int MediaId { get; set; }
        public DateTime FirstWeek { get; set; }
        public DateTime LastWeek { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Media Media { get; set; }
    }
}
