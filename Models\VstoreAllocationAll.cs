﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VstoreAllocationAll
    {
        public string StoreType { get; set; }
        public string PnpstoreCode { get; set; }
        public string StoreName { get; set; }
        public string PnpRegion { get; set; }
        public string NovaHeadOffice { get; set; }
        public string NovaRegion { get; set; }
        public string NovaCity { get; set; }
        public string NovaName { get; set; }
        public string NovaAddress { get; set; }
        public string NovaNumber { get; set; }
        public string Chain { get; set; }
        public int? StoreId { get; set; }
        public int? NovaId { get; set; }
        public string StoreStatus { get; set; }
        public string StoreType1 { get; set; }
        public string Chain2 { get; set; }
        public int? StoreIdLiqour { get; set; }
        public string NovaRegion2 { get; set; }
        public string NovaName2 { get; set; }
        public string StoreStatus2 { get; set; }
        public string StoreType2 { get; set; }
        public string NovaNumber2 { get; set; }
        public string NovaAddress2 { get; set; }
        public string NovaCity2 { get; set; }
    }
}
