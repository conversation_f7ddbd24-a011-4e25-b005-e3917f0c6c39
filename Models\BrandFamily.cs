﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BrandFamily
    {
        public BrandFamily()
        {
            BrandFamilyMembers = new HashSet<BrandFamilyMember>();
        }

        public int BrandFamilyId { get; set; }
        public string BrandFamilyName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<BrandFamilyMember> BrandFamilyMembers { get; set; }
    }
}
