﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Models;
using PhoenixAPI.Models.IncomingMedia;
using PhoenixAPI.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static PhoenixAPI.Controllers.OpsManagementController;

namespace PhoenixAPI.Services.Implementation
{
    public class MediaMonitorService : IMediaMonitorService
    {
        private readonly NovaDBContext _context;
        private static string pathForDocuments = @"\\192.168.0.16\Images\Media-Monitor\DeliveryNotes\";
        private static string pathForImages = @"\\192.168.0.16\Images\Media-Monitor\";

        public MediaMonitorService(NovaDBContext context)
        {
            _context = context;
        }
        public async Task<object> AcknowledgeStatus(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised)
        {
            var checkExistingMedia = await _context.IncomingMediaPerRegionRevised
                 .Where(s => s.Id == incomingMediaPerRegionRevised.Id &&
                 s.ContractId == incomingMediaPerRegionRevised.ContractId &&
                 s.RegionName == incomingMediaPerRegionRevised.RegionName
                 && s.Chain == incomingMediaPerRegionRevised.Chain).FirstOrDefaultAsync();

            var model = new IncomingMediaPerRegionRevised()
            {
                Id = incomingMediaPerRegionRevised.Id,
                ContractId = incomingMediaPerRegionRevised.ContractId,
                ContractNumber = incomingMediaPerRegionRevised.ContractNumber,
                Campaign = incomingMediaPerRegionRevised.Campaign,
                Chain = incomingMediaPerRegionRevised.Chain,
                MediaType = incomingMediaPerRegionRevised.MediaType,
                RegionName = incomingMediaPerRegionRevised.RegionName,
                IsMediaReceivedByRequestedRegion = incomingMediaPerRegionRevised.IsMediaReceivedByRequestedRegion
            };

            if (checkExistingMedia != null)
            {
                checkExistingMedia.IsMediaReceivedByRequestedRegion = incomingMediaPerRegionRevised.IsMediaReceivedByRequestedRegion;

                _context.IncomingMediaPerRegionRevised.Update(checkExistingMedia);
                _context.SaveChanges();
            }
            else
            {
                _context.IncomingMediaPerRegionRevised.Add(model);
                _context.SaveChanges();
            }

            return new OkResult();
        }
        public async Task<object> CaptureMediaMonitor(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised)
        {
            var checkExistingMedia = await _context.IncomingMediaPerRegionRevised.Where(s => s.Id == incomingMediaPerRegionRevised.Id && s.ContractId == incomingMediaPerRegionRevised.ContractId && s.RegionName == incomingMediaPerRegionRevised.RegionName).FirstOrDefaultAsync();

            var model = new IncomingMediaPerRegionRevised()
            {
                Id = incomingMediaPerRegionRevised.Id,
                ContractId = incomingMediaPerRegionRevised.ContractId,
                ContractNumber = incomingMediaPerRegionRevised.ContractNumber,
                Campaign = incomingMediaPerRegionRevised.Campaign,
                Chain = incomingMediaPerRegionRevised.Chain,
                MediaType = incomingMediaPerRegionRevised.MediaType,
                RegionName = incomingMediaPerRegionRevised.RegionName,
                AvailableMediaQty = incomingMediaPerRegionRevised.AvailableMediaQty != null ? incomingMediaPerRegionRevised.AvailableMediaQty : 0,
                ClosingBalanceQty = incomingMediaPerRegionRevised.ClosingBalanceQty != null ? incomingMediaPerRegionRevised.ClosingBalanceQty : 0,
                MediaRequiredQty = incomingMediaPerRegionRevised.MediaRequiredQty != null ? incomingMediaPerRegionRevised.MediaRequiredQty : 0,
                MediaRequestedQty = incomingMediaPerRegionRevised.MediaRequestedQty != null ? incomingMediaPerRegionRevised.MediaRequestedQty : 0,
                QuantityReceived = incomingMediaPerRegionRevised.QuantityReceived != null ? incomingMediaPerRegionRevised.QuantityReceived : 0,
                MediaDistributedQty = incomingMediaPerRegionRevised.MediaDistributedQty != null ? incomingMediaPerRegionRevised.MediaDistributedQty : 0,
                RegionReceivingMediaRequest = incomingMediaPerRegionRevised.RegionReceivingMediaRequest != null ? incomingMediaPerRegionRevised.RegionReceivingMediaRequest : null,
                DeliveryNote = incomingMediaPerRegionRevised.DeliveryNote != null ? uploadDeliveryNote(incomingMediaPerRegionRevised.DeliveryNote) : null,
                IsMediaReceivedByRequestedRegion = incomingMediaPerRegionRevised.IsMediaReceivedByRequestedRegion
            };

            if (checkExistingMedia != null)
            {
                checkExistingMedia.AvailableMediaQty = incomingMediaPerRegionRevised.AvailableMediaQty != null ? incomingMediaPerRegionRevised.AvailableMediaQty : checkExistingMedia.AvailableMediaQty;
                checkExistingMedia.QuantityReceived = incomingMediaPerRegionRevised.QuantityReceived != null ? incomingMediaPerRegionRevised.QuantityReceived : checkExistingMedia.QuantityReceived;
                checkExistingMedia.RegionReceivingMediaRequest = incomingMediaPerRegionRevised.RegionReceivingMediaRequest != null ? incomingMediaPerRegionRevised.RegionReceivingMediaRequest : checkExistingMedia.RegionReceivingMediaRequest;
                checkExistingMedia.RegionName = incomingMediaPerRegionRevised.RegionName != null ? incomingMediaPerRegionRevised.RegionName : checkExistingMedia.RegionName;
                checkExistingMedia.MediaRequestedQty = incomingMediaPerRegionRevised.MediaRequestedQty != null ? incomingMediaPerRegionRevised.MediaRequestedQty : checkExistingMedia.MediaRequestedQty;
                checkExistingMedia.MediaDistributedQty = incomingMediaPerRegionRevised.MediaDistributedQty != null ? incomingMediaPerRegionRevised.MediaDistributedQty : checkExistingMedia.MediaDistributedQty;
                checkExistingMedia.ClosingBalanceQty = incomingMediaPerRegionRevised.ClosingBalanceQty != null ? incomingMediaPerRegionRevised.ClosingBalanceQty : checkExistingMedia.ClosingBalanceQty;
                checkExistingMedia.DeliveryNote = incomingMediaPerRegionRevised.DeliveryNote != null ? uploadDeliveryNote(incomingMediaPerRegionRevised.DeliveryNote) : checkExistingMedia.DeliveryNote;
                checkExistingMedia.IsMediaReceivedByRequestedRegion = incomingMediaPerRegionRevised.IsMediaReceivedByRequestedRegion;

                _context.IncomingMediaPerRegionRevised.Update(checkExistingMedia);
                await _context.SaveChangesAsync();

                return new JsonResult(checkExistingMedia);
            }
            else
            {
                _context.IncomingMediaPerRegionRevised.Add(model);
                await _context.SaveChangesAsync();

                return new JsonResult(model);
            }
        }
        public async Task<object> GetContractNumber(string contractId)
        {
            var checkIfContractExist = await _context.Contract.Where(x => x.ContractId.ToString() == contractId).FirstOrDefaultAsync();

            if (checkIfContractExist == null)
            {
                return new JsonResult(new { error = "The contract id provided does not exist." });
            }

            return checkIfContractExist;
        }
        public async Task<object> DistributeMedia(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised)
        {
            var checkExistingMedia = await _context.IncomingMediaPerRegionRevised.Where(s => s.Id == incomingMediaPerRegionRevised.Id && s.ContractId == incomingMediaPerRegionRevised.ContractId && s.RegionName == incomingMediaPerRegionRevised.RegionName).FirstOrDefaultAsync();

            var model = new IncomingMediaPerRegionRevised()
            {
                Id = incomingMediaPerRegionRevised.Id,
                ContractId = incomingMediaPerRegionRevised.ContractId,
                ContractNumber = incomingMediaPerRegionRevised.ContractNumber,
                Campaign = incomingMediaPerRegionRevised.Campaign,
                Comment = incomingMediaPerRegionRevised.Comment,
                MediaType = incomingMediaPerRegionRevised.MediaType,
                RegionName = incomingMediaPerRegionRevised.RegionName,
                MediaRequestedQty = incomingMediaPerRegionRevised.MediaRequestedQty != null ? incomingMediaPerRegionRevised.MediaRequestedQty : 0,
                MediaDistributedQty = incomingMediaPerRegionRevised.MediaDistributedQty != null ? incomingMediaPerRegionRevised.MediaDistributedQty : 0,
                RegionReceivingMediaRequest = incomingMediaPerRegionRevised.RegionReceivingMediaRequest != null ? incomingMediaPerRegionRevised.RegionReceivingMediaRequest : null,
                DistributedBy = incomingMediaPerRegionRevised.DistributedBy != null ? incomingMediaPerRegionRevised.DistributedBy : null,
                DistributionDate = ConvertStringToDatetime(DateTime.Today),
                DeliveryNote = incomingMediaPerRegionRevised.DeliveryNote != null ? uploadDeliveryNote(incomingMediaPerRegionRevised.DeliveryNote) : null
            };

            if (checkExistingMedia != null)
            {
                checkExistingMedia.Comment = incomingMediaPerRegionRevised.Comment;
                checkExistingMedia.RegionReceivingMediaRequest = incomingMediaPerRegionRevised.RegionReceivingMediaRequest;
                checkExistingMedia.RegionName = incomingMediaPerRegionRevised.RegionName;
                checkExistingMedia.MediaRequestedQty = incomingMediaPerRegionRevised.MediaRequestedQty;
                checkExistingMedia.MediaDistributedQty = incomingMediaPerRegionRevised.MediaDistributedQty;
                checkExistingMedia.DistributedBy = incomingMediaPerRegionRevised.DistributedBy;
                checkExistingMedia.DistributionDate = ConvertStringToDatetime(DateTime.Today);
                checkExistingMedia.DeliveryNote = incomingMediaPerRegionRevised.DeliveryNote != null ? uploadDeliveryNote(incomingMediaPerRegionRevised.DeliveryNote) : checkExistingMedia.DeliveryNote;

                _context.IncomingMediaPerRegionRevised.Update(checkExistingMedia);
                await _context.SaveChangesAsync();

                return new JsonResult(checkExistingMedia);
            }
            else
            {
                _context.IncomingMediaPerRegionRevised.Add(model);
                await _context.SaveChangesAsync();

                return new JsonResult(model);
            }
        }
        public async Task<object> GetBursts()
        {
            return await _context.Bursts.Where(x => x.FirstWeek >= System.DateTime.Now.AddMonths(-3) || x.FirstWeek >= System.DateTime.Now)
                                        .Select(s => new { BurstId = s.BurstId, CreationDate = s.FirstWeek, ContractId = s.ContractID })
                                        .ToListAsync();
        }
        public async Task<object> GetIncomingMediaRevised()
        {
            var results = await _context.Contract.Where(x => x.Signed == true && x.Cancelled == false).Include(burst => burst.Burst).ThenInclude(burstCategory => burstCategory.BurstCategories).ThenInclude(category => category.Category)
                        .Include(contractapproved => contractapproved.ContractApproved)
                        .Include(storelist => storelist.Burst.StoreLists)
                        .Where(x => x.Signed == true && x.ContractApproved.Approved == true && x.Cancelled == false && (x.Burst.FirstWeek >= System.DateTime.Now.AddMonths(-3) || x.Burst.FirstWeek >= System.DateTime.Now))
                        .Select(x => new { x.ContractId, x.ContractNumber, MediaType = x.Burst.MediaName, MediaId = x.Burst.MediaId, Chain = x.Burst.ChainName, Campaign = x.Burst.ProductName, creationDate = x.ContractDate, TotalAdsInstallationQty = (x.Burst.AdsPerCrossover * x.Burst.InstallStoreQty) + (x.Burst.InstallStoreQty * x.Burst.AdsPerInstallation), storelist = ((x.Burst.StoreLists.Count * x.Burst.AdsPerCrossover) + (x.Burst.StoreLists.Count * x.Burst.AdsPerInstallation)) }).ToListAsync();

            var resultWithStoreList = results.GroupBy(x => new { x.Campaign, x.ContractId, x.ContractNumber, x.creationDate, x.MediaType, x.Chain, x.MediaId }).Select(x => new { x.Key.Campaign, x.Key.ContractId, x.Key.ContractNumber, x.Key.creationDate, x.Key.Chain, x.Key.MediaId, x.Key.MediaType, TotalAdsInstallationQty = x.Sum(s => s.TotalAdsInstallationQty), storelistcount = x.Sum(sl => sl.storelist) });

            return resultWithStoreList.Where(x => x.TotalAdsInstallationQty > 0 && x.storelistcount > 0 && !x.MediaType.Contains("Unapproved"));
        }
        public async Task<object> GetMediaRelationship()
        {
            return await _context.MediaRuleRelationships.Include(r => r.Media).Where(s => s.MediaRuleId == 1).ToListAsync();
        }

        public async Task<object> GetMediaMonitorRegions() 
        {
            return await _context.OpsMediaMonitorRegions.ToListAsync();
        }
        public async Task<object> GetIncomingMediaRevisedByID(Guid contractID, string chain)
        {
            string rawSql = "select Cast(ROW_NUMBER() over (order by r.regionName) as Int) as  Id, c.contractId,b.productName as Campaign, b.ChainName as Chain, c.ContractNumber ,c.CreationDate,b.MediaName as MediaType, count(r.regionName) as BalanceQty, r.RegionName, 'a' as DeliveryNote, 0 as ReceivedInitiallyQty,Cast(ROW_NUMBER() over (order by r.regionName) as Int) as regionId from sales.Contract c "
                                        + "inner join sales.ContractApproved ca on c.ContractID = ca.contractid "
                                        + "inner join sales.Burst b on b.ContractID = c.ContractID "
                                        + "inner join sales.StoreList sl on sl.BurstID = b.BurstID "
                                        + "inner join store.Store s on s.StoreID = sl.StoreID "
                                        + "inner join store.Region r on r.RegionID = s.RegionID "
                                        + "where c.Signed = 1 and c.Cancelled = 0 and c.ContractID = '" + contractID + "' and b.ChainName = '" + chain + "' and b.FirstWeek >= dateadd(month,-3, getdate())"
                                        + "group by c.ContractNumber,r.RegionName,c.contractId,c.CreationDate,b.MediaName,b.productName, b.ChainName";
            var testResults = _context.IncomingMediaPerRegions.FromSqlRaw(rawSql);

            List<IncomingMediaResult> lstRegions = new List<IncomingMediaResult>();
            foreach (var result in testResults)
            {
                IncomingMediaResult regionMediaResult = new IncomingMediaResult();
                regionMediaResult.Campaign = result.Campaign;
                regionMediaResult.ContractId = result.ContractId.ToString();
                regionMediaResult.ContractNumber = result.ContractNumber;
                regionMediaResult.MediaType = result.MediaType;
                regionMediaResult.StoreListQty = 0;
                regionMediaResult.MediaRequiredQty = Convert.ToInt32(result.BalanceQty);
                regionMediaResult.Region = result.RegionName;
                lstRegions.Add(regionMediaResult);
            }

            return lstRegions.ToList().OrderBy(x => x.Region).ThenBy(x => x.MediaType);
        }
        public async Task<object> GetIncomingMediaRevisedByWarehouse(string warehouse)
        {
            string rawSql = "select Cast(ROW_NUMBER() over (order by r.regionName) as Int) as  Id, c.contractId,b.productName as Campaign, b.ChainName as Chain, c.ContractNumber ,c.CreationDate,b.MediaName as MediaType, count(r.regionName) as BalanceQty, r.RegionName, 'a' as DeliveryNote, 0 as ReceivedInitiallyQty,Cast(ROW_NUMBER() over (order by r.regionName) as Int) as regionId from sales.Contract c "
                                        + "inner join sales.ContractApproved ca on c.ContractID = ca.contractid "
                                        + "inner join sales.Burst b on b.ContractID = c.ContractID "
                                        + "inner join sales.StoreList sl on sl.BurstID = b.BurstID "
                                        + "inner join store.Store s on s.StoreID = sl.StoreID "
                                        + "inner join store.Region r on r.RegionID = s.RegionID "
                                        + "where c.Signed = 1 and c.Cancelled = 0 and r.regionName = '" + warehouse + "' "
                                        + "and (b.FirstWeek >= GETDATE() or b.FirstWeek >= dateadd(Month,-3, getdate())) "
                                        + "group by c.ContractNumber,r.RegionName,c.contractId,c.CreationDate,b.MediaName,b.productName, b.ChainName";
            var testResults = _context.IncomingMediaPerRegions.FromSqlRaw(rawSql);

            List<IncomingMediaResult> lstRegions = new List<IncomingMediaResult>();
            foreach (var result in testResults)
            {
                IncomingMediaResult regionMediaResult = new IncomingMediaResult();
                regionMediaResult.Campaign = result.Campaign;
                regionMediaResult.ContractId = result.ContractId.ToString();
                regionMediaResult.ContractNumber = result.ContractNumber;
                regionMediaResult.MediaType = result.MediaType;
                regionMediaResult.StoreListQty = 0;
                regionMediaResult.MediaRequiredQty = Convert.ToInt32(result.BalanceQty);
                regionMediaResult.Region = result.RegionName;
                regionMediaResult.Chain = result.Chain;
                lstRegions.Add(regionMediaResult);
            }

            return lstRegions.ToList().OrderBy(x => x.Region).ThenBy(x => x.MediaType);
        }
        public async Task<object> GetIncomingMediaRevisedForNationalWarehouse()
        {
            string rawSql = "select Cast(ROW_NUMBER() over (order by r.regionName) as Int) as  Id, c.contractId,b.productName as Campaign,b.ChainName as Chain, c.ContractNumber ,c.CreationDate,b.MediaName as MediaType, count(r.regionName) as BalanceQty, r.RegionName, 'a' as DeliveryNote, 0 as ReceivedInitiallyQty,Cast(ROW_NUMBER() over (order by r.regionName) as Int) as regionId from sales.Contract c "
                                        + "inner join sales.ContractApproved ca on c.ContractID = ca.contractid "
                                        + "inner join sales.Burst b on b.ContractID = c.ContractID "
                                        + "inner join sales.StoreList sl on sl.BurstID = b.BurstID "
                                        + "inner join store.Store s on s.StoreID = sl.StoreID "
                                        + "inner join store.Region r on r.RegionID = s.RegionID "
                                        + "where c.Signed = 1 and c.Cancelled = 0 "
                                        + "and (b.FirstWeek >= GETDATE() or b.FirstWeek >= dateadd(Month,-3, getdate())) "
                                        + "group by c.ContractNumber,r.RegionName,c.contractId,c.CreationDate,b.MediaName,b.productName, b.ChainName";
            var testResults = _context.IncomingMediaPerRegions.FromSqlRaw(rawSql);

            List<IncomingMediaResult> lstRegions = new List<IncomingMediaResult>();
            foreach (var result in testResults)
            {
                IncomingMediaResult regionMediaResult = new IncomingMediaResult();
                regionMediaResult.Campaign = result.Campaign;
                regionMediaResult.ContractId = result.ContractId.ToString();
                regionMediaResult.ContractNumber = result.ContractNumber;
                regionMediaResult.MediaType = result.MediaType;
                regionMediaResult.StoreListQty = 0;
                regionMediaResult.MediaRequiredQty = Convert.ToInt32(result.BalanceQty);
                regionMediaResult.Region = result.RegionName;
                regionMediaResult.Chain = result.Chain;
                lstRegions.Add(regionMediaResult);
            }

            return lstRegions.ToList().OrderBy(x => x.Region).ThenBy(x => x.MediaType);
        }
        public async Task<object> GetIncomingMedia()
        {
            return await _context.IncomingMediaPerRegionRevised.ToListAsync();
        }
        public async Task<object> GetMediaMonitor()
        {
            return await _context.MediaMonitors.ToListAsync();
        }
        public string GetDocumentById(string document)
        {
            string[] files = Directory.GetFiles(pathForDocuments);

            var file = files.FirstOrDefault(s => s.Contains(document));

            var newFile = Path.GetFileName(file);

            return newFile.Substring(0, newFile.Length - 4);
        }
        public async Task<object> UpdateImageOrCampaignNote(MediaMonitor mediaMonitor)
        {
            var result = await _context.MediaMonitors.FirstOrDefaultAsync(s => s.Id == mediaMonitor.Id && s.ContractId == mediaMonitor.ContractId);

            if (result != null)
            {
                result.ImagePath = mediaMonitor.ImagePath != null ? uploadImage(mediaMonitor.ImagePath) : result.ImagePath;
                result.CampaignNote = mediaMonitor.CampaignNote != null ? mediaMonitor.CampaignNote : result.CampaignNote;

                _context.MediaMonitors.Update(result);
                _context.SaveChanges();

                return result;
            }

            return null;
        }
        public async Task<object> UploadImageOrCaptureCampaignNote(MediaMonitor mediaMonitor)
        {
            var model = new MediaMonitor()
            {
                Id = mediaMonitor.Id,
                ContractId = mediaMonitor.ContractId,
                ImagePath = uploadImage(mediaMonitor.ImagePath),
                CampaignNote = mediaMonitor.CampaignNote,
                Chain = mediaMonitor.Chain
            };

            _context.MediaMonitors.Add(model);
            await _context.SaveChangesAsync();

            return model;
        }
        private string uploadDeliveryNote(string baseFiles)
        {
            var fileName = Guid.NewGuid().ToString();

            if (string.IsNullOrEmpty(baseFiles))
            {
                return "File cannot be null";
            }

            if (baseFiles.Contains("data:application/pdf;base64,"))
            {
                byte[] pdf = Convert.FromBase64String(baseFiles.Replace("data:application/pdf;base64,", ""));
                var pdfFilePath = pathForDocuments + fileName + ".pdf";

                if (!System.IO.File.Exists(pdfFilePath))
                {
                    System.IO.File.WriteAllBytes(pdfFilePath, pdf);
                }
            }

            if (baseFiles.Contains("data:application/vnd.ms-excel,"))
            {
                byte[] excel = Convert.FromBase64String(baseFiles.Replace("data:application/vnd.ms-excel,", ""));
                var excelFilePath = pathForDocuments + fileName + (".xls");

                if (!System.IO.File.Exists(excelFilePath))
                {
                    System.IO.File.WriteAllBytes(excelFilePath, excel);
                }
            }

            if (baseFiles.Contains("data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"))
            {
                byte[] excel = Convert.FromBase64String(baseFiles.Replace("data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,", ""));
                var excelFilePath = pathForDocuments + fileName + (".xlsx");

                if (!System.IO.File.Exists(excelFilePath))
                {
                    System.IO.File.WriteAllBytes(excelFilePath, excel);
                }
            }

            if (baseFiles.Contains("data:text/plain;base64,"))
            {
                byte[] text = Convert.FromBase64String(baseFiles.Replace("data:text/plain;base64,", ""));
                var textFilePath = pathForDocuments + fileName + ".txt";

                if (!System.IO.File.Exists(textFilePath))
                {
                    System.IO.File.WriteAllBytes(textFilePath, text);
                }
            }

            if (baseFiles.Contains("data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,"))
            {
                byte[] word = Convert.FromBase64String(baseFiles.Replace("data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,", ""));
                var wordFilePath = pathForDocuments + fileName + ".docx";

                if (!System.IO.File.Exists(wordFilePath))
                {
                    System.IO.File.WriteAllBytes(wordFilePath, word);
                }
            }

            if (baseFiles.Contains("data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,"))
            {
                byte[] presentation = Convert.FromBase64String(baseFiles.Replace("data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,", ""));
                var presentationFilePath = pathForDocuments + fileName + ".pptx";

                if (!System.IO.File.Exists(presentationFilePath))
                {
                    System.IO.File.WriteAllBytes(presentationFilePath, presentation);
                }
            }

            return fileName;
        }
        private string uploadImage(string baseFiles)
        {
            if (string.IsNullOrEmpty(baseFiles))
            {
                return "";
            }

            byte[] bytes = Convert.FromBase64String(baseFiles.Replace("data:image/png;base64,", "").Replace("data:image/jpeg;base64,", "").Replace("data:text/html;base64,", ""));

            Image image;
            MemoryStream imageStream = new MemoryStream(bytes);
            image = Image.FromStream(imageStream);

            var fileFolder = Path.Combine(pathForImages);
            var fileName = Guid.NewGuid().ToString();
            var fullPath = Path.Combine(fileFolder, fileName);

            image.Save(fullPath + ".jpeg");

            return (fullPath + ".jpeg");
        }
        private string ConvertStringToDatetime(DateTime date)
        {
            return date.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}
