﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class HeadOffice
    {
        public HeadOffice()
        {
            Stores = new HashSet<Store>();
        }

        public int HeadOfficeId { get; set; }
        public string HeadOfficeName { get; set; }
        public bool? Approval { get; set; }
        public decimal PaymentPercent { get; set; }
        public decimal FixedPayment { get; set; }
        public string AccountNumber { get; set; }

        public virtual ICollection<Store> Stores { get; set; }
    }
}
