﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VwListofContract
    {
        public Guid ContractId { get; set; }
        public string ContractNumber { get; set; }
        public string ClientName { get; set; }
        public bool Signed { get; set; }
        public DateTime? SignDate { get; set; }
        public string SignedBy { get; set; }
        public string SpecialConditions { get; set; }
        public string ProjectName { get; set; }
        public bool Cancelled { get; set; }
        public DateTime? CancelDate { get; set; }
        public string CancelledBy { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string ContractNotes { get; set; }
        public bool RollForward { get; set; }
        public bool AddedValue { get; set; }
        public DateTime InstallDate { get; set; }
        public DateTime RemoveDate { get; set; }
        public string AccountManagerName { get; set; }
    }
}
