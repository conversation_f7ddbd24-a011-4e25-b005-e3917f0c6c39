﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models;
using PhoenixAPI.Models.IncomingMedia;
using PhoenixAPI.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Controllers.OperationsManagement
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Authorize(Roles = "Admin, NovaOpsUser, Storeman, MyMobilityUser")]
    [Route("api/OpsManagement/[controller]")]
    public class MediaMonitorController : ControllerBase
    {
        private readonly IMediaMonitorService _mediaMonitorService;
        public MediaMonitorController(IMediaMonitorService mediaMonitorService)
        {
            _mediaMonitorService = mediaMonitorService;
        }

        [Route("CaptureMediaMonitor")]
        [HttpPost]
        public async Task<object> CaptureMediaMonitor(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised)
        {
            try
            {
                return await _mediaMonitorService.CaptureMediaMonitor(incomingMediaPerRegionRevised).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("AcknowledgeStatus")]
        [HttpPost]
        public async Task<object> AcknowledgeStatus(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised)
        {
            try
            {
                return await _mediaMonitorService.AcknowledgeStatus(incomingMediaPerRegionRevised).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }


        [Route("GetMediaMonitorRegions")]
        [HttpGet]
        public async Task<object> GetMediaMonitorRegions()
        {
            try
            {
                return await _mediaMonitorService.GetMediaMonitorRegions().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetBursts")]
        [HttpGet]
        public async Task<object> GetBursts()
        {
            try
            {
                return await _mediaMonitorService.GetBursts().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetMediaRelationship")]
        [HttpGet]
        public async Task<object> GetMediaRelationship()
        {
            try
            {
                return await _mediaMonitorService.GetMediaRelationship().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetDocumentById")]
        [HttpGet]
        public async Task<object> GetDocumentById(string document)
        {
            try
            {
                return _mediaMonitorService.GetDocumentById(document);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetIncomingMediaRevised")]
        [HttpGet]
        public async Task<object> GetIncomingMediaRevised()
        {
            try
            {
                return await _mediaMonitorService.GetIncomingMediaRevised().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetIncomingMediaRevisedByID")]
        [HttpGet]
        public async Task<object> GetIncomingMediaRevisedByID(Guid contractID, string chain)
        {
            try
            {
                return await _mediaMonitorService.GetIncomingMediaRevisedByID(contractID, chain).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetIncomingMediaRevisedByWarehouse")]
        [HttpGet]
        public async Task<object> GetIncomingMediaRevisedByWarehouse(string warehouse)
        {
            try
            {
                return await _mediaMonitorService.GetIncomingMediaRevisedByWarehouse(warehouse).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetIncomingMediaRevisedForNationalWarehouse")]
        [HttpGet]
        public async Task<object> GetIncomingMediaRevisedForNationalWarehouse()
        {
            try
            {
                return await _mediaMonitorService.GetIncomingMediaRevisedForNationalWarehouse().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetIncomingMedia")]
        [HttpGet]
        public async Task<object> GetIncomingMedia()
        {
            try
            {
                return await _mediaMonitorService.GetIncomingMedia().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetMediaMonitor")]
        [HttpGet]
        public async Task<object> GetMediaMonitor()
        {
            try
            {
                return await _mediaMonitorService.GetMediaMonitor().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("DistributeMedia")]
        [HttpPost]
        public async Task<object> DistributeMedia(IncomingMediaPerRegionRevised incomingMediaPerRegionRevised)
        {
            try
            {
                return await _mediaMonitorService.DistributeMedia(incomingMediaPerRegionRevised).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetContractNumber")]
        [HttpGet]
        public async Task<object> GetContractNumber(string contractId)
        {
            try
            {
                return await _mediaMonitorService.GetContractNumber(contractId).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("UpdateImageOrCampaignNote")]
        [HttpPost]
        public async Task<object> UpdateImageOrCampaignNote(MediaMonitor mediaMonitor)
        {
            try
            {
                return await _mediaMonitorService.UpdateImageOrCampaignNote(mediaMonitor);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("UploadImageOrCaptureCampaignNote")]
        [HttpPost]
        public async Task<object> UploadImageOrCaptureCampaignNote(MediaMonitor mediaMonitor)
        {
            try
            {
                return await _mediaMonitorService.UploadImageOrCaptureCampaignNote(mediaMonitor);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }
    }
}
