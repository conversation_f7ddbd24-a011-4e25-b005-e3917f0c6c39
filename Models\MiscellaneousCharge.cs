﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MiscellaneousCharge
    {
        public MiscellaneousCharge()
        {
            ContractMiscellaneousCharges = new HashSet<ContractMiscellaneousCharge>();
        }

        public int MiscellaneousChargeId { get; set; }
        public string MiscellaneousChargeName { get; set; }
        public bool Dormant { get; set; }

        public virtual ICollection<ContractMiscellaneousCharge> ContractMiscellaneousCharges { get; set; }
    }
}
