﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BemsTask
    {
        public Guid BemsTaskId { get; set; }
        public DateTime DateScheduled { get; set; }
        public Guid VanId { get; set; }
        public int StoreId { get; set; }
        public Guid ProductId { get; set; }
        public Guid PackSizeId { get; set; }
        public Guid PackSizeUnitOfMeasureId { get; set; }
        public Guid BrandId { get; set; }
        public int CategoryId { get; set; }
        public Guid ContractId { get; set; }
        public string ContractNumber { get; set; }
        public bool Sell { get; set; }
        public bool? InStock { get; set; }
        public Guid? UserId { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }
        public DateTime? DateCompleted { get; set; }
        public DateTime? DateSubmitted { get; set; }

        public virtual PackSize PackSize { get; set; }
        public virtual PackSizeUnitOfMeasure PackSizeUnitOfMeasure { get; set; }
        public virtual Store Store { get; set; }
        public virtual User1 User { get; set; }
        public virtual Van Van { get; set; }
    }
}
