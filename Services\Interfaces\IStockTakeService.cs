﻿using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models;
using PhoenixAPI.Services.EmailModel;
using System.Threading.Tasks;

namespace PhoenixAPI.Services.Interfaces
{
    public interface IStockTakeService
    {
        Task<MasterItemInventoryItem> CreateMasterInventoryItemAsync(MasterItemInventoryItem item, string user);
        Task<IActionResult> CreateStockTakeAsync(InventoryStockTake inventoryStockTake, string user);
        Task<IActionResult> UpdateMasterItemAsync(string barcode, int masterItemId);
        Task<IActionResult> UpdateMasterItemOnStockTakeDetailsBarcodesAsync(string barcode, int masterItemId, int inventoryStockTakeDetailId);
        Task<IActionResult> StockTakeInProgress();
        Task<IActionResult> CompleteStockTake(int stockTakeId);
      //  object UpdateMasterItemLocationAsync(MasterItemLocation model);
    }
}
