﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaRate
    {
        public int Id { get; set; }
        public int MediaId { get; set; }
        public double MediaRate1 { get; set; }
        public DateTime EffectiveDate { get; set; }
        public int PaymentClassificationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string CreatedBy { get; set; }

        public virtual Media Media { get; set; }
        public virtual PaymentClassification PaymentClassification { get; set; }
    }
}
