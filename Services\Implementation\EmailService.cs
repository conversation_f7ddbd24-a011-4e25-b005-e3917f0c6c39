﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using PhoenixAPI.Configurations;
using PhoenixAPI.Models;
using PhoenixAPI.Services.EmailModel;
using PhoenixAPI.Services.Interfaces;

namespace PhoenixAPI.Services.Implementation
{
    public class EmailService : IEmailService
    {
        private readonly Configuration _configuration;
        private static string contentID;
        private static readonly Attachment footer = new Attachment(@"\\192.168.0.16\API Resources\Resources\Images\instore.jpg"); 

        public EmailService(IOptions<Configuration> configuration)
        {
            _configuration = configuration.Value;
            contentID = "Image";
            footer.ContentId = contentID;
            footer.ContentDisposition.Inline = true;
            footer.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
        }

        public async Task<bool> SendAsync(MediaRequisitionRequest model)
        {
            bool isEmailSend = false;

            var message = new MailMessage(_configuration.Sender, model.Email.recipient)
            {
                Subject = "Media Requisition",
                IsBodyHtml = true
            };

            var checkDayOfTheDay = DateTime.Now.Hour < 12 ? "Good Morning,<br/><br/> " : "Good day,<br/><br/> ";

            string MailText = string.Format(checkDayOfTheDay + model.regionRequestingMedia + " is requesting " + model.mediaRequestedQty + " " + model.mediaType + " for " + model.campaign + " campaign " + " from the " + model.regionDistributingMedia
                  + ", please click on the link below to complete the media distribution form.<br/><br/>" +
                  " <a href='" + _configuration.Url + "?requestedBy=" + model.regionRequestingMedia  + "&distributedBy=" + model.regionDistributingMedia + "&mediaType=" + model.mediaType 
                  + "&campaign=" + model.campaign + "&quantity=" + model.mediaRequestedQty + "&id=" + model.contractId + "&chain=" + model.chain + "&contractNumber=" + model.contractNumber
                  + " ' download target='_blank'>" + "<span class='btn btn-primary btn-block'>phoenix.primeinstore.co.za/media-distribution</span>" + "</a> " + "<br/><br/>"  + "Thank you" + "<br/><br/>" + "<img src=\"cid:" + contentID + "\">");

            message.Attachments.Add(footer);
            message.Body = MailText;

            using (var client = new SmtpClient(_configuration.Host, _configuration.Port))
            {
                client.Credentials = new NetworkCredential(_configuration.Username, _configuration.Password);
                client.EnableSsl = true;

                await client.SendMailAsync(message);
                isEmailSend = true;
            }

            return isEmailSend;
        }
    }
}
