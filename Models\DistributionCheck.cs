﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class DistributionCheck
    {
        public DistributionCheck()
        {
            DistributionCheckDetails = new HashSet<DistributionCheckDetail>();
        }

        public Guid CheckId { get; set; }
        public int VanId { get; set; }
        public DateTime CheckDate { get; set; }
        public string OperatorName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Van1 Van { get; set; }
        public virtual ICollection<DistributionCheckDetail> DistributionCheckDetails { get; set; }
    }
}
