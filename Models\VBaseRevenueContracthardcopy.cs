﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VBaseRevenueContracthardcopy
    {
        public Guid BurstId { get; set; }
        public Guid ContractId { get; set; }
        public DateTime? SignDate { get; set; }
        public int ClientId { get; set; }
        public int AccountManagerId { get; set; }
        public int ChainId { get; set; }
        public int MediaId { get; set; }
        public Guid BrandId { get; set; }
        public int InstallStoreQty { get; set; }
        public int InstallWeeks { get; set; }
        public DateTime FirstWeek { get; set; }
        public DateTime? LastWeek { get; set; }
        public int BillableStoreQty { get; set; }
        public int BillableWeeks { get; set; }
        public decimal RentalRate { get; set; }
        public decimal? BasicRental { get; set; }
        public decimal TotalLoadingFeePercentage { get; set; }
        public decimal? TotalLoadingFeeAmount { get; set; }
        public decimal? BasicRentalPlusLoadingFees { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal? DiscountAmount { get; set; }
        public decimal? NetRentalAfterDiscount { get; set; }
        public decimal? AgencyCommPercentage { get; set; }
        public decimal? AgencyCommAmount { get; set; }
        public decimal? RecognisedRevenue { get; set; }
        public decimal? RecognisedWeeklyRevenue { get; set; }
        public decimal? WeeklyRevenuePerBillableWeek { get; set; }
    }
}
