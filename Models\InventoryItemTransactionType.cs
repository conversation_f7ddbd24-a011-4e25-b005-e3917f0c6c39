﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryItemTransactionType
    {
        public int Id { get; set; }
        public string TransactionType { get; set; }
      
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string DeletedBy { get; set; }
        public DateTime? DeletionDate { get; set; }

        public virtual ICollection<InventoryItemTransactions> InventoryItemTransactions { get; set; }
    }
}
