﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class CrmClient
    {
        public int CrmClientId { get; set; }
        public int? ClientId { get; set; }
        public int? ClassificationId { get; set; }
        public string ClientName { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public int? CityId { get; set; }
        public string PostalCode { get; set; }
        public string Vatnumber { get; set; }
        public string Notes { get; set; }
        public bool? Agency { get; set; }
        public string ModBy { get; set; }
        public DateTime? ModDt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreationDate { get; set; }
        public string AccountType { get; set; }
    }
}
