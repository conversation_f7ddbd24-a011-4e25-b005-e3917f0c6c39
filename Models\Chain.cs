﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Chain
    {
        public Chain()
        {
            Bursts = new HashSet<Burst>();
            ChainGroupChains = new HashSet<ChainGroupChain>();
            ChainSelectForPaymentClassifications = new HashSet<ChainSelectForPaymentClassification>();
            ChainTargets = new HashSet<ChainTarget>();
            InverseParentChain = new HashSet<Chain>();
            ProvisionalBookings = new HashSet<ProvisionalBooking>();
            Regions = new HashSet<Region>();
            StoreSelectedForPayments = new HashSet<StoreSelectedForPayment>();
        }

        public int ChainId { get; set; }
        public int ChainTypeId { get; set; }
        public int? ParentChainId { get; set; }
        public string ChainName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string ScannerGroup { get; set; }

        public virtual ChainType ChainType { get; set; }
        public virtual Chain ParentChain { get; set; }
        public virtual ChainsToIncludeForStorePayment ChainsToIncludeForStorePayment { get; set; }
        public virtual ICollection<Burst> Bursts { get; set; }
        public virtual ICollection<ChainGroupChain> ChainGroupChains { get; set; }
        public virtual ICollection<ChainSelectForPaymentClassification> ChainSelectForPaymentClassifications { get; set; }
        public virtual ICollection<ChainTarget> ChainTargets { get; set; }
        public virtual ICollection<Chain> InverseParentChain { get; set; }
        public virtual ICollection<ProvisionalBooking> ProvisionalBookings { get; set; }
        public virtual ICollection<Region> Regions { get; set; }
        public virtual ICollection<StoreSelectedForPayment> StoreSelectedForPayments { get; set; }
    }
}
