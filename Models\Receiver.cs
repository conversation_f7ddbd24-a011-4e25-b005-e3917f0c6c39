﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Receiver
    {
        public Receiver()
        {
            Buttonreceivers = new HashSet<Buttonreceiver>();
        }

        public Guid Receiverid { get; set; }
        public Guid Contractid { get; set; }
        public byte Receivernumber { get; set; }
        public string Product { get; set; }

        public virtual Contract Contract { get; set; }
        public virtual ICollection<Buttonreceiver> Buttonreceivers { get; set; }
    }
}
