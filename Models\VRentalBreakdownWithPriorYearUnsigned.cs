﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VRentalBreakdownWithPriorYearUnsigned
    {
        public int? Year { get; set; }
        public int? Month { get; set; }
        public Guid? ContractId { get; set; }
        public int? ContractAccountManagerId { get; set; }
        public int? AssignedAccountManagerId { get; set; }
        public int? CurrentAccountManagerId { get; set; }
        public int? ClientId { get; set; }
        public int? ChainId { get; set; }
        public int? MediaId { get; set; }
        public Guid? BrandId { get; set; }
        public int? CategoryId { get; set; }
        public string CategoryType { get; set; }
        public decimal Rental { get; set; }
        public decimal PriorRentalActual { get; set; }
        public decimal PriorRentalForecast { get; set; }
        public bool? Signed { get; set; }
        public bool? Approved { get; set; }
    }
}
