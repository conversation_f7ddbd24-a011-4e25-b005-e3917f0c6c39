﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BillingScheduleGroup
    {
        public BillingScheduleGroup()
        {
            BillingScheduleGroupingDetails = new HashSet<BillingScheduleGroupingDetail>();
        }

        public Guid GroupId { get; set; }
        public string GroupName { get; set; }
        public int? GroupTypeId { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }

        public virtual BillingScheduleGroupType GroupType { get; set; }
        public virtual ICollection<BillingScheduleGroupingDetail> BillingScheduleGroupingDetails { get; set; }
    }
}
