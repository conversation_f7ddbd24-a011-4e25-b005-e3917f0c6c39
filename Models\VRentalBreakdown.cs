﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VRentalBreakdown
    {
        public DateTime Week { get; set; }
        public Guid ContractId { get; set; }
        public DateTime? SignDate { get; set; }
        public int ClientId { get; set; }
        public int ContractAccountManagerId { get; set; }
        public int? AssignedAccountManagerId { get; set; }
        public int? CurrentAccountManagerId { get; set; }
        public int ChainId { get; set; }
        public int MediaId { get; set; }
        public Guid BrandId { get; set; }
        public int? CategoryId { get; set; }
        public string CategoryType { get; set; }
        public decimal? Rental { get; set; }
        public decimal ProductionAmount { get; set; }
    }
}
