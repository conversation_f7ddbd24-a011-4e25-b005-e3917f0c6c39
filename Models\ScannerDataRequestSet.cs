﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ScannerDataRequestSet
    {
        public string Number { get; set; }
        public DateTime FromDate1 { get; set; }
        public DateTime ToDate1 { get; set; }
        public DateTime FromDate2 { get; set; }
        public DateTime ToDate2 { get; set; }
        public DateTime? SubmitDate { get; set; }
        public DateTime? PcrReadyDate { get; set; }
        public bool? Standard { get; set; }
        public bool? Selected { get; set; }
        public Guid? RequestId { get; set; }
        public string StoreType { get; set; }
        public bool? FamilyStoresData { get; set; }
        public bool? CorpStoresData { get; set; }
        public DateTime? SubmitDatePnp { get; set; }
        public DateTime? SubmitDateSpar { get; set; }
        public DateTime? SubmitDateClicks { get; set; }
        public DateTime? PcrReadyDatePnp { get; set; }
        public DateTime? PcrReadyDateClicks { get; set; }
        public DateTime? PcrReadyDateSpar { get; set; }
    }
}
