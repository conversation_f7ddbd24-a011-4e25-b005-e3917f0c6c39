﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Settingtype
    {
        public Settingtype()
        {
            Settingtypelinks = new HashSet<Settingtypelink>();
        }

        public Guid Settingtypeid { get; set; }
        public string Settingtypename { get; set; }

        public virtual ICollection<Settingtypelink> Settingtypelinks { get; set; }
    }
}
