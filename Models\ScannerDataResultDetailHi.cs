﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ScannerDataResultDetailHi
    {
        public Guid ScannerImportId { get; set; }
        public Guid PnpstoreId { get; set; }
        public Guid BarcodeId { get; set; }
        public DateTime? FromDate1 { get; set; }
        public DateTime? ToDate1 { get; set; }
        public DateTime? FromDate2 { get; set; }
        public DateTime? ToDate2 { get; set; }
        public double? Qty1 { get; set; }
        public double? Value1 { get; set; }
        public double? Qty2 { get; set; }
        public double? Value2 { get; set; }
        public string Number { get; set; }
        public bool? Promostore { get; set; }
        public Guid? PcrId { get; set; }
        public DateTime? CreatedDt { get; set; }
        public string StoreType { get; set; }
    }
}
