﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Region
    {
        public Region()
        {
            Stores = new HashSet<Store>();
        }

        public int RegionId { get; set; }
        public int ChainId { get; set; }
        public string RegionName { get; set; }

        public virtual Chain Chain { get; set; }
        public virtual ICollection<Store> Stores { get; set; }
    }
}
