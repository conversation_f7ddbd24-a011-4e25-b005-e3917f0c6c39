﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ContractClassification
    {
        public ContractClassification()
        {
            Contracts = new HashSet<Contract>();
        }

        public int ContractClassificationId { get; set; }
        public string Classification { get; set; }
        public bool Dormant { get; set; }
        public DateTime CreationDate { get; set; }
        public string CreatedBy { get; set; }

        public virtual ICollection<Contract> Contracts { get; set; }
    }
}
