﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Van
    {
        public Van()
        {
            BemsTasks = new HashSet<BemsTask>();
            VanUsers = new HashSet<VanUser>();
        }

        public Guid VanId { get; set; }
        public string VanName { get; set; }
        public Guid VanRegionId { get; set; }
        public string CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime? DateRequested { get; set; }

        public virtual VanRegion VanRegion { get; set; }
        public virtual ICollection<BemsTask> BemsTasks { get; set; }
        public virtual ICollection<VanUser> VanUsers { get; set; }
    }
}
