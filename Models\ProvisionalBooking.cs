﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ProvisionalBooking
    {
        public Guid ProvisionalBookingId { get; set; }
        public string ProvisionalBookingName { get; set; }
        public int ChainId { get; set; }
        public int CategoryId { get; set; }
        public int MediaFamilyId { get; set; }
        public Guid BrandId { get; set; }
        public DateTime FirstWeek { get; set; }
        public int Duration { get; set; }
        public string CreatedBy { get; set; }
        public DateTime BookTime { get; set; }
        public DateTime ExpiryTime { get; set; }

        public virtual Brand Brand { get; set; }
        public virtual Category1 Category { get; set; }
        public virtual Chain Chain { get; set; }
        public virtual MediaFamily MediaFamily { get; set; }
    }
}
