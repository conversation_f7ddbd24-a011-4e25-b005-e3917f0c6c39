﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

#nullable disable

namespace PhoenixAPI
{
    public partial class PhoenixUsersContext : DbContext
    {
        public PhoenixUsersContext()
        {
        }

        public PhoenixUsersContext(DbContextOptions<PhoenixUsersContext> options)
            : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                // optionsBuilder.UseSqlServer("Data Source=10.20.32.3;Initial Catalog=PhoenixUsers;Persist Security Info=True;User ID=sa;Password=************");
                optionsBuilder.UseSqlServer(@"Data Source=192.168.0.13\TEST;Initial Catalog=PhoenixUsers;Persist Security Info=True;User ID=SSIS;Password=****************$fRc");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasAnnotation("Relational:Collation", "SQL_Latin1_General_CP1_CI_AS");

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
