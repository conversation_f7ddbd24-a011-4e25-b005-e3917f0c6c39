﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MediaFamily
    {
        public MediaFamily()
        {
            MediaFamilyMembers = new HashSet<MediaFamilyMember>();
            ProvisionalBookings = new HashSet<ProvisionalBooking>();
        }

        public int MediaFamilyId { get; set; }
        public string MediaFamilyName { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<MediaFamilyMember> MediaFamilyMembers { get; set; }
        public virtual ICollection<ProvisionalBooking> ProvisionalBookings { get; set; }
    }
}
