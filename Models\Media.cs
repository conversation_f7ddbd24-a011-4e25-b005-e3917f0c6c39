﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PhoenixAPI.Models
{
    [Table("Media", Schema = "Media")]
    public partial class Media
    {
        public Media()
        {
            Burst = new HashSet<Burst>();
            MediaCategory = new HashSet<MediaCategory>();
            MediaFamilyMember = new HashSet<MediaFamilyMember>();
            MediaGroupMember = new HashSet<MediaGroupMember>();
            MediaInventory = new HashSet<MediaInventory>();
            MediaLifeCycle = new HashSet<MediaLifeCycle>();
            MediaRuleRelationship = new HashSet<MediaRuleRelationship>();
            StockTake = new HashSet<StockTake>();
            StoreMedia = new HashSet<StoreMedia>();
            StoreMediaCategoryPermission = new HashSet<StoreMediaCategoryPermission>();
            StoreRentalRate = new HashSet<StoreRentalRate>();
        }

        [Key]
        [Column("MediaID")]
        public int MediaId { get; set; }
        [Required]
        [StringLength(200)]
        public string MediaName { get; set; }
        public int Stock { get; set; }
        public bool Clutter { get; set; }
        //[Required]
        [StringLength(2000)]
        public string Notes { get; set; }
        [Required]
        public bool? Homesite { get; set; }
        [Required]
        public bool? Crossover { get; set; }
        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreationDate { get; set; }

        [InverseProperty("Media")]
        public virtual ICollection<Burst> Burst { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<MediaCategory> MediaCategory { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<MediaFamilyMember> MediaFamilyMember { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<MediaGroupMember> MediaGroupMember { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<MediaInventory> MediaInventory { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<MediaLifeCycle> MediaLifeCycle { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<MediaRuleRelationship> MediaRuleRelationship { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<StockTake> StockTake { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<StoreMedia> StoreMedia { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<StoreMediaCategoryPermission> StoreMediaCategoryPermission { get; set; }
        [InverseProperty("Media")]
        public virtual ICollection<StoreRentalRate> StoreRentalRate { get; set; }

        //[InverseProperty("Media")]
        public virtual ICollection<MediaCapexOpex> MediaCapexOpex { get; set; }

        public virtual List<Questions> Questions { get; set; }
    }
}
