﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <UserSecretsId>aa82d4d2-185a-4a52-a5b9-be0a13249777</UserSecretsId>
  </PropertyGroup>
	
  <ItemGroup>
    <Compile Remove="Models\MediaMonitorModels\**" />
    <Content Remove="Models\MediaMonitorModels\**" />
    <EmbeddedResource Remove="Models\MediaMonitorModels\**" />
    <None Remove="Models\MediaMonitorModels\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.95.3" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.12.3" />
    <PackageReference Include="HtmlRenderer.Core" Version="1.5.1-beta1" />
    <PackageReference Include="HtmlRenderer.PdfSharp" Version="1.5.1-beta1" />
    <PackageReference Include="MetadataExtractor" Version="2.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="5.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="5.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="5.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="Select.HtmlToPdf.NetCore" Version="20.1.0" />
    <PackageReference Include="System.Runtime" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\OperationsManagement\" />
    <Folder Include="Demonstration\" />
    <Folder Include="Interfaces\" />
    <Folder Include="Models\Demonstrations\" />
    <Folder Include="Models\IncomingMedia\" />
  </ItemGroup>


</Project>
