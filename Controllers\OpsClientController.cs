﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using System.Linq.Expressions;
using PhoenixAPI.Helpers.IRCodesHelper;
using System.Diagnostics;

namespace PhoenixAPI.Controllers
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "Admin")]
    public class OpsClientController : ControllerBase
    {
        [Route("GetVisits")]
        [HttpGet]
        //gets the Store Visits for the User
        //here we will do better
        public async Task<object> GetStoreVisits()
        {
            try
            {
                using (var context = new NovaDBContext())
                {
                    var results = context.InstallationScheduleCurrent

                        .ToList().OrderBy(x => x.Store);

                    var newResults = (from dc in context.InstallationScheduleCurrent
                                      join MediaTable in context.Media on dc.MediaType equals MediaTable.MediaName
                                      into MediaGroupTable
                                      from Media in MediaGroupTable.DefaultIfEmpty()
                                      join MediaCapexOpexTable in context.MediaCapexOpex on Media.MediaId equals MediaCapexOpexTable.MediaId
                                       into MediaCapexOpexGroupTable
                                      from MediaCapexOpex in MediaCapexOpexGroupTable.DefaultIfEmpty()
                                      join MasterItemTable in context.MasterItems on MediaCapexOpex.MasterItemId equals MasterItemTable.Id
                                      into MasterItemGroupTable
                                      from MasterItem in MasterItemGroupTable.DefaultIfEmpty()
                                      join MasterItemGroupsTable in context.MasterItemGroups on MediaCapexOpex.MasterItemGroupId equals MasterItemGroupsTable.MasterItemGroupId
                                     into MasterItemGroupsGroupTable
                                      from MasterItemGroups in MasterItemGroupsGroupTable.DefaultIfEmpty()
                                      select new
                                      {
                                          InstallationScheduleCurrent = dc,
                                          MediaType = Media,
                                          MediaCapexOpex = MediaCapexOpex,
                                          MasterItem = MasterItem,
                                          MasterItemGroups = MasterItemGroups,

                                      }
                                      ).ToList();

                    //lets do different
                    List<StoresWithInstallations> storeNameDays = results.GroupBy(x => new { x.StoId, x.Store, installationDay = x.InstallationDay, x.Region, x.ForDate, x.Chain }).Select(y => new StoresWithInstallations() { storeId = y.Key.StoId, storeName = y.Key.Store, storeInstallDay = y.Key.installationDay, forDate = y.Key.ForDate, region = y.Key.Region, storeInstallations = null, chainName = y.Key.Chain }).OrderBy(z => z.storeName).ToList();

                    //ok, we have distinct stores, lets build the stuff now

                    foreach (StoresWithInstallations sday in storeNameDays)
                    {
                        //lets get all the stuff here
                        List<InstallationScheduleCurrent> lstVMyMobilityExport = results.Where(x => x.Store == sday.storeName).ToList();
                        sday.storeInstallations = new List<InstallationsForStore>();
                        sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore()
                        {
                            categoryName = x.CategoryName,
                            client = x.Client,
                            dayOfCommencementDate = x.DayOfCommencementDate,
                            dayOfTerminationDate = x.DayOfTerminationDate,
                            jobNumber = x.JobNumber,
                            mediaType = x.MediaType,
                            product = x.Product,
                            qtyToInstall = x.QtyToInstall,
                            status = x.Status,
                            installationInstructions = x.SpecialInstructions,
                            CampaignFinished = x.CampaignFinished,
                            CampaignIRCodeSelected = x.CampaignIRCodeSelected,
                            CampaignPicturePath = x.CampaignPicturePath,
                            CampaignPictureTaken = x.CampaignPictureTaken,
                            CampaignSpecialInstructionsRead = x.CampaignSpecialInstructionsRead,
                            InstallationScheduleCurrentID = x.InstallationScheduleCurrentID,
                            Chain = x.Chain,
                            CreatedBy = x.CreatedBy,
                            Group = x.Group,
                            Region = x.Region,
                            Store = x.Store,
                            SelectedIRCode = x.SelectedIRCode,
                            StoreId = x.StoId,
                            ContractNumber = x.JobNumber,
                            TeamId = x.InstallationTeam.InstallationTeamId
                        }).ToList();
                    }
                    var dayIndex = new List<string> { "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY" };

                    //now we have all stores
                    return storeNameDays.OrderBy(x => dayIndex.IndexOf(x.storeInstallDay.ToUpper())).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetStoreVisistsNew")]
        [HttpGet]
        //gets the Store Visits for the User
        //here we will do better
        public async Task<object> GetStoreVisistsNew()
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var context = new NovaDBContext())
                {
                    //SET ONE SINGLETON WITH LIST OF IR CODES
                    //DID THIS BECAUSE WITH REPORTS AND AND 
                    List<IRCodes> _list = new List<IRCodes>();
                    var _irc = IRC.GetInstance();
                    _list = _irc.getList();
                    if (_list.Count == 0)
                    {
                        _list = context.IRCodes.ToList();
                        _irc.setList(_list);
                    }

                    var results = context.InstallationScheduleCurrent
                       .Include(x => x.Media).ThenInclude(x => x.Questions)
                       .ThenInclude(x => x.QuestionsAndAnswers).ThenInclude(x => x.Answers)
                       .Include(x => x.InstallationScheduleQuestionsAndAnswers)
                       //.ThenInclude(x => x.MediaCapexOpex)
                       //.ThenInclude(y => y.MasterItem)
                       //.ThenInclude(z => z.MasterItemInventoryItem
                       //.Where(x => x.StoreId == x.StoreId))
                       //.Include(x => x.Media)
                       //.ThenInclude(x => x.MediaCapexOpex)
                       //.ThenInclude(x => x.MasterItemGroup)
                       //.ThenInclude(x => x.MasterItemGroupMembers)
                       //.ThenInclude(x => x.MasterItem)
                       //.ThenInclude(x => x.MasterItemInventoryItem)

                       //REMOVED THIS JACQUES 2022 08 17 --CHECK

                       //.Include(x => x.SelectedIRCode)
                       //.Include(x => x.PreviousIRCode)

                       .Where(x => x.InstallationTeam.InstallationTeamUser == email && x.IsCurrent == true)
                        .ToList().OrderBy(x => x.Store);

                    //ADDED THIS THIS JACQUES 2022 08 17
                    //I did this becuase it was creating deadlocks and timing out see Check above
                    foreach (var item in results)
                    {
                        item.SelectedIRCode = _list.FirstOrDefault(i => i.IRCodeID == item.IRCodeID);
                        item.PreviousIRCode = _list.FirstOrDefault(i => i.IRCodeID == item.PreviousIRCodeId);
                    }




                    //var newResults = (from dc in context.InstallationScheduleCurrent
                    //                  join MediaTable in context.Media on dc.MediaType equals MediaTable.MediaName
                    //                  into MediaGroupTable
                    //                  from Media in MediaGroupTable.DefaultIfEmpty()
                    //                  join MediaCapexOpexTable in context.MediaCapexOpex on Media.MediaId equals MediaCapexOpexTable.MediaId
                    //                   into MediaCapexOpexGroupTable
                    //                  from MediaCapexOpex in MediaCapexOpexGroupTable.DefaultIfEmpty()
                    //                  join MasterItemTable in context.MasterItems on MediaCapexOpex.MasterItemId equals MasterItemTable.Id
                    //                  into MasterItemGroupTable
                    //                  from MasterItem in MasterItemGroupTable.DefaultIfEmpty()
                    //                  join MasterItemGroupsTable in context.MasterItemGroups on MediaCapexOpex.MasterItemGroupId equals MasterItemGroupsTable.MasterItemGroupId
                    //                 into MasterItemGroupsGroupTable
                    //                  from MasterItemGroups in MasterItemGroupsGroupTable.DefaultIfEmpty()
                    //                  select new
                    //                  {
                    //                      InstallationScheduleCurrent = dc,
                    //                      MediaType = Media,
                    //                      MediaCapexOpex = MediaCapexOpex,
                    //                      MasterItem = MasterItem,
                    //                      MasterItemGroups = MasterItemGroups,


                    //                  }
                    //                  ).ToList();

                    //lets do different
                    //List<StoresWithInstallations> storeNameDays = newResults.GroupBy(x => new { x.InstallationScheduleCurrent.StoId, x.InstallationScheduleCurrent.Store, installationDay = x.InstallationScheduleCurrent.InstallationDay, x.InstallationScheduleCurrent.Region, x.InstallationScheduleCurrent.ForDate, x.InstallationScheduleCurrent.Chain }).Select(y => new StoresWithInstallations() { storeId = y.Key.StoId, storeName = y.Key.Store, storeInstallDay = y.Key.installationDay, forDate = y.Key.ForDate, region = y.Key.Region, storeInstallations = null, chainName = y.Key.Chain }).OrderBy(z => z.storeName).ToList();


                    //lets do different
                    List<StoresWithInstallations> storeNameDays = results.GroupBy(x => new { x.StoId, x.Store, installationDay = x.InstallationDay, x.Region, x.ForDate, x.Chain, x.Installationteamid }).Select(y => new StoresWithInstallations() { storeId = y.Key.StoId, storeName = y.Key.Store, storeInstallDay = y.Key.installationDay, forDate = y.Key.ForDate, region = y.Key.Region, storeInstallations = null, chainName = y.Key.Chain, teamId = y.Key.Installationteamid }).OrderBy(z => z.storeName).ToList();



                    //var res2 = from db in newResults
                    //           group db by db.InstallationScheduleCurrent into g
                    //           select new { Schedule = g.Key, FullSchedule = g.ToList() };

                    //ok, we have distinct stores, lets build the stuff now

                    //what if we get all masteriteminventoryitems here.?
                    var storeIDs = storeNameDays.Select(x => x.storeId).ToList();
                    //  var irCodeIDS = context.IRCodes.Where(x => filterValue.Contains(x.IRCodeName)).Select(x => x.IRCodeID).ToList();
                    //  results = results.Where(v => irCodeIDS.Contains(v.IRCodeID));

                    //var allMediaCapex = context.MasterItemInventoryItems.Where(x => x.InstallationTeamId == storeNameDays.Select(x => x.teamId).FirstOrDefault() || storeIDs.Contains((int)x.StoreId))
                    //    .Include(x => x.MasterItem)
                    //    .ThenInclude(x => x.MediaCapexOpices)


                    //    .ToList();




                    foreach (StoresWithInstallations sday in storeNameDays)
                    {

                        //var data = results
                        //   .GroupBy(s => s.InstallationScheduleCurrent)
                        //   .Select(s => new
                        //   {
                        //       Key = s.Key,
                        //       listOfStudents = s.ToList()
                        //   }).ToList();




                        ////lets get all the stuff here
                        //var lstVMyMobilityExport = results.Where(x => x.Schedule.Store == sday.storeName).ToList();
                        //sday.storeInstallations = new List<InstallationsForStore>();
                        ////sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.InstallationScheduleCurrent.CategoryName, client = x.InstallationScheduleCurrent.Client, dayOfCommencementDate = x.InstallationScheduleCurrent.DayOfCommencementDate, dayOfTerminationDate = x.InstallationScheduleCurrent.DayOfTerminationDate, jobNumber = x.InstallationScheduleCurrent.JobNumber, mediaType = x.InstallationScheduleCurrent.MediaType, product = x.InstallationScheduleCurrent.Product, qtyToInstall = x.InstallationScheduleCurrent.QtyToInstall, status = x.InstallationScheduleCurrent.Status, installationInstructions = x.InstallationScheduleCurrent.SpecialInstructions, CampaignFinished = x.InstallationScheduleCurrent.CampaignFinished, CampaignIRCodeSelected = x.InstallationScheduleCurrent.CampaignIRCodeSelected, CampaignPicturePath = x.InstallationScheduleCurrent.CampaignPicturePath, CampaignPictureTaken = x.InstallationScheduleCurrent.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.InstallationScheduleCurrent.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrent.InstallationScheduleCurrentID, Chain = x.InstallationScheduleCurrent.Chain, CreatedBy = x.InstallationScheduleCurrent.CreatedBy, Group = x.InstallationScheduleCurrent.Group, Region = x.InstallationScheduleCurrent.Region, Store = x.InstallationScheduleCurrent.Store, SelectedIRCode = x.InstallationScheduleCurrent.SelectedIRCode, MasterItem = x.MasterItem, MasterItemGroups = x.MasterItemGroups }).ToList();
                        //sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.Schedule.CategoryName, client = x.Schedule.Client, dayOfCommencementDate = x.Schedule.DayOfCommencementDate, dayOfTerminationDate = x.Schedule.DayOfTerminationDate, jobNumber = x.Schedule.JobNumber, mediaType = x.Schedule.MediaType, product = x.Schedule.Product, qtyToInstall = x.Schedule.QtyToInstall, status = x.Schedule.Status, installationInstructions = x.Schedule.SpecialInstructions, CampaignFinished = x.Schedule.CampaignFinished, CampaignIRCodeSelected = x.Schedule.CampaignIRCodeSelected, CampaignPicturePath = x.Schedule.CampaignPicturePath, CampaignPictureTaken = x.Schedule.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.Schedule.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.Schedule.InstallationScheduleCurrentID, Chain = x.Schedule.Chain, CreatedBy = x.Schedule.CreatedBy, Group = x.Schedule.Group, Region = x.Schedule.Region, Store = x.Schedule.Store, SelectedIRCode = x.Schedule.SelectedIRCode, MasterItem = x.FullSchedule.Select(x => x.MasterItem).ToList(), MasterItemGroups = x.FullSchedule.Select(x => x.MasterItemGroups).ToList(),MediaCapexOpices = x.FullSchedule.Select(x => x.MediaCapexOpex).ToList()}).ToList();

                        //lets get all the stuff here
                        var lstVMyMobilityExport = results.Where(x => x.Store == sday.storeName && x.StoId == sday.storeId && sday.forDate == x.ForDate).ToList();
                        sday.storeInstallations = new List<InstallationsForStore>();
                        //sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.InstallationScheduleCurrent.CategoryName, client = x.InstallationScheduleCurrent.Client, dayOfCommencementDate = x.InstallationScheduleCurrent.DayOfCommencementDate, dayOfTerminationDate = x.InstallationScheduleCurrent.DayOfTerminationDate, jobNumber = x.InstallationScheduleCurrent.JobNumber, mediaType = x.InstallationScheduleCurrent.MediaType, product = x.InstallationScheduleCurrent.Product, qtyToInstall = x.InstallationScheduleCurrent.QtyToInstall, status = x.InstallationScheduleCurrent.Status, installationInstructions = x.InstallationScheduleCurrent.SpecialInstructions, CampaignFinished = x.InstallationScheduleCurrent.CampaignFinished, CampaignIRCodeSelected = x.InstallationScheduleCurrent.CampaignIRCodeSelected, CampaignPicturePath = x.InstallationScheduleCurrent.CampaignPicturePath, CampaignPictureTaken = x.InstallationScheduleCurrent.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.InstallationScheduleCurrent.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrent.InstallationScheduleCurrentID, Chain = x.InstallationScheduleCurrent.Chain, CreatedBy = x.InstallationScheduleCurrent.CreatedBy, Group = x.InstallationScheduleCurrent.Group, Region = x.InstallationScheduleCurrent.Region, Store = x.InstallationScheduleCurrent.Store, SelectedIRCode = x.InstallationScheduleCurrent.SelectedIRCode, MasterItem = x.MasterItem, MasterItemGroups = x.MasterItemGroups }).ToList();
                        sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.CategoryName, client = x.Client, dayOfCommencementDate = x.DayOfCommencementDate, dayOfTerminationDate = x.DayOfTerminationDate, jobNumber = x.JobNumber, mediaType = x.MediaType, product = x.Product, qtyToInstall = x.QtyToInstall, status = x.Status, installationInstructions = x.SpecialInstructions, CampaignFinished = x.CampaignFinished, CampaignIRCodeSelected = x.CampaignIRCodeSelected, PreviousIRCode = x.PreviousIRCode, PreviousIRCodeComment = x.PreviousIRCodeComment, CampaignPicturePath = x.CampaignPicturePath, CampaignPictureTaken = x.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrentID, Chain = x.Chain, CreatedBy = x.CreatedBy, Group = x.Group, Region = x.Region, Store = x.Store, SelectedIRCode = x.SelectedIRCode, MasterItemWithBarcodes = null, MasterItemGroupWithBarcodes = null, Media = x.Media, StoreId = x.StoId, ContractNumber = x.JobNumber, TeamId = sday.teamId, Questions = x.Media.Questions.ToList(), InstallationScheduleQuestionsAndAnswers = x.InstallationScheduleQuestionsAndAnswers }).OrderBy(x => x.product).ToList();


                        List<InstallationsForStore> lstInstallationsForStores = new List<InstallationsForStore>();
                        //this foreach takes a lot of time, we need to speed this up somehow.
                        foreach (InstallationsForStore installationsForStore in sday.storeInstallations)
                        {

                            //var testResult = context.MasterItemInventoryItems.Where(x => x.)

                            var myresulst = context.MediaCapexOpex.Where(x => x.MediaId == installationsForStore.Media.MediaId)
                                .Include(x => x.MasterItem)
                                .ThenInclude(x => x.MasterItemInventoryItem.Where(z => z.StoreId == installationsForStore.StoreId || z.InstallationTeamId == installationsForStore.TeamId))
                                .Include(x => x.MasterItemGroup)
                                .ThenInclude(x => x.MasterItemGroupMembers)
                                .ThenInclude(x => x.MasterItem)
                                .ThenInclude(x => x.MasterItemInventoryItem.Where(z => z.StoreId == installationsForStore.StoreId || z.InstallationTeamId == installationsForStore.TeamId))
                                .ToList();


                            installationsForStore.Media.MediaCapexOpex = myresulst;
                            List<MasterItemWithBarcodes> masterItemWithBarcodes = new List<MasterItemWithBarcodes>();

                            List<MasterItemGroupWithBarcodes> masterItemGroupWithBarcodes = new List<MasterItemGroupWithBarcodes>();
                            foreach (MediaCapexOpex mediaCapexOpex in installationsForStore.Media.MediaCapexOpex)
                            {
                                MasterItemWithBarcodes masterItemWith = new MasterItemWithBarcodes();
                                if (mediaCapexOpex.MasterItem != null)
                                {
                                    if (mediaCapexOpex.AmountRequired > 0)
                                    {
                                        masterItemWith.MasterItemId = mediaCapexOpex.MasterItem.Id;
                                        masterItemWith.MasterItemName = mediaCapexOpex.MasterItem.MasterItemName;
                                        masterItemWith.barcodes = mediaCapexOpex.MasterItem.MasterItemInventoryItem.Select(x => new BarcodeWithId() { id = x.Id, Barcode = x.Barcode, InstallationTeamId = x.InstallationTeamId, StoreId = x.StoreId }).Where(x => x.InstallationTeamId == installationsForStore.TeamId || x.StoreId == installationsForStore.StoreId).ToList();
                                        masterItemWith.amountRequired = mediaCapexOpex.AmountRequired;
                                        masterItemWith.scannedBarcodes = new List<BarcodeWithId>();
                                        masterItemWithBarcodes.Add(masterItemWith);
                                    }

                                }

                                if (mediaCapexOpex.MasterItemGroup != null)
                                {
                                    if (mediaCapexOpex.AmountRequired > 0)
                                    {
                                        MasterItemGroupWithBarcodes masterItemGroupWith = new MasterItemGroupWithBarcodes();

                                        masterItemGroupWith.amountRequired = mediaCapexOpex.AmountRequired;
                                        masterItemGroupWith.MasterItemGroupId = mediaCapexOpex.MasterItemGroup.MasterItemGroupId;
                                        masterItemGroupWith.MasterItemGroupName = mediaCapexOpex.MasterItemGroup.MasterItemGroupName;
                                        masterItemGroupWith.barcodes = new List<BarcodeWithId>();
                                        masterItemGroupWith.scannedBarcodes = new List<BarcodeWithId>();
                                        foreach (var item in mediaCapexOpex.MasterItemGroup.MasterItemGroupMembers)
                                        {

                                            masterItemGroupWith.barcodes.AddRange(item.MasterItem.MasterItemInventoryItem.Select(x => new BarcodeWithId() { id = x.Id, Barcode = x.Barcode, InstallationTeamId = x.InstallationTeamId, StoreId = x.StoreId }).Where(x => x.InstallationTeamId == installationsForStore.TeamId || x.StoreId == installationsForStore.StoreId).ToList());
                                        }
                                        masterItemGroupWithBarcodes.Add(masterItemGroupWith);
                                    }
                                    string abc = "";
                                }



                            }
                            installationsForStore.MasterItemWithBarcodes = masterItemWithBarcodes;
                            installationsForStore.MasterItemGroupWithBarcodes = masterItemGroupWithBarcodes;
                            installationsForStore.Media.MediaCapexOpex = null;
                            foreach (var item in installationsForStore.Media.Questions)
                            {
                                foreach (var item2 in item.QuestionsAndAnswers)
                                {
                                    item2.Answers.QuestionsAndAnswers = null;
                                }
                            }
                            foreach (InstallationScheduleQuestionsAndAnswers iqa in installationsForStore.InstallationScheduleQuestionsAndAnswers)
                            {
                                iqa.InstallationScheduleCurrent = null;


                            }
                            lstInstallationsForStores.Add(installationsForStore);
                        }
                        sday.storeInstallations = lstInstallationsForStores;
                        if (sday.storeInstallations.Where(x => x.CampaignFinished == true).Count() == sday.storeInstallations.Select(x => x.CampaignFinished).Count())
                        {
                            sday.storeVisitCompleted = true;
                        }

                    }

                    var dayIndex = new List<string> { "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY" };

                    //now we have all stores

                    //we need to fix the results here

                    return storeNameDays.OrderBy(x => dayIndex.IndexOf(x.storeInstallDay.ToUpper())).ToList();
                    //IEnumerable<StoresWithInstallations> listB = results.Select(a => new StoresWithInstallations()
                    //{
                    //    storeId = a.StoId,
                    //    storeName = a.Store,
                    //    region = a.Region,
                    //    forDate = a.ForDate,
                    //    storeInstallations =  new List<InstallationsForStore>
                    //    {

                    //        results.GroupBy(x => new { x.Store }).Select(y => new StoresWithInstallations() { FirstWeek = y.Key., BrandName = y.Key.BrandName }).OrderBy(z => z.BrandName).ToList()
                    ////results.GroupBy(x => x.DayOfCommencementDate).Where(x => x.Key.)
                    //    }
                    //}).ToList();

                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        /// <summary>
        /// Tried creating faster version then previous version
        /// </summary>
        /// <returns></returns>
        [Route("GetStoreVisistsNewV2")]
        [HttpGet]
        public async Task<object> GetStoreVisistsNewV2()
        {
            //REMOVE WHEN PUBLISH
            var watch = new System.Diagnostics.Stopwatch();
            watch.Start();
            var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
            System.Console.WriteLine($"Get user claims - Execution Time: {watch.ElapsedMilliseconds} ms");

            ///////////////
            watch.Reset();
            watch.Start();
            ///////////////

            var context = new NovaDBContext();

            Debug.WriteLine($"Context Initialized - Execution Time: {watch.ElapsedMilliseconds} ms");

            ///////////////
            watch.Reset();
            watch.Start();
            ///////////////


            //SET ONE SINGLETON WITH LIST OF IR CODES
            //DID THIS BECAUSE WITH REPORTS AND AND 
            List<IRCodes> _list = new List<IRCodes>();
            var _irc = IRC.GetInstance();
            _list = _irc.getList();
            if (_list.Count == 0)
            {
                _list = context.IRCodes.ToList();
                _irc.setList(_list);
            }
            Debug.WriteLine($"IR codes singleton - Execution Time: {watch.ElapsedMilliseconds} ms");

            ///////////////
            watch.Reset();
            watch.Start();
            ///////////////


            var results = context.InstallationScheduleCurrent
               .Include(x => x.Media).ThenInclude(x => x.Questions)
               .ThenInclude(x => x.QuestionsAndAnswers)
               .ThenInclude(x => x.Answers)
               .Include(x => x.InstallationScheduleQuestionsAndAnswers)
               .Where(x => x.InstallationTeam.InstallationTeamUser == email && x.IsCurrent == true)
               .OrderBy(x => x.Store).ToList();

            Debug.WriteLine($"Results fetch - Execution Time: {watch.ElapsedMilliseconds} ms");

            ///////////////
            watch.Reset();
            watch.Start();
            ///////////////

            //ADDED THIS THIS JACQUES 2022 08 17
            // Check Previous version comment
            Parallel.ForEach(results, item =>
            {
                //Thread Safe since  _list doesnt change
                //https://tinyurl.com/bdcxp28k
                item.SelectedIRCode = _list.FirstOrDefault(i => i.IRCodeID == item.IRCodeID);
                item.PreviousIRCode = _list.FirstOrDefault(i => i.IRCodeID == item.PreviousIRCodeId);
            });
            Debug.WriteLine($"Paralel Foreach: {watch.ElapsedMilliseconds} ms");

            ///////////////
            watch.Reset();
            watch.Start();
            ///////////////

            //lets do different - lukas 
            // why do this?
            List<StoresWithInstallations> storeNameDays = results.GroupBy(x => new
            {
                x.StoId,
                x.Store,
                installationDay =
                x.InstallationDay,
                x.Region,
                x.ForDate,
                x.Chain,
                x.Installationteamid
            }).Select(y => new StoresWithInstallations()
            {
                storeId = y.Key.StoId,
                storeName = y.Key.Store,
                storeInstallDay = y.Key.installationDay,
                forDate = y.Key.ForDate,
                region = y.Key.Region,
                storeInstallations = null,
                chainName = y.Key.Chain,
                teamId = y.Key.Installationteamid
            }).OrderBy(z => z.storeName).ToList();
            Debug.WriteLine($"Lukas Grouping thing: {watch.ElapsedMilliseconds} ms");


            Debug.WriteLine($"################## Foreach Start #######################");
            ///////////////
            watch.Reset();
            watch.Start();
            ///////////////

            foreach (StoresWithInstallations sday in storeNameDays)
            {
                //lets get all the stuff here
                var lstVMyMobilityExport = results.Where(x => x.Store == sday.storeName
                && x.StoId == sday.storeId && sday.forDate == x.ForDate).ToList();

                sday.storeInstallations = new List<InstallationsForStore>();

                //sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.InstallationScheduleCurrent.CategoryName, client = x.InstallationScheduleCurrent.Client, dayOfCommencementDate = x.InstallationScheduleCurrent.DayOfCommencementDate, dayOfTerminationDate = x.InstallationScheduleCurrent.DayOfTerminationDate, jobNumber = x.InstallationScheduleCurrent.JobNumber, mediaType = x.InstallationScheduleCurrent.MediaType, product = x.InstallationScheduleCurrent.Product, qtyToInstall = x.InstallationScheduleCurrent.QtyToInstall, status = x.InstallationScheduleCurrent.Status, installationInstructions = x.InstallationScheduleCurrent.SpecialInstructions, CampaignFinished = x.InstallationScheduleCurrent.CampaignFinished, CampaignIRCodeSelected = x.InstallationScheduleCurrent.CampaignIRCodeSelected, CampaignPicturePath = x.InstallationScheduleCurrent.CampaignPicturePath, CampaignPictureTaken = x.InstallationScheduleCurrent.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.InstallationScheduleCurrent.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrent.InstallationScheduleCurrentID, Chain = x.InstallationScheduleCurrent.Chain, CreatedBy = x.InstallationScheduleCurrent.CreatedBy, Group = x.InstallationScheduleCurrent.Group, Region = x.InstallationScheduleCurrent.Region, Store = x.InstallationScheduleCurrent.Store, SelectedIRCode = x.InstallationScheduleCurrent.SelectedIRCode, MasterItem = x.MasterItem, MasterItemGroups = x.MasterItemGroups }).ToList();
                sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.CategoryName, client = x.Client, dayOfCommencementDate = x.DayOfCommencementDate, dayOfTerminationDate = x.DayOfTerminationDate, jobNumber = x.JobNumber, mediaType = x.MediaType, product = x.Product, qtyToInstall = x.QtyToInstall, status = x.Status, installationInstructions = x.SpecialInstructions, CampaignFinished = x.CampaignFinished, CampaignIRCodeSelected = x.CampaignIRCodeSelected, PreviousIRCode = x.PreviousIRCode, PreviousIRCodeComment = x.PreviousIRCodeComment, CampaignPicturePath = x.CampaignPicturePath, CampaignPictureTaken = x.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrentID, Chain = x.Chain, CreatedBy = x.CreatedBy, Group = x.Group, Region = x.Region, Store = x.Store, SelectedIRCode = x.SelectedIRCode, MasterItemWithBarcodes = null, MasterItemGroupWithBarcodes = null, Media = x.Media, StoreId = x.StoId, ContractNumber = x.JobNumber, TeamId = sday.teamId, Questions = x.Media.Questions.ToList(), InstallationScheduleQuestionsAndAnswers = x.InstallationScheduleQuestionsAndAnswers }).OrderBy(x => x.product).ToList();


                List<InstallationsForStore> lstInstallationsForStores = new List<InstallationsForStore>();

                //this foreach takes a lot of time, we need to speed this up somehow.
                foreach (InstallationsForStore installationsForStore in sday.storeInstallations)
                {
                    //^^^^^^^^^^^^^^^DONT KNOW IF THE FRONT END IS EXPECTING A LIST WITH ZERO ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    List<MasterItemWithBarcodes> masterItemWithBarcodes = new List<MasterItemWithBarcodes>();
                    List<MasterItemGroupWithBarcodes> masterItemGroupWithBarcodes = new List<MasterItemGroupWithBarcodes>();
                    //^^^^^^^^^^^^^^^DONT KNOW IF THE FRONT END IS EXPECTING A LIST WITH ZERO ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

                    //CHECK IF THERE ARE ANY MEDIA CAPEX
                    if (context.MediaCapexOpex.Any(x => x.MediaId == installationsForStore.Media.MediaId))
                    {
                        //GET MEDIA CAPEX
                        installationsForStore.Media.MediaCapexOpex = context.MediaCapexOpex.Where(x => x.MediaId == installationsForStore.Media.MediaId)
                        .Include(x => x.MasterItem)
                        .ThenInclude(x => x.MasterItemInventoryItem.Where(z => z.StoreId == installationsForStore.StoreId ||
                            z.InstallationTeamId == installationsForStore.TeamId))
                        .Include(x => x.MasterItemGroup)
                        .ThenInclude(x => x.MasterItemGroupMembers)
                        .ThenInclude(x => x.MasterItem)
                        .ThenInclude(x => x.MasterItemInventoryItem.Where(z => z.StoreId == installationsForStore.StoreId ||
                            z.InstallationTeamId == installationsForStore.TeamId))
                        .ToList();

                        //DO SOME BUSINESS LOGIC WITH IT
                        foreach (MediaCapexOpex mediaCapexOpex in installationsForStore.Media.MediaCapexOpex)
                        {
                            MasterItemWithBarcodes masterItemWith = new MasterItemWithBarcodes();
                            if (mediaCapexOpex.MasterItem != null)
                            {
                                if (mediaCapexOpex.AmountRequired > 0)
                                {
                                    masterItemWith.MasterItemId = mediaCapexOpex.MasterItem.Id;
                                    masterItemWith.MasterItemName = mediaCapexOpex.MasterItem.MasterItemName;
                                    masterItemWith.barcodes = mediaCapexOpex.MasterItem.MasterItemInventoryItem.Select(x => new BarcodeWithId() { id = x.Id, Barcode = x.Barcode, InstallationTeamId = x.InstallationTeamId, StoreId = x.StoreId }).Where(x => x.InstallationTeamId == installationsForStore.TeamId || x.StoreId == installationsForStore.StoreId).ToList();
                                    masterItemWith.amountRequired = mediaCapexOpex.AmountRequired;
                                    masterItemWith.scannedBarcodes = new List<BarcodeWithId>();
                                    masterItemWithBarcodes.Add(masterItemWith);
                                }
                            }
                            if (mediaCapexOpex.MasterItemGroup != null)
                            {
                                if (mediaCapexOpex.AmountRequired > 0)
                                {
                                    MasterItemGroupWithBarcodes masterItemGroupWith = new MasterItemGroupWithBarcodes();

                                    masterItemGroupWith.amountRequired = mediaCapexOpex.AmountRequired;
                                    masterItemGroupWith.MasterItemGroupId = mediaCapexOpex.MasterItemGroup.MasterItemGroupId;
                                    masterItemGroupWith.MasterItemGroupName = mediaCapexOpex.MasterItemGroup.MasterItemGroupName;
                                    masterItemGroupWith.barcodes = new List<BarcodeWithId>();
                                    masterItemGroupWith.scannedBarcodes = new List<BarcodeWithId>();
                                    foreach (var item in mediaCapexOpex.MasterItemGroup.MasterItemGroupMembers)
                                    {
                                        masterItemGroupWith.barcodes.AddRange(item.MasterItem.MasterItemInventoryItem.Select(x => new BarcodeWithId() { id = x.Id, Barcode = x.Barcode, InstallationTeamId = x.InstallationTeamId, StoreId = x.StoreId }).Where(x => x.InstallationTeamId == installationsForStore.TeamId || x.StoreId == installationsForStore.StoreId).ToList());
                                    }
                                    masterItemGroupWithBarcodes.Add(masterItemGroupWith);
                                }
                            }
                        }
                    }
                
                    //JUST CHECK IF THERE IS ANY
                    if (installationsForStore.Media.Questions.Any() || installationsForStore.InstallationScheduleQuestionsAndAnswers.Any())
                    {
                        foreach (var item in installationsForStore.Media.Questions)
                        {
                            foreach (var item2 in item.QuestionsAndAnswers)
                            {
                                item2.Answers.QuestionsAndAnswers = null;
                            }
                        }
                        foreach (InstallationScheduleQuestionsAndAnswers iqa in installationsForStore.InstallationScheduleQuestionsAndAnswers)
                        {
                            iqa.InstallationScheduleCurrent = null;
                        }
                    }

                    installationsForStore.MasterItemWithBarcodes = masterItemWithBarcodes;
                    installationsForStore.MasterItemGroupWithBarcodes = masterItemGroupWithBarcodes;
                    installationsForStore.Media.MediaCapexOpex = null;

                    lstInstallationsForStores.Add(installationsForStore);
                }
                sday.storeInstallations = lstInstallationsForStores;

                if (sday.storeInstallations.Where(x => x.CampaignFinished == true).Count() == sday.storeInstallations.Select(x => x.CampaignFinished).Count())
                {
                    sday.storeVisitCompleted = true;
                }
            }

            Debug.WriteLine($"Foreach end: {watch.ElapsedMilliseconds} ms");
            Debug.WriteLine($"################## Foreach End #######################");


            //This does not have to be declared everytime...
            var dayIndex = new List<string> { "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY" };

            return storeNameDays.OrderBy(x => dayIndex.IndexOf(x.storeInstallDay.ToUpper())).ToList();

        }


        [Route("GetStoreVisitsFast")]
        [HttpGet]
        //gets the Store Visits for the User
        //here we will do better
        public async Task<object> GetStoreVisitsFast()
        {
            try
            {

                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;



                using (var context = new NovaDBContext())
                {




                    var results = context.InstallationScheduleCurrent
                       .Include(x => x.Media).ThenInclude(x => x.Questions)
                       .ThenInclude(x => x.QuestionsAndAnswers).ThenInclude(x => x.Answers)
                       .Include(x => x.InstallationScheduleQuestionsAndAnswers)
                       .Include(x => x.Media.MediaCapexOpex)
                        .ThenInclude(x => x.MasterItem)
                       .ThenInclude(x => x.MasterItemInventoryItem)
                       .Include(x => x.Media.MediaCapexOpex)
                        .ThenInclude(x => x.MasterItemGroup)
                         .ThenInclude(x => x.MasterItemGroupMembers)
                         .ThenInclude(x => x.MasterItem)
                        .ThenInclude(x => x.MasterItemInventoryItem)

                       .Include(x => x.SelectedIRCode)
                       .Include(x => x.PreviousIRCode)
                       .Where(x => x.InstallationTeam.InstallationTeamUser == email && x.IsCurrent == true)
                        .ToList().OrderBy(x => x.Store);


                    //lets do different
                    List<StoresWithInstallations> storeNameDays = results.GroupBy(x => new { x.StoId, x.Store, installationDay = x.InstallationDay, x.Region, x.ForDate, x.Chain, x.Installationteamid }).Select(y => new StoresWithInstallations() { storeId = y.Key.StoId, storeName = y.Key.Store, storeInstallDay = y.Key.installationDay, forDate = y.Key.ForDate, region = y.Key.Region, storeInstallations = null, chainName = y.Key.Chain, teamId = y.Key.Installationteamid }).OrderBy(z => z.storeName).ToList();


                    foreach (StoresWithInstallations sday in storeNameDays)
                    {

                        var lstVMyMobilityExport = results.Where(x => x.Store == sday.storeName && x.StoId == sday.storeId && sday.forDate == x.ForDate).ToList();
                        sday.storeInstallations = new List<InstallationsForStore>();
                        //sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.InstallationScheduleCurrent.CategoryName, client = x.InstallationScheduleCurrent.Client, dayOfCommencementDate = x.InstallationScheduleCurrent.DayOfCommencementDate, dayOfTerminationDate = x.InstallationScheduleCurrent.DayOfTerminationDate, jobNumber = x.InstallationScheduleCurrent.JobNumber, mediaType = x.InstallationScheduleCurrent.MediaType, product = x.InstallationScheduleCurrent.Product, qtyToInstall = x.InstallationScheduleCurrent.QtyToInstall, status = x.InstallationScheduleCurrent.Status, installationInstructions = x.InstallationScheduleCurrent.SpecialInstructions, CampaignFinished = x.InstallationScheduleCurrent.CampaignFinished, CampaignIRCodeSelected = x.InstallationScheduleCurrent.CampaignIRCodeSelected, CampaignPicturePath = x.InstallationScheduleCurrent.CampaignPicturePath, CampaignPictureTaken = x.InstallationScheduleCurrent.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.InstallationScheduleCurrent.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrent.InstallationScheduleCurrentID, Chain = x.InstallationScheduleCurrent.Chain, CreatedBy = x.InstallationScheduleCurrent.CreatedBy, Group = x.InstallationScheduleCurrent.Group, Region = x.InstallationScheduleCurrent.Region, Store = x.InstallationScheduleCurrent.Store, SelectedIRCode = x.InstallationScheduleCurrent.SelectedIRCode, MasterItem = x.MasterItem, MasterItemGroups = x.MasterItemGroups }).ToList();
                        sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.CategoryName, client = x.Client, dayOfCommencementDate = x.DayOfCommencementDate, dayOfTerminationDate = x.DayOfTerminationDate, jobNumber = x.JobNumber, mediaType = x.MediaType, product = x.Product, qtyToInstall = x.QtyToInstall, status = x.Status, installationInstructions = x.SpecialInstructions, CampaignFinished = x.CampaignFinished, CampaignIRCodeSelected = x.CampaignIRCodeSelected, PreviousIRCode = x.PreviousIRCode, PreviousIRCodeComment = x.PreviousIRCodeComment, CampaignPicturePath = x.CampaignPicturePath, CampaignPictureTaken = x.CampaignPictureTaken, CampaignSpecialInstructionsRead = x.CampaignSpecialInstructionsRead, InstallationScheduleCurrentID = x.InstallationScheduleCurrentID, Chain = x.Chain, CreatedBy = x.CreatedBy, Group = x.Group, Region = x.Region, Store = x.Store, SelectedIRCode = x.SelectedIRCode, MasterItemWithBarcodes = null, MasterItemGroupWithBarcodes = null, Media = x.Media, StoreId = x.StoId, ContractNumber = x.JobNumber, TeamId = sday.teamId, Questions = x.Media.Questions.ToList(), InstallationScheduleQuestionsAndAnswers = x.InstallationScheduleQuestionsAndAnswers }).OrderBy(x => x.product).ToList();
                        List<InstallationsForStore> lstInstallationsForStores = new List<InstallationsForStore>();
                        foreach (InstallationsForStore installationsForStore in sday.storeInstallations)
                        {
                            var checker = context.MediaCapexOpex.Where(x => x.MediaId == installationsForStore.Media.MediaId).ToList();
                            if (checker.Count > 0)
                            {
                                var myresulst = context.MediaCapexOpex.Where(x => x.MediaId == installationsForStore.Media.MediaId)
                                                                .Include(x => x.MasterItem)
                                                                .ThenInclude(x => x.MasterItemInventoryItem.Where(z => z.StoreId == installationsForStore.StoreId || z.InstallationTeamId == installationsForStore.TeamId))
                                                                .Include(x => x.MasterItemGroup)
                                                                .ThenInclude(x => x.MasterItemGroupMembers)
                                                                .ThenInclude(x => x.MasterItem)
                                                                .ThenInclude(x => x.MasterItemInventoryItem.Where(z => z.StoreId == installationsForStore.StoreId || z.InstallationTeamId == installationsForStore.TeamId))
                                                                .ToList();


                                installationsForStore.Media.MediaCapexOpex = myresulst;
                                List<MasterItemWithBarcodes> masterItemWithBarcodes = new List<MasterItemWithBarcodes>();

                                List<MasterItemGroupWithBarcodes> masterItemGroupWithBarcodes = new List<MasterItemGroupWithBarcodes>();
                                foreach (MediaCapexOpex mediaCapexOpex in installationsForStore.Media.MediaCapexOpex)
                                {
                                    MasterItemWithBarcodes masterItemWith = new MasterItemWithBarcodes();
                                    if (mediaCapexOpex.MasterItem != null)
                                    {
                                        if (mediaCapexOpex.AmountRequired > 0)
                                        {
                                            masterItemWith.MasterItemId = mediaCapexOpex.MasterItem.Id;
                                            masterItemWith.MasterItemName = mediaCapexOpex.MasterItem.MasterItemName;
                                            masterItemWith.barcodes = mediaCapexOpex.MasterItem.MasterItemInventoryItem.Select(x => new BarcodeWithId() { id = x.Id, Barcode = x.Barcode, InstallationTeamId = x.InstallationTeamId, StoreId = x.StoreId }).Where(x => x.InstallationTeamId == installationsForStore.TeamId || x.StoreId == installationsForStore.StoreId).ToList();
                                            masterItemWith.amountRequired = mediaCapexOpex.AmountRequired;
                                            masterItemWith.scannedBarcodes = new List<BarcodeWithId>();
                                            masterItemWithBarcodes.Add(masterItemWith);
                                        }

                                    }

                                    if (mediaCapexOpex.MasterItemGroup != null)
                                    {
                                        if (mediaCapexOpex.AmountRequired > 0)
                                        {
                                            MasterItemGroupWithBarcodes masterItemGroupWith = new MasterItemGroupWithBarcodes();

                                            masterItemGroupWith.amountRequired = mediaCapexOpex.AmountRequired;
                                            masterItemGroupWith.MasterItemGroupId = mediaCapexOpex.MasterItemGroup.MasterItemGroupId;
                                            masterItemGroupWith.MasterItemGroupName = mediaCapexOpex.MasterItemGroup.MasterItemGroupName;
                                            masterItemGroupWith.barcodes = new List<BarcodeWithId>();
                                            masterItemGroupWith.scannedBarcodes = new List<BarcodeWithId>();
                                            foreach (var item in mediaCapexOpex.MasterItemGroup.MasterItemGroupMembers)
                                            {

                                                masterItemGroupWith.barcodes.AddRange(item.MasterItem.MasterItemInventoryItem.Select(x => new BarcodeWithId() { id = x.Id, Barcode = x.Barcode, InstallationTeamId = x.InstallationTeamId, StoreId = x.StoreId }).Where(x => x.InstallationTeamId == installationsForStore.TeamId || x.StoreId == installationsForStore.StoreId).ToList());
                                            }
                                            masterItemGroupWithBarcodes.Add(masterItemGroupWith);
                                        }
                                        string abc = "";
                                    }



                                }
                                installationsForStore.MasterItemWithBarcodes = masterItemWithBarcodes;
                                installationsForStore.MasterItemGroupWithBarcodes = masterItemGroupWithBarcodes;
                            }


                            installationsForStore.Media.MediaCapexOpex = null;
                            foreach (var item in installationsForStore.Media.Questions)
                            {
                                foreach (var item2 in item.QuestionsAndAnswers)
                                {
                                    item2.Answers.QuestionsAndAnswers = null;
                                }
                            }
                            foreach (InstallationScheduleQuestionsAndAnswers iqa in installationsForStore.InstallationScheduleQuestionsAndAnswers)
                            {
                                iqa.InstallationScheduleCurrent = null;


                            }
                            lstInstallationsForStores.Add(installationsForStore);
                        }
                        sday.storeInstallations = lstInstallationsForStores;
                        if (sday.storeInstallations.Where(x => x.CampaignFinished == true).Count() == sday.storeInstallations.Select(x => x.CampaignFinished).Count())
                        {
                            sday.storeVisitCompleted = true;
                        }

                    }

                    var dayIndex = new List<string> { "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY" };



                    return storeNameDays.OrderBy(x => dayIndex.IndexOf(x.storeInstallDay.ToUpper())).ToList();


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("GetVisitsByStores/{storeName}")]
        [HttpGet]
        //gets the Store Visits for the User

        public IEnumerable<VMyMobilityExport> GetStoreVisitsByStore(string storeName)
        {
            using (var context = new NovaDBContext())
            {
                return context.VMyMobilityExport
                    .Where(x => x.Store == storeName)
                    .ToList().OrderBy(x => x.Store);
            }
        }
        [Route("UpdateStoreVisit")]
        [HttpPost]
        public async Task<object> UpdateStoreVisit(InstallationsForStore installationsForStore)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {

                    if (installationsForStore.baseImage != "")
                    {
                        //we need to do something
                    }
                    var result = await db.InstallationScheduleCurrent.SingleOrDefaultAsync(s => s.InstallationScheduleCurrentID == installationsForStore.InstallationScheduleCurrentID);
                    result.CampaignSpecialInstructionsRead = installationsForStore.CampaignSpecialInstructionsRead;
                    result.CampaignPictureTaken = installationsForStore.CampaignPictureTaken;
                    //result.LastModified = System.DateTime.Now;
                    result.LastModifiedBy = installationsForStore.TeamId;

                    //if(installationsForStore.openedDate != null)
                    //{
                    //    result.OpenedDate = installationsForStore.openedDate;
                    //}

                    if (result.OpenedDate == null || result.OpenedDate.Year == 1)
                    {
                        result.OpenedDate = System.DateTime.Now;
                    }

                    if (installationsForStore.SelectedIRCode != null && installationsForStore.SelectedIRCode.IRCodeID != 0)
                    {
                        result.CampaignIRCodeSelected = true;
                        result.SelectedIRCode = installationsForStore.SelectedIRCode;
                        result.IRCodeComment = installationsForStore.IRCodeComment;
                    }
                    //result.Dormant = iRCodes.Dormant;
                    if (result.CampaignSpecialInstructionsRead && result.CampaignIRCodeSelected && installationsForStore.CampaignFinished)
                    {

                        //if (installationsForStore.finishedDate != null)
                        //{
                        //    result.FinishedDate = installationsForStore.finishedDate;
                        //}
                        //else
                        //{
                        //    result.FinishedDate = System.DateTime.Now;
                        //}

                        if (result.FinishedDate == null || result.FinishedDate.Year == 1)
                        {
                            result.FinishedDate = System.DateTime.Now;
                        }
                        result.CampaignFinished = true;

                    }

                    foreach (MasterItemWithBarcodes masterItemWithBarcodes in installationsForStore.MasterItemWithBarcodes)
                    {
                        if (masterItemWithBarcodes.scannedBarcodes.Count > 0)
                        {
                            try
                            {
                                foreach (BarcodeWithId barcodeWithId in masterItemWithBarcodes.scannedBarcodes)
                                {
                                    bool isExisting = true;
                                    MasterItemInventoryItem resultMasterItemInventoryItem = new MasterItemInventoryItem();

                                    InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();
                                    if (barcodeWithId.installationActionId == "")
                                    {
                                        resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Id == barcodeWithId.id);
                                    }
                                    else
                                    {
                                        barcodeWithId.InstallationTeamId = db.InstallationScheduleCurrent.Where(x => x.InstallationScheduleCurrentID == new Guid(barcodeWithId.installationActionId)).Select(x => x.Installationteamid).FirstOrDefault();
                                        resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Barcode == barcodeWithId.Barcode);
                                    }

                                    //var resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Id == barcodeWithId.id);

                                    //InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();

                                    if (resultMasterItemInventoryItem != null)
                                    {
                                        inventoryItemTransactions.Barcode = resultMasterItemInventoryItem.Barcode;
                                        inventoryItemTransactions.MasterItemId = resultMasterItemInventoryItem.MasterItemId;
                                        inventoryItemTransactions.MasterItemInvenoryItemId = resultMasterItemInventoryItem.Id;
                                        resultMasterItemInventoryItem.ContractNumber = installationsForStore.ContractNumber;
                                    }
                                    else
                                    {
                                        isExisting = false;
                                        resultMasterItemInventoryItem = new MasterItemInventoryItem();
                                        resultMasterItemInventoryItem.Barcode = barcodeWithId.Barcode;
                                        inventoryItemTransactions.Barcode = resultMasterItemInventoryItem.Barcode;
                                        resultMasterItemInventoryItem.MasterItemId = db.MasterItems.Where(x => x.MasterItemName == installationsForStore.MasterItemWithBarcodes[0].MasterItemName).Select(x => x.Id).FirstOrDefault();
                                        resultMasterItemInventoryItem.ContractNumber = installationsForStore.ContractNumber;

                                    }

                                    inventoryItemTransactions.CreatedBy = email;




                                    if (installationsForStore.status.ToLower() == "remove")
                                    {
                                        resultMasterItemInventoryItem.StoreId = null;
                                        resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.TeamId;
                                        resultMasterItemInventoryItem.WarehouseId = null;

                                        inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                        inventoryItemTransactions.ToVanId = installationsForStore.TeamId;
                                    }
                                    else if (installationsForStore.status.ToLower() == "install")
                                    {
                                        resultMasterItemInventoryItem.InstallationTeamId = null;
                                        resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                        resultMasterItemInventoryItem.WarehouseId = null;

                                        inventoryItemTransactions.FromVanId = installationsForStore.TeamId;
                                        inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                    }
                                    else if (installationsForStore.status.ToLower() == "running")
                                    {
                                        resultMasterItemInventoryItem.InstallationTeamId = null;
                                        resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                        resultMasterItemInventoryItem.WarehouseId = null;

                                        if (resultMasterItemInventoryItem.InstallationTeamId != null)
                                        {
                                            inventoryItemTransactions.FromVanId = resultMasterItemInventoryItem.InstallationTeamId;
                                            inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                        }
                                        else
                                        {
                                            inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                            inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                        }

                                    }
                                    else
                                    {
                                        resultMasterItemInventoryItem.StoreId = null;
                                        resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.StoreId;
                                        resultMasterItemInventoryItem.WarehouseId = null;
                                    }


                                    var hasGotten = db.InventoryItemTransactions.Where(x => x.Barcode == inventoryItemTransactions.Barcode && x.CreatedBy == inventoryItemTransactions.CreatedBy).FirstOrDefault();

                                    if (hasGotten != null)
                                    {

                                    }
                                    else
                                    {
                                        if (!isExisting)
                                        {
                                            db.MasterItemInventoryItems.Add(resultMasterItemInventoryItem);
                                            db.SaveChanges();
                                        }
                                        db.InventoryItemTransactions.Add(inventoryItemTransactions);
                                    }

                                }
                            }
                            catch (Exception ex)
                            {

                            }

                            //we need to update the inventory items

                        }
                    }

                    foreach (MasterItemGroupWithBarcodes masterItemWithBarcodes in installationsForStore.MasterItemGroupWithBarcodes)
                    {
                        if (masterItemWithBarcodes.scannedBarcodes.Count > 0)
                        {
                            try
                            {
                                foreach (BarcodeWithId barcodeWithIdGroup in masterItemWithBarcodes.scannedBarcodes)
                                {
                                    bool isExisting = true;
                                    MasterItemInventoryItem resultMasterItemInventoryItem = new MasterItemInventoryItem();

                                    InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();
                                    if (barcodeWithIdGroup.installationActionId == "")
                                    {
                                        resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Id == barcodeWithIdGroup.id);
                                    }
                                    else
                                    {
                                        barcodeWithIdGroup.InstallationTeamId = db.InstallationScheduleCurrent.Where(x => x.InstallationScheduleCurrentID == new Guid(barcodeWithIdGroup.installationActionId)).Select(x => x.Installationteamid).FirstOrDefault();
                                        resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Barcode == barcodeWithIdGroup.Barcode);
                                    }
                                    if (resultMasterItemInventoryItem == null || resultMasterItemInventoryItem.Id == 0)
                                    {
                                        //lets create the item, then get it again

                                        resultMasterItemInventoryItem = new MasterItemInventoryItem();
                                        resultMasterItemInventoryItem.Barcode = barcodeWithIdGroup.Barcode;

                                    }

                                    if (resultMasterItemInventoryItem != null && resultMasterItemInventoryItem.Id != 0)
                                    {
                                        inventoryItemTransactions.Barcode = resultMasterItemInventoryItem.Barcode;
                                        inventoryItemTransactions.MasterItemId = resultMasterItemInventoryItem.MasterItemId;
                                        inventoryItemTransactions.MasterItemInvenoryItemId = resultMasterItemInventoryItem.Id;
                                        resultMasterItemInventoryItem.ContractNumber = installationsForStore.ContractNumber;
                                    }
                                    else
                                    {
                                        isExisting = false;
                                        resultMasterItemInventoryItem = new MasterItemInventoryItem();
                                        resultMasterItemInventoryItem.Barcode = barcodeWithIdGroup.Barcode;
                                        inventoryItemTransactions.Barcode = resultMasterItemInventoryItem.Barcode;
                                        resultMasterItemInventoryItem.MasterItemId = 0;

                                    }
                                    inventoryItemTransactions.CreatedBy = email;

                                    inventoryItemTransactions.MasterItemId = resultMasterItemInventoryItem.MasterItemId;
                                    inventoryItemTransactions.MasterItemInvenoryItemId = resultMasterItemInventoryItem.Id;


                                    resultMasterItemInventoryItem.ContractNumber = installationsForStore.ContractNumber;
                                    if (installationsForStore.status.ToLower() == "remove")
                                    {
                                        resultMasterItemInventoryItem.StoreId = null;
                                        resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.TeamId;
                                        resultMasterItemInventoryItem.WarehouseId = null;

                                        inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                        inventoryItemTransactions.ToVanId = installationsForStore.TeamId;
                                    }
                                    else if (installationsForStore.status.ToLower() == "install")
                                    {
                                        resultMasterItemInventoryItem.InstallationTeamId = null;
                                        resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                        resultMasterItemInventoryItem.WarehouseId = null;

                                        inventoryItemTransactions.FromVanId = installationsForStore.TeamId;
                                        inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                    }
                                    else if (installationsForStore.status.ToLower() == "running")
                                    {
                                        resultMasterItemInventoryItem.InstallationTeamId = null;
                                        resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                        resultMasterItemInventoryItem.WarehouseId = null;
                                        if (resultMasterItemInventoryItem.InstallationTeamId != null)
                                        {
                                            inventoryItemTransactions.FromVanId = resultMasterItemInventoryItem.InstallationTeamId;
                                            inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                        }
                                        else
                                        {
                                            inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                            inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                        }
                                    }
                                    else
                                    {
                                        resultMasterItemInventoryItem.StoreId = null;
                                        resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.StoreId;
                                        resultMasterItemInventoryItem.WarehouseId = null;
                                    }

                                    var hasGotten = db.InventoryItemTransactions.Where(x => x.Barcode == inventoryItemTransactions.Barcode && x.CreatedBy == inventoryItemTransactions.CreatedBy).FirstOrDefault();

                                    if (hasGotten != null)
                                    {

                                    }
                                    else
                                    {
                                        if (!isExisting)
                                        {

                                        }
                                        else
                                        {
                                            db.InventoryItemTransactions.Add(inventoryItemTransactions);
                                        }

                                    }


                                    //inventoryItemTransactions = resultMasterItemInventoryItem.;
                                    //inventoryItemTransactions.CreatedBy = resultMasterItemInventoryItem.CreatedBy;
                                    //inventoryItemTransactions.

                                }
                            }
                            catch
                            {

                            }

                            //we need to update the inventory items

                        }
                    }
                    if (installationsForStore.barcodesToRemove != null)
                    {
                        foreach (BarcodeWithId barcode in installationsForStore.barcodesToRemove)
                        {
                            //we need to find its id, then we need to move it I suppose.
                            MasterItemInventoryItem resultMasterItemInventoryItem = new MasterItemInventoryItem();
                            resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Barcode == barcode.Barcode);
                            if (resultMasterItemInventoryItem != null)
                            {
                                resultMasterItemInventoryItem.StoreId = null;
                                resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.TeamId;

                                InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();
                                inventoryItemTransactions.Barcode = barcode.Barcode;
                                inventoryItemTransactions.CreatedBy = email;
                                inventoryItemTransactions.CreationDate = System.DateTime.Now;
                                inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                inventoryItemTransactions.ToStoreId = null;
                                inventoryItemTransactions.FromVanId = null;
                                inventoryItemTransactions.ToVanId = installationsForStore.TeamId;
                                inventoryItemTransactions.ToWarehouse = null;
                                inventoryItemTransactions.FromWarehouse = null;
                                inventoryItemTransactions.MasterItemInvenoryItemId = resultMasterItemInventoryItem.Id;

                                var hasGotten = db.InventoryItemTransactions.Where(x => x.Barcode == inventoryItemTransactions.Barcode && x.CreatedBy == inventoryItemTransactions.CreatedBy).FirstOrDefault();

                                if (hasGotten != null)
                                {

                                }
                                else
                                {

                                    db.InventoryItemTransactions.Add(inventoryItemTransactions);
                                }

                            }
                        }

                    }

                    foreach (InstallationScheduleQuestionsAndAnswers installationScheduleQuestionsAndAnswers in installationsForStore.InstallationScheduleQuestionsAndAnswers)
                    {
                        //ensure we either add or update
                        //check if it exists
                        var value = db.InstallationScheduleQuestionsAndAnswers.Where(x => x.InstallationActionId == installationScheduleQuestionsAndAnswers.InstallationActionId && x.QuestionId == installationScheduleQuestionsAndAnswers.QuestionId).FirstOrDefault();
                        if (value != null)
                        {
                            value.AnswerId = installationScheduleQuestionsAndAnswers.AnswerId;
                            value.Comment = installationScheduleQuestionsAndAnswers.Comment;
                            db.InstallationScheduleQuestionsAndAnswers.Update(value);
                        }
                        else
                        {
                            db.InstallationScheduleQuestionsAndAnswers.Add(installationScheduleQuestionsAndAnswers);
                        }
                    }

                    db.SaveChanges();
                    return result;
                    // return StatusCode(200, Newtonsoft.Json.JsonConvert.SerializeObject("OK"));
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }

        }


        [Route("UpdateStoreVisitByStore")]
        [HttpPost]
        public bool UpdateStoreVisitByStore(StoresWithInstallations store)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                foreach (InstallationsForStore installationsForStore in store.storeInstallations)
                {
                    using (var db = new NovaDBContext())
                    {

                        try
                        {
                            if (installationsForStore.baseImage != "")
                            {
                                //we need to do something
                            }
                            var result = db.InstallationScheduleCurrent.SingleOrDefault(s => s.InstallationScheduleCurrentID == installationsForStore.InstallationScheduleCurrentID);
                            result.CampaignSpecialInstructionsRead = installationsForStore.CampaignSpecialInstructionsRead;
                            result.CampaignPictureTaken = installationsForStore.CampaignPictureTaken;
                            //result.LastModified = System.DateTime.Now;
                            result.LastModifiedBy = installationsForStore.TeamId;

                            if (result.OpenedDate == null || result.OpenedDate.Year == 1)
                            {
                                result.OpenedDate = System.DateTime.Now;
                            }

                            if (installationsForStore.SelectedIRCode != null && installationsForStore.SelectedIRCode.IRCodeID != 0 && installationsForStore.SelectedIRCode.IRCodeName.ToLower() != "not selected")
                            {
                                result.CampaignIRCodeSelected = true;
                                result.SelectedIRCode = installationsForStore.SelectedIRCode;
                                result.IRCodeComment = installationsForStore.IRCodeComment;
                            }
                            //result.Dormant = iRCodes.Dormant;
                            if (result.CampaignSpecialInstructionsRead && result.CampaignIRCodeSelected && installationsForStore.CampaignFinished)
                            {
                                result.CampaignFinished = true;
                                result.FinishedDate = System.DateTime.Now;
                            }

                            foreach (MasterItemWithBarcodes masterItemWithBarcodes in installationsForStore.MasterItemWithBarcodes)
                            {
                                if (masterItemWithBarcodes.scannedBarcodes.Count > 0)
                                {
                                    try
                                    {
                                        foreach (BarcodeWithId barcodeWithId in masterItemWithBarcodes.scannedBarcodes)
                                        {

                                            MasterItemInventoryItem resultMasterItemInventoryItem = new MasterItemInventoryItem();

                                            InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();
                                            if (barcodeWithId.installationActionId == "")
                                            {
                                                resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Id == barcodeWithId.id);
                                            }
                                            else
                                            {
                                                barcodeWithId.InstallationTeamId = db.InstallationScheduleCurrent.Where(x => x.InstallationScheduleCurrentID == new Guid(barcodeWithId.installationActionId)).Select(x => x.Installationteamid).FirstOrDefault();
                                                resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Barcode == barcodeWithId.Barcode);
                                            }

                                            //var resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Id == barcodeWithId.id);

                                            //InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();


                                            inventoryItemTransactions.Barcode = resultMasterItemInventoryItem.Barcode;
                                            inventoryItemTransactions.CreatedBy = email;

                                            inventoryItemTransactions.MasterItemId = resultMasterItemInventoryItem.MasterItemId;
                                            inventoryItemTransactions.MasterItemInvenoryItemId = resultMasterItemInventoryItem.Id;

                                            resultMasterItemInventoryItem.ContractNumber = installationsForStore.ContractNumber;
                                            if (installationsForStore.status.ToLower() == "remove")
                                            {
                                                resultMasterItemInventoryItem.StoreId = null;
                                                resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.TeamId;
                                                resultMasterItemInventoryItem.WarehouseId = null;

                                                inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                                inventoryItemTransactions.ToVanId = installationsForStore.TeamId;
                                            }
                                            else if (installationsForStore.status.ToLower() == "install")
                                            {
                                                resultMasterItemInventoryItem.InstallationTeamId = null;
                                                resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                                resultMasterItemInventoryItem.WarehouseId = null;

                                                inventoryItemTransactions.FromVanId = installationsForStore.TeamId;
                                                inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                            }
                                            else if (installationsForStore.status.ToLower() == "running")
                                            {
                                                resultMasterItemInventoryItem.InstallationTeamId = null;
                                                resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                                resultMasterItemInventoryItem.WarehouseId = null;

                                                if (resultMasterItemInventoryItem.InstallationTeamId != null)
                                                {
                                                    inventoryItemTransactions.FromVanId = resultMasterItemInventoryItem.InstallationTeamId;
                                                    inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                                }
                                                else
                                                {
                                                    inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                                    inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                                }

                                            }
                                            else
                                            {
                                                resultMasterItemInventoryItem.StoreId = null;
                                                resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.StoreId;
                                                resultMasterItemInventoryItem.WarehouseId = null;
                                            }


                                            db.InventoryItemTransactions.Add(inventoryItemTransactions);
                                        }
                                    }
                                    catch
                                    {

                                    }

                                    //we need to update the inventory items

                                }
                            }

                            foreach (MasterItemGroupWithBarcodes masterItemWithBarcodes in installationsForStore.MasterItemGroupWithBarcodes)
                            {
                                if (masterItemWithBarcodes.scannedBarcodes.Count > 0)
                                {
                                    try
                                    {
                                        foreach (BarcodeWithId barcodeWithId in masterItemWithBarcodes.scannedBarcodes)
                                        {

                                            MasterItemInventoryItem resultMasterItemInventoryItem = new MasterItemInventoryItem();

                                            InventoryItemTransactions inventoryItemTransactions = new InventoryItemTransactions();
                                            if (barcodeWithId.installationActionId == "")
                                            {
                                                resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Id == barcodeWithId.id);
                                            }
                                            else
                                            {
                                                barcodeWithId.InstallationTeamId = db.InstallationScheduleCurrent.Where(x => x.InstallationScheduleCurrentID == new Guid(barcodeWithId.installationActionId)).Select(x => x.Installationteamid).FirstOrDefault();
                                                resultMasterItemInventoryItem = db.MasterItemInventoryItems.SingleOrDefault(s => s.Barcode == barcodeWithId.Barcode);
                                            }

                                            if (resultMasterItemInventoryItem != null)
                                            {

                                            }
                                            inventoryItemTransactions.Barcode = resultMasterItemInventoryItem.Barcode;
                                            inventoryItemTransactions.CreatedBy = email;

                                            inventoryItemTransactions.MasterItemId = resultMasterItemInventoryItem.MasterItemId;
                                            inventoryItemTransactions.MasterItemInvenoryItemId = resultMasterItemInventoryItem.Id;


                                            resultMasterItemInventoryItem.ContractNumber = installationsForStore.ContractNumber;
                                            if (installationsForStore.status.ToLower() == "remove")
                                            {
                                                resultMasterItemInventoryItem.StoreId = null;
                                                resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.TeamId;
                                                resultMasterItemInventoryItem.WarehouseId = null;

                                                inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                                inventoryItemTransactions.ToVanId = installationsForStore.TeamId;
                                            }
                                            else if (installationsForStore.status.ToLower() == "install")
                                            {
                                                resultMasterItemInventoryItem.InstallationTeamId = null;
                                                resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                                resultMasterItemInventoryItem.WarehouseId = null;

                                                inventoryItemTransactions.FromVanId = installationsForStore.TeamId;
                                                inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                            }
                                            else if (installationsForStore.status.ToLower() == "running")
                                            {
                                                resultMasterItemInventoryItem.InstallationTeamId = null;
                                                resultMasterItemInventoryItem.StoreId = installationsForStore.StoreId;
                                                resultMasterItemInventoryItem.WarehouseId = null;
                                                if (resultMasterItemInventoryItem.InstallationTeamId != null)
                                                {
                                                    inventoryItemTransactions.FromVanId = resultMasterItemInventoryItem.InstallationTeamId;
                                                    inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                                }
                                                else
                                                {
                                                    inventoryItemTransactions.FromStoreId = installationsForStore.StoreId;
                                                    inventoryItemTransactions.ToStoreId = installationsForStore.StoreId;
                                                }
                                            }
                                            else
                                            {
                                                resultMasterItemInventoryItem.StoreId = null;
                                                resultMasterItemInventoryItem.InstallationTeamId = installationsForStore.StoreId;
                                                resultMasterItemInventoryItem.WarehouseId = null;
                                            }


                                            db.InventoryItemTransactions.Add(inventoryItemTransactions);

                                            //inventoryItemTransactions = resultMasterItemInventoryItem.;
                                            //inventoryItemTransactions.CreatedBy = resultMasterItemInventoryItem.CreatedBy;
                                            //inventoryItemTransactions.

                                        }
                                    }
                                    catch
                                    {

                                    }

                                    //we need to update the inventory items

                                }
                            }

                            foreach (InstallationScheduleQuestionsAndAnswers installationScheduleQuestionsAndAnswers in installationsForStore.InstallationScheduleQuestionsAndAnswers)
                            {
                                //ensure we either add or update
                                //check if it exists
                                var value = db.InstallationScheduleQuestionsAndAnswers.Where(x => x.InstallationActionId == installationScheduleQuestionsAndAnswers.InstallationActionId && x.QuestionId == installationScheduleQuestionsAndAnswers.QuestionId).FirstOrDefault();
                                if (value != null)
                                {
                                    value.AnswerId = installationScheduleQuestionsAndAnswers.AnswerId;
                                    value.Comment = installationScheduleQuestionsAndAnswers.Comment;
                                    db.InstallationScheduleQuestionsAndAnswers.Update(value);
                                }
                                else
                                {
                                    db.InstallationScheduleQuestionsAndAnswers.Add(installationScheduleQuestionsAndAnswers);
                                }
                            }

                        }
                        catch
                        {

                        }



                        db.SaveChanges();
                    }


                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        [Route("UpdateStoreVisitFull")]
        [HttpPost]
        public async Task<object> UpdateStoreVisitFull(StoresWithInstallations store)
        {
            try
            {
                foreach (InstallationsForStore installationsForStore in store.storeInstallations)
                {
                    using (var db = new NovaDBContext())
                    {
                        //here is where we need to update.
                        var result = db.InstallationScheduleCurrent.SingleOrDefault(s => s.InstallationScheduleCurrentID == installationsForStore.InstallationScheduleCurrentID && s.CampaignFinished != true);

                        if (result != null)
                        {
                            result.LastModifiedBy = installationsForStore.TeamId;

                            if (result.OpenedDate == null || result.OpenedDate.Year == 1)
                            {
                                result.OpenedDate = System.DateTime.Now;
                            }

                            if (installationsForStore.SelectedIRCode != null && installationsForStore.SelectedIRCode.IRCodeID != 0)
                            {
                                result.CampaignIRCodeSelected = true;
                                result.SelectedIRCode = installationsForStore.SelectedIRCode;
                                result.IRCodeComment = installationsForStore.IRCodeComment;
                            }
                            result.CampaignFinished = installationsForStore.CampaignFinished;
                            result.FinishedDate = System.DateTime.Now;
                            db.SaveChanges();
                        }




                    }



                }
                return StatusCode(200);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }

        }

        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser")]
        [Route("GetIRCodes")]
        [HttpGet]
        public async Task<object> GetIRCodes(bool isStoreSkipVisit = false)
        {
            try
            {
                //This is for testing
                //throw new Exception();

                List<IRCodes> _list = new List<IRCodes>();
                var _irc = IRC.GetInstance();
                _list = _irc.getList();
                if (_list.Count == 0)
                {
                    var context = new NovaDBContext();
                    _list = context.IRCodes.ToList();
                    _irc.setList(_list);
                }

              
                    if (isStoreSkipVisit)
                    {
                        var result =  _list.Where(x => x.isStoreSkip).OrderBy(x => x.IrCodeSortOrder).ThenBy(x => x.IRCodeID).ToList();
                        return result;
                    }
                    else
                    {
                        var result =  _list.Where(x => x.Dormant != true).OrderBy(x => x.IrCodeSortOrder).ThenBy(x => x.IRCodeID).ToList();
                        return result;
                    }

                
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }



        [Route("GetQuestionsAndAnswers")]
        [HttpGet]
        public async Task<object> GetQuestionsAndAnswers()
        {
            using (var db = new NovaDBContext())
            {
                var result = db.QuestionsAndAnswers.Include(x => x.Questions).Include(x => x.Answers).ToList();
                return result;
            }

        }
    }


    public class StoresWithInstallations
    {
        public int storeId { get; set; }
        public string storeName { get; set; }

        public string storeInstallDay { get; set; }
        public string region { get; set; }
        public string forDate { get; set; }

        public string chainName { get; set; }

        public int teamId { get; set; }

        public bool storeVisitCompleted { get; set; }
        public IEnumerable<InstallationsForStore> storeInstallations { get; set; }

        public StoresWithInstallations()
        {

        }
    }

    public class InstallationsForStore
    {
        public string dayOfCommencementDate { get; set; }
        public string dayOfTerminationDate { get; set; }
        public string jobNumber { get; set; }
        public string product { get; set; }
        public string mediaType { get; set; }
        public string client { get; set; }
        public string categoryName { get; set; }
        public string status { get; set; }
        public int qtyToInstall { get; set; }

        //public string openedDate { get; set; }
        //public string finishedDate { get; set; }

        public string installationInstructions { get; set; }

        public Guid InstallationScheduleCurrentID { get; set; }
        public bool CampaignFinished { get; set; }
        public bool CampaignIRCodeSelected { get; set; }
        public string CampaignPicturePath { get; set; }
        public bool CampaignPictureTaken { get; set; }
        public bool CampaignSpecialInstructionsRead { get; set; }


        public string Chain { get; set; }
        public string Group { get; set; }
        public string Store { get; set; }
        public string Region { get; set; }
        public string CreatedBy { get; set; }

        public IRCodes SelectedIRCode { get; set; }

        public IRCodes PreviousIRCode { get; set; }

        public List<MasterItem> MasterItem { get; set; }

        public Media Media { get; set; }

        public List<MasterItemWithBarcodes> MasterItemWithBarcodes { get; set; }

        public List<MasterItemGroupWithBarcodes> MasterItemGroupWithBarcodes { get; set; }

        public List<MasterItemGroups> MasterItemGroups { get; set; }

        public List<MediaCapexOpex> MediaCapexOpices { get; set; }

        public List<MasterItemInventoryItem> masterItemInventoryItems { get; set; }

        public string IRCodeComment { get; set; }

        public string PreviousIRCodeComment { get; set; }

        public int StoreId { get; set; }

        public int TeamId { get; set; }

        public string ContractNumber { get; set; }

        public string baseImage { get; set; }

        public List<BarcodeWithId> barcodesToRemove { get; set; }

        public List<Questions> Questions { get; set; }

        public ICollection<InstallationScheduleQuestionsAndAnswers> InstallationScheduleQuestionsAndAnswers { get; set; }
    }

    class StoreNameDay
    {
        public string StoreName { get; set; }
        public string InstallDay { get; set; }
    }

    public class MasterItemWithBarcodes
    {
        public int amountRequired { get; set; }
        public string MasterItemName { get; set; }
        public int MasterItemId { get; set; }

        public List<BarcodeWithId> barcodes { get; set; }

        public List<BarcodeWithId> scannedBarcodes { get; set; }

    }

    public class MasterItemGroupWithBarcodes
    {
        public int amountRequired { get; set; }
        public string MasterItemGroupName { get; set; }
        public int MasterItemGroupId { get; set; }

        public List<BarcodeWithId> barcodes { get; set; }

        public List<BarcodeWithId> scannedBarcodes { get; set; }

    }

    public class BarcodeWithId
    {
        public int id { get; set; }
        public string Barcode { get; set; }

        public int? InstallationTeamId { get; set; }

        public int? StoreId { get; set; }

        public string installationActionId { get; set; }
    }
}
