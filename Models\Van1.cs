﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Van1
    {
        public Van1()
        {
            DistributionChecks = new HashSet<DistributionCheck>();
            Purchases = new HashSet<Purchase>();
            Sales = new HashSet<Sale>();
        }

        public int VanId { get; set; }
        public int DistributionRegionId { get; set; }
        public string VanName { get; set; }
        public string DefaultOperatorName { get; set; }

        public virtual DistributionRegion DistributionRegion { get; set; }
        public virtual ICollection<DistributionCheck> DistributionChecks { get; set; }
        public virtual ICollection<Purchase> Purchases { get; set; }
        public virtual ICollection<Sale> Sales { get; set; }
    }
}
