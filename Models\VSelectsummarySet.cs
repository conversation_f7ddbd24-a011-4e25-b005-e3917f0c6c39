﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VSelectsummarySet
    {
        public string ContractNumber { get; set; }
        public string SubmitTime { get; set; }
        public string SubmitTimeSpar { get; set; }
        public string SubmitTimeClicks { get; set; }
        public bool? Selected { get; set; }
        public DateTime? SubmitDate { get; set; }
        public DateTime? PcrReadyDate { get; set; }
        public DateTime? SubmitDatePnp { get; set; }
        public DateTime? SubmitDateSpar { get; set; }
        public DateTime? SubmitDateClicks { get; set; }
        public DateTime? PcrReadyDatePnp { get; set; }
        public DateTime? PcrReadyDateClicks { get; set; }
        public int? ContractCount { get; set; }
        public int? DataSets { get; set; }
        public string AccountManager { get; set; }
        public int AccountManagerId { get; set; }
        public string ScannerGroup { get; set; }
        public DateTime? PcrReadyDateSpar { get; set; }
        public int? StoreCount { get; set; }
        public int? ClicksCount { get; set; }
        public int? SparCount { get; set; }
    }
}
