﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class BrandCategoryMember
    {
        public Guid BrandId { get; set; }
        public int BrandCategoryId { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Brand Brand { get; set; }
        public virtual BrandCategory BrandCategory { get; set; }
    }
}
