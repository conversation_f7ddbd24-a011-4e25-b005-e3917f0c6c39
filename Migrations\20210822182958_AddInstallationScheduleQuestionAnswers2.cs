﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddInstallationScheduleQuestionAnswers2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "InstallationScheduleQuestionsAndAnswers",
                schema: "Ops",
                columns: table => new
                {
                    InstallationScheduleQuestionsAndAnswersId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    InstallationActionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AnswerId = table.Column<int>(type: "int", nullable: true),
                    QuestionId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InstallationScheduleQuestionsAndAnswers", x => x.InstallationScheduleQuestionsAndAnswersId);
                    table.ForeignKey(
                        name: "FK_InstallationScheduleQuestionsAndAnswers_InstallationScheduleCurrent_InstallationActionId",
                        column: x => x.InstallationActionId,
                        principalSchema: "Ops",
                        principalTable: "InstallationScheduleCurrent",
                        principalColumn: "InstallationScheduleCurrentID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InstallationScheduleQuestionsAndAnswers_InstallationActionId",
                schema: "Ops",
                table: "InstallationScheduleQuestionsAndAnswers",
                column: "InstallationActionId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InstallationScheduleQuestionsAndAnswers",
                schema: "Ops");
        }
    }
}
