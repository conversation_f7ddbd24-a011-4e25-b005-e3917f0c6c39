﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MasterItem
    {
        public MasterItem()
        {
            InventoryItemMovements = new HashSet<InventoryItemMovement>();
            MasterItemGroupMembers = new HashSet<MasterItemGroupMember>();
            MasterItemInventoryItem = new HashSet<MasterItemInventoryItem>();
            MediaCapexOpices = new HashSet<MediaCapexOpex>();
        }

        public int Id { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public int? CategoryId { get; set; }
        public bool? Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string DeletedBy { get; set; }
        public DateTime? DeletionDate { get; set; }
        public int? MasterItemTypeId { get; set; }
        public string MasterItemName { get; set; }

        public virtual Category Category { get; set; }
        public virtual MasterItemType MasterItemType { get; set; }
        public virtual ICollection<InventoryItemMovement> InventoryItemMovements { get; set; }
        public virtual ICollection<MasterItemGroupMember> MasterItemGroupMembers { get; set; }
        public virtual ICollection<MasterItemInventoryItem> MasterItemInventoryItem { get; set; }
        public virtual ICollection<MediaCapexOpex> MediaCapexOpices { get; set; }
    }
}
