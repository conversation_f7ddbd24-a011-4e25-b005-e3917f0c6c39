﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class MasterItemInventoryItem
    {
        public MasterItemInventoryItem()
        {
            InventoryItemMovements = new HashSet<InventoryItemMovement>();
        }

        public int Id { get; set; }
        public string Barcode { get; set; }
        public int? StoreId { get; set; }
        public int? InstallationTeamId { get; set; }
        public int? MasterItemId { get; set; }
        public bool Dormant { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public string DeletedBy { get; set; }
        public DateTime? DeletionDate { get; set; }
        public int? WarehouseId { get; set; }
        public int? ShelfId { get; set; }
        public string ContractNumber { get; set; }

        public virtual InstallationTeam InstallationTeam { get; set; }
        public virtual MasterItem MasterItem { get; set; }
        public virtual Shelf Shelf { get; set; }
        public virtual Store Store { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        public virtual ICollection<InventoryItemMovement> InventoryItemMovements { get; set; }

        public virtual ICollection<InventoryItemTransactions> InventoryItemTransactions { get; set; }


        public DateTime? ScannedDate { get; set; }

        public string categoryName { get; set; }
    }
}
