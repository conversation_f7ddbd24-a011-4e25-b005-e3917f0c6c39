﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Purchase
    {
        public Purchase()
        {
            PurchaseDetails = new HashSet<PurchaseDetail>();
        }

        public Guid PurchaseId { get; set; }
        public int VanId { get; set; }
        public string WholesalerName { get; set; }
        public DateTime PurchaseDate { get; set; }
        public string OperatorName { get; set; }
        public string InvoiceNumber { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual Van1 Van { get; set; }
        public virtual ICollection<PurchaseDetail> PurchaseDetails { get; set; }
    }
}
