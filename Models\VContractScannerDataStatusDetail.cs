﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VContractScannerDataStatusDetail
    {
        public string Number { get; set; }
        public DateTime? PcrReadyDate { get; set; }
        public DateTime? PcrReadyDateSpar { get; set; }
        public DateTime? PcrReadyDateClicks { get; set; }
        public int? Barcodecount { get; set; }
        public DateTime? SubmitDate { get; set; }
        public string SubmitTime { get; set; }
        public DateTime? SubmitDateSpar { get; set; }
        public string SubmitTimeSpar { get; set; }
        public DateTime? SubmitDateClicks { get; set; }
        public string SubmitTimeClicks { get; set; }
        public bool? Selected { get; set; }
        public Guid? RequestId { get; set; }
    }
}
