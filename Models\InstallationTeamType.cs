﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationTeamType
    {
        public InstallationTeamType()
        {
            InstallationTeams = new HashSet<InstallationTeam>();
        }

        public int InstallationTeamTypeId { get; set; }
        public string TypeName { get; set; }

        public virtual ICollection<InstallationTeam> InstallationTeams { get; set; }
    }
}
