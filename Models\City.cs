﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class City
    {
        public City()
        {
            Clients = new HashSet<Client>();
            StoreBillingAddressCities = new HashSet<Store>();
            StorePhysicalAddressCities = new HashSet<Store>();
        }

        public int CityId { get; set; }
        public string CityName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<Client> Clients { get; set; }
        public virtual ICollection<Store> StoreBillingAddressCities { get; set; }
        public virtual ICollection<Store> StorePhysicalAddressCities { get; set; }
    }
}
