﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PhoenixAPI.Models
{
    public class ApplicationUser : IdentityUser
    {


        public string FirstName { get; set; }
        public string LastName { get; set; }

        public bool isEnabled { get; set; }
    }

    public class UserLists
    {
        public ApplicationUser user { get; set; }
        public List<string> userRoles { get; set; }
    }
}
