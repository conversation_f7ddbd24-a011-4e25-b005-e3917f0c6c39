﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class IRCodeSortOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "IrCodeSortOrder",
                schema: "Ops",
                table: "IRCodes",
                type: "int",
                nullable: false,
                defaultValue: 999);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IrCodeSortOrder",
                schema: "Ops",
                table: "IRCodes");
        }
    }
}
