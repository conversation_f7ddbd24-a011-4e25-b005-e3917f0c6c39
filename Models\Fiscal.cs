﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Fiscal
    {
        public Fiscal()
        {
            AccountManagerBudgets = new HashSet<AccountManagerBudget>();
        }

        public int FiscalId { get; set; }
        public DateTime FiscalStartDate { get; set; }
        public string FiscalName { get; set; }

        public virtual ICollection<AccountManagerBudget> AccountManagerBudgets { get; set; }
    }
}
