﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PhoenixAPI.Models
{
   
    public partial class InvetoryItemsWithMovement
    {
       public int id { get; set; }
        public string MasterItemName { get; set; }
        public string warehouseName { get; set; }

        public string InstallationTeamName { get; set; }

        public string storeName { get; set; }
        public string masterItemTypeName { get; set; }
        public int qtyOnHand { get; set; }
        public int qtyBeingMoved { get; set; }
        
       public int qtyAtWarehouse { get; set; }

     public string SourceLocation { get; set; }
    }
}
