﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VEffectiveProvisionalBooking
    {
        public Guid ProvisionalBookingId { get; set; }
        public string ProvisionalBookingName { get; set; }
        public int ChainId { get; set; }
        public int CategoryId { get; set; }
        public int MediaFamilyId { get; set; }
        public Guid BrandId { get; set; }
        public string BrandName { get; set; }
        public DateTime FirstWeek { get; set; }
        public DateTime? LastWeek { get; set; }
        public int Weeks { get; set; }
        public string CreatedBy { get; set; }
        public DateTime BookTime { get; set; }
        public DateTime ExpiryTime { get; set; }
    }
}
