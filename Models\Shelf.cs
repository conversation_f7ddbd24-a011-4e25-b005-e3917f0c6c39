﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Shelf
    {
        public Shelf()
        {
            MasterItemInventoryItems = new HashSet<MasterItemInventoryItem>();
        }

        public int ShelfId { get; set; }
        public string ShelfCode { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public int? WarehouseId { get; set; }
        public string ShelfColumn { get; set; }
        public string ShelfName { get; set; }
        public string ShelfRow { get; set; }

        public virtual Warehouse Warehouse { get; set; }
        public virtual ICollection<MasterItemInventoryItem> MasterItemInventoryItems { get; set; }
    }
}
