﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class addTransactionTypes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "InvetoryItemTransactionTypeId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "InventoryItemTransactionType",
                schema: "Ops",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TransactionType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValueSql: "(suser_sname())"),
                    CreationDate = table.Column<DateTime>(type: "datetime", nullable: false, defaultValueSql: "(getdate())"),
                    DeletedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DeletionDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryItemTransactionType", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InventoryItemTransactions_InvetoryItemTransactionTypeId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "InvetoryItemTransactionTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_InventoryItemTransactions_InventoryItemTransactionType_InvetoryItemTransactionTypeId",
                schema: "Ops",
                table: "InventoryItemTransactions",
                column: "InvetoryItemTransactionTypeId",
                principalSchema: "Ops",
                principalTable: "InventoryItemTransactionType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InventoryItemTransactions_InventoryItemTransactionType_InvetoryItemTransactionTypeId",
                schema: "Ops",
                table: "InventoryItemTransactions");

            migrationBuilder.DropTable(
                name: "InventoryItemTransactionType",
                schema: "Ops");

            migrationBuilder.DropIndex(
                name: "IX_InventoryItemTransactions_InvetoryItemTransactionTypeId",
                schema: "Ops",
                table: "InventoryItemTransactions");

            migrationBuilder.DropColumn(
                name: "InvetoryItemTransactionTypeId",
                schema: "Ops",
                table: "InventoryItemTransactions");
        }
    }
}
