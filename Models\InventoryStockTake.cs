﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InventoryStockTake
    {

        [Key]
        [Column("InventoryStockTakeId")]
        public int InventoryStockTakeId { get; set; }

        public string StockTakeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime CreationDate { get; set; }
        public string CreatedBy { get; set; }
        public bool isCompleted { get; set; }

        public virtual List<InventoryStockTakeDetails> InventoryStockTakeDetails { get; set; }
    }
}
