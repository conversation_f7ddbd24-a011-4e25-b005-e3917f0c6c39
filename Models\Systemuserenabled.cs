﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Systemuserenabled
    {
        public Guid Userid { get; set; }
        public DateTime Datemodified { get; set; }
        public bool? Enabled { get; set; }
        public Guid Modifiedbyuserid { get; set; }

        public virtual Systemuser Modifiedbyuser { get; set; }
        public virtual Systemuser User { get; set; }
    }
}
