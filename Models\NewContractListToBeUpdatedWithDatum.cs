﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class NewContractListToBeUpdatedWithDatum
    {
        public string AccountManagerName { get; set; }
        public string ContractNumber { get; set; }
        public string AccountManagerInitialsCode { get; set; }
        public string ClientName { get; set; }
        public string ChainName { get; set; }
        public string BrandName { get; set; }
        public string MediaType { get; set; }
        public int? CampaignTotalRunningWeeks { get; set; }
        public DateTime CampaignFirstWeek { get; set; }
        public DateTime CampaignLastWeek { get; set; }
        public DateTime? ContractTerminationDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public bool Signed { get; set; }
        public string SignedBy { get; set; }
        public DateTime? SignDate { get; set; }
        public Guid ContractId { get; set; }
        public int? PnpNumberOfStoresSelected { get; set; }
        public int? SparNumberOfStoresSelected { get; set; }
        public int? ClicksNumberOfStoresSelected { get; set; }
        public bool IsSelectedForScannerData { get; set; }
        public DateTime? SelectedForScannerDataDate { get; set; }
        public bool PnpIsDataReadyForPrc { get; set; }
        public bool SparIsDataReadyForPrc { get; set; }
        public bool ClicksIsDataReadyForPrc { get; set; }
        public DateTime? PnpReadyDate { get; set; }
        public DateTime? ClicksReadyDate { get; set; }
        public DateTime? SparReadyDate { get; set; }
        public bool PnpIsDataSubmittedForPrc { get; set; }
        public DateTime? PnpSubmissionDate { get; set; }
        public bool ClicksIsDataSubmittedForPrc { get; set; }
        public DateTime? ClicksSubmissionDate { get; set; }
        public bool SparIsDataSubmittedForPrc { get; set; }
        public DateTime? SparSubmissionDate { get; set; }
        public int Barcodecount { get; set; }
    }
}
