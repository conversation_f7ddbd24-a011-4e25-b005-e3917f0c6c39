﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class InstallationActionsReportWithStore
    {
        public string ContractNumber { get; set; }
        public string ClientName { get; set; }
        public string BrandName { get; set; }
        public string MediaName { get; set; }
        public string Category { get; set; }
        public string Installcategory { get; set; }
        public string HomesiteCategory { get; set; }
        public bool InstallAtHomesite { get; set; }
        public string ChainName { get; set; }
        public DateTime FirstWeek { get; set; }
        public int Weeks { get; set; }
        public int? TotalToInstall { get; set; }
        public int? TotalInstalled { get; set; }
        public int? TotalNotInstalled { get; set; }
        public int TotalStores { get; set; }
        public string Mediatype { get; set; }
        public string MymobilityCategory { get; set; }
        public int? TotalUnaprovedMediaFound { get; set; }
        public string Store { get; set; }
        public string Region { get; set; }
    }
}
