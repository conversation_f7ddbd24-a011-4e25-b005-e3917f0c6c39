﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class ClicksScannerDataToolRollForwardRequest
    {
        public int Id { get; set; }
        public string ContractNumber { get; set; }
        public DateTime PromoStartDate { get; set; }
        public DateTime PromoEndDate { get; set; }
        public int PromoNumberOfWeeks { get; set; }
        public DateTime NonPromoStartDate { get; set; }
        public DateTime NonPromoEndDate { get; set; }
        public int NonPromoNumberOfWeeks { get; set; }
        public bool IsSubmittedForScannerData { get; set; }
        public bool IsDataReadyForPcr { get; set; }
        public int? DataRequestId { get; set; }
        public DateTime? SubmissionDate { get; set; }
        public DateTime? ReadyForPcrDate { get; set; }
    }
}
