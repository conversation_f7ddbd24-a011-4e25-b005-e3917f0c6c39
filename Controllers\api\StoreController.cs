﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PhoenixAPI.Models;
using PhoenixAPI.Entities;
using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Interfaces;

namespace PhoenixAPI.Controllers
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "Admin")]
    public class StoreController : ControllerBase, IStore
    {
        private readonly NovaDBContext _context = new NovaDBContext();
        //ok, here we will handle all store logic
        [Route("GetTeams")]
        [HttpGet]
        public IEnumerable<InstallationTeam> GetTeams()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                return context.InstallationTeam
                    .ToList().OrderBy(x => x.InstallationTeamName);
            }

        }



        [Route("GetInstallationDays")]
        [HttpGet]
        public IEnumerable<InstallationDays> GetInstallationDays()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {




                return context.InstallationDays
                .ToList().OrderBy(x => x.InstallationDayId);
            }

        }
        //done
        [Route("GetStore")]
        [HttpGet]
        public Store GetStore(int StoreId)
        {
            try
            {
                return _context.Store.SingleOrDefault(s => s.StoreId == StoreId);
            }
            catch (Exception ex)
            {
                return null;
            }

        }
        //done
        [Route("GetStores")]
        [HttpGet]
        public IEnumerable<Store> GetStores()
        {
            try
            {
                return _context.Store.ToList().OrderBy(x => x.StoreName);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        //done
        [Route("GetTeamStores/{teamId}")]
        [HttpGet]
        public IEnumerable<Store> GetTeamStores(int teamId)
        {
            //Querying with LINQ to Entities 
            try
            {
                var returnVal = _context.Store
                 .Include(InstallationDays => InstallationDays.InstallationDaysInstallationDay)
                 .Include(a => a.Region).ThenInclude(c => c.Chain)
                 .Where(x => x.InstallationTeamId == teamId)
                 .ToList().OrderBy(x => x.StoreName);
                return returnVal;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        //done to be tested
        public Store Add(Store store)
        {
            _context.Store.Add(store);
            _context.SaveChanges();
            return store;

        }
        [Route("UpdateStore")]
        [HttpPost]
        public Store Update(Store store)
        {
            using (var db = new NovaDBContext())
            {
                var result = db.Store.SingleOrDefault(s => s.StoreId == store.StoreId);
                if (result != null)
                {
                    result.InstallationDaysInstallationDay = store.InstallationDaysInstallationDay;
                    db.SaveChanges();
                    return result;
                }
                else
                {
                    return null;
                }
            }
        }

        public Store Delete(Store store)
        {
            throw new NotImplementedException();
        }
    }
}
