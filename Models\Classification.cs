﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Classification
    {
        public Classification()
        {
            Clients = new HashSet<Client>();
        }

        public int ClassificationId { get; set; }
        public string ClassificationName { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }

        public virtual ICollection<Client> Clients { get; set; }
    }
}
